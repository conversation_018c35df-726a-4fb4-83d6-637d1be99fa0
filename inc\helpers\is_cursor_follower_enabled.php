<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_is_cursor_follower_enabled' ) ) {
	/**
	 * Check if the cursor follower is enabled.
	 *
	 * Retrieves the cursor follower setting from the kit settings.
	 * Allows overriding the setting via the 'cursor' query parameter.
	 *
	 * @return bool True if the cursor follower is enabled, false otherwise.
	 */
	function arts_is_cursor_follower_enabled() {
		$cursor_follower_enabled = Utilities::get_kit_settings( 'cursor_follower_enabled', false );

		if ( isset( $_GET['cursor'] ) ) {
			$force_cursor_follower_enabled = preg_replace( '/[^-a-zA-Z0-9_]/', '', $_GET['cursor'] );

			if ( $force_cursor_follower_enabled === 'yes' ) {
				return true;
			} elseif ( $force_cursor_follower_enabled === 'no' ) {
				return false;
			}
		}

		return $cursor_follower_enabled;
	}
}
