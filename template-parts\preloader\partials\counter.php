<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$defaults = array();
$args     = wp_parse_args( $args, $defaults );

$container_attributes = array(
	'class' => array(
		'preloader__wrapper-counter',
		'preloader__lcp-visible',
		'js-preloader__wrapper-counter',
	),
);

$counter_end_number = '100';

$circle_args = array(
	'size'   => 600,
	'stroke' => 3,
	'inner'  => array(
		'class' => array( 'js-preloader__circle', 'preloader__lcp-visible' ),
	),
);

$counter_attributes = Utilities::get_component_attributes(
	array(
		'class' => array( 'preloader__lcp-visible' ),
	),
	array(
		'name'         => 'SplitCounter',
		'hasAnimation' => 'true',
	)
);

?>

<?php if ( $args['counterEnabled'] ) : ?>
	<!-- Counter -->
	<div <?php Utilities::print_attributes( $container_attributes ); ?>>
		<div class="preloader__counter preloader__lcp-visible">
			<div <?php Utilities::print_attributes( $counter_attributes ); ?>>
				<?php for ( $i = 0; $i < 2; $i++ ) : ?>
					<div class="split-counter__lane preloader__lcp-visible js-split-counter__lane">
						<?php for ( $j = 0; $j < 10; $j++ ) : ?>
							<?php
								// $translateY = $j === 0 ? '0%' : '103%';
								$number_attributes = array(
									'class'       => array(),
									'data-number' => esc_attr( $j ),
								);

								if ( $j === 0 ) {
									$number_attributes['class'][] = 'preloader__lcp-visible';
									$number_attributes['style']   = 'translate: none; rotate: none; scale: none; transform: translate(0px, 0%);';
								} else {
									$number_attributes['style'] = 'translate: none; rotate: none; scale: none; transform: translate(0px, 103%); position: absolute;';
								}
								?>
							<span <?php Utilities::print_attributes( $number_attributes ); ?>><?php echo esc_html( $j ); ?></span>
						<?php endfor; ?>
					</div>
				<?php endfor; ?>
			</div>
			<!-- Last counter number -->
			<div class="preloader__wrapper-counter-end">
				<div class="js-preloader__counter-end"><?php echo esc_html( $counter_end_number ); ?></div>
			</div>
			<!-- - Last counter number -->
		</div>
		<?php get_template_part( 'template-parts/svg/circle', '', $circle_args ); ?>
	</div>
	<!-- - Counter -->
<?php endif; ?>
