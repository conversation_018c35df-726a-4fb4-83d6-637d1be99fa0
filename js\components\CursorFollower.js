const _0x1806f2=_0x3360;(function(_0x50f015,_0x202ab3){const _0x30f54d=_0x3360,_0x16bae1=_0x50f015();while(!![]){try{const _0x4b634d=parseInt(_0x30f54d(0x84))/0x1*(parseInt(_0x30f54d(0x77))/0x2)+-parseInt(_0x30f54d(0x74))/0x3+-parseInt(_0x30f54d(0x78))/0x4+-parseInt(_0x30f54d(0x6d))/0x5*(parseInt(_0x30f54d(0x7d))/0x6)+parseInt(_0x30f54d(0x81))/0x7+-parseInt(_0x30f54d(0x86))/0x8+parseInt(_0x30f54d(0x72))/0x9*(parseInt(_0x30f54d(0x6f))/0xa);if(_0x4b634d===_0x202ab3)break;else _0x16bae1['push'](_0x16bae1['shift']());}catch(_0x19ab5a){_0x16bae1['push'](_0x16bae1['shift']());}}}(_0x2953,0x6de06));function _0x3360(_0x2d336a,_0x1029ad){const _0x29537d=_0x2953();return _0x3360=function(_0x33601b,_0x16ae5e){_0x33601b=_0x33601b-0x68;let _0x45b3ab=_0x29537d[_0x33601b];return _0x45b3ab;},_0x3360(_0x2d336a,_0x1029ad);}function _0x2953(){const _0x90b83f=['addEventListener','push','transitionStart','_createCursorFollower','_detachEvents','_onTransitionStart','_attachEvents','155pEhlKv','element','110vVYgKw','dataReady','update','1153017PWtRws','transitionEnd','390009fgLvJZ','destroy','_handlers','629780JQUZvz','2090776vkwKVB','postTask','options','ready','all','117366VRMLsv','setup','arts/barba/transition/start','instance','3235386OfDWkx','bind','arts/barba/transition/end/before','2gknggn','reset','6336632oznmrf','removeEventListener','setLoading','_onTransitionEnd'];_0x2953=function(){return _0x90b83f;};return _0x2953();}export default class CursorFollower extends BaseComponent{constructor({name:_0x2a6c82,loadInnerComponents:_0x216cd4,loadAfterSyncStyles:_0x19a54d,parent:_0x23ce18,element:_0x4f37ae,options:_0x3a61c7}){const _0x12d907=_0x3360;super({'name':_0x2a6c82,'loadInnerComponents':_0x216cd4,'loadAfterSyncStyles':_0x19a54d,'parent':_0x23ce18,'element':_0x4f37ae,'defaults':_0x3a61c7,'innerElements':{}}),this[_0x12d907(0x76)]={'transitionStart':this[_0x12d907(0x6b)]['bind'](this),'transitionEnd':this[_0x12d907(0x89)][_0x12d907(0x82)](this)},this[_0x12d907(0x70)]['finally'](()=>{const _0x6e7a=_0x12d907;this[_0x6e7a(0x7e)]();});}['init'](){return new Promise(_0x5da551=>{const _0x334f10=_0x3360;this[_0x334f10(0x69)](),this[_0x334f10(0x80)]?this['instance'][_0x334f10(0x7b)]['finally'](()=>{const _0x453718=_0x334f10;this[_0x453718(0x6c)](),_0x5da551(!![]);}):_0x5da551(!![]);});}[_0x1806f2(0x75)](){return new Promise(_0x217e37=>{const _0x5d45b0=_0x3360,_0x235e45=[];this['_detachEvents']();if(this[_0x5d45b0(0x80)]&&typeof this[_0x5d45b0(0x80)][_0x5d45b0(0x75)]==='function'){const _0x2b18f3=scheduler[_0x5d45b0(0x79)](()=>{const _0x2d7bad=_0x5d45b0;this['instance'][_0x2d7bad(0x75)]();});_0x235e45[_0x5d45b0(0x8b)](_0x2b18f3);}Promise[_0x5d45b0(0x7c)](_0x235e45)['finally'](()=>_0x217e37(!![]));});}[_0x1806f2(0x6c)](){const _0x58f43b=_0x1806f2;document[_0x58f43b(0x8a)](_0x58f43b(0x7f),this['_handlers'][_0x58f43b(0x68)]),document[_0x58f43b(0x8a)]('arts/barba/transition/end/before',this[_0x58f43b(0x76)][_0x58f43b(0x73)]);}[_0x1806f2(0x6a)](){const _0x16d24c=_0x1806f2;document[_0x16d24c(0x87)]('arts/barba/transition/start',this[_0x16d24c(0x76)][_0x16d24c(0x68)]),document[_0x16d24c(0x87)](_0x16d24c(0x83),this[_0x16d24c(0x76)][_0x16d24c(0x73)]);}[_0x1806f2(0x71)](){const _0x40763b=_0x1806f2;this[_0x40763b(0x80)][_0x40763b(0x71)]();}['_createCursorFollower'](){const _0x2e030b=_0x1806f2;this[_0x2e030b(0x80)]=new ArtsCursorFollower(this[_0x2e030b(0x6e)],this[_0x2e030b(0x7a)]);}['_onTransitionStart'](){const _0x460ab8=_0x1806f2;this[_0x460ab8(0x88)](!![]);}[_0x1806f2(0x89)](){const _0x4024d8=_0x1806f2;this[_0x4024d8(0x88)](![]);}[_0x1806f2(0x88)](_0x3406a3=!![]){const _0x30f03a=_0x1806f2;this[_0x30f03a(0x80)]&&(_0x3406a3?this['instance'][_0x30f03a(0x85)]():this[_0x30f03a(0x80)][_0x30f03a(0x71)](),this[_0x30f03a(0x80)][_0x30f03a(0x88)](_0x3406a3));}}