function _0x38f1(){const _0x368ee3=['.js-screens-wall__list-lane','_updateElements','bottom','elements','4562703fADbHR','67qpCrrD','init','object','4094OmQXFi','_initMarquee','marquee','animateMask','update','2763952zFOAZc','setup','_createInfiniteList','904820xtboHN','add','finally','lanes','destroy','dataReady','innerSelectors','options','pluginsReady','114FxekoH','length','.js-screens-wall__list-item','4460QVPcRU','items','545223PJlIIs','infiniteList','function','postTask','loop','224IdzXVL','container','126301ePTguP','timeline','opacityEffect','data-arts-infinite-list-options','center','<66%'];_0x38f1=function(){return _0x368ee3;};return _0x38f1();}const _0x326393=_0x1831;function _0x1831(_0x54813d,_0xaecd21){const _0x38f149=_0x38f1();return _0x1831=function(_0x183140,_0x440aad){_0x183140=_0x183140-0x18b;let _0x49989a=_0x38f149[_0x183140];return _0x49989a;},_0x1831(_0x54813d,_0xaecd21);}(function(_0x54a39e,_0x533374){const _0x1c25d2=_0x1831,_0x556721=_0x54a39e();while(!![]){try{const _0x4f688f=parseInt(_0x1c25d2(0x196))/0x1*(parseInt(_0x1c25d2(0x199))/0x2)+parseInt(_0x1c25d2(0x1af))/0x3+parseInt(_0x1c25d2(0x1b4))/0x4*(parseInt(_0x1c25d2(0x1ad))/0x5)+-parseInt(_0x1c25d2(0x1aa))/0x6*(parseInt(_0x1c25d2(0x18b))/0x7)+-parseInt(_0x1c25d2(0x19e))/0x8+parseInt(_0x1c25d2(0x195))/0x9+parseInt(_0x1c25d2(0x1a1))/0xa;if(_0x4f688f===_0x533374)break;else _0x556721['push'](_0x556721['shift']());}catch(_0x12bbd6){_0x556721['push'](_0x556721['shift']());}}}(_0x38f1,0x43ddc));export default class ScreensWall extends BaseComponent{constructor({name:_0x12d26c,loadInnerComponents:_0xc3e812,loadAfterSyncStyles:_0x195291,parent:_0x42df93,element:_0x432de9}){const _0x14dad8=_0x1831;super({'name':_0x12d26c,'loadInnerComponents':_0xc3e812,'loadAfterSyncStyles':_0x195291,'parent':_0x42df93,'element':_0x432de9,'defaults':{'loop':!![],'autoClone':!![],'marquee':{'speed':0.5,'onHoverSpeed':0.5,'onScrollSpeed':![],'onScrollInverseDirection':![]},'opacityEffect':![]},'innerElements':{'container':'.js-screens-wall__list-container','lanes':_0x14dad8(0x191),'items':_0x14dad8(0x1ac)}}),this[_0x14dad8(0x1a6)][_0x14dad8(0x1a3)](()=>{const _0x3d1ec3=_0x14dad8;this[_0x3d1ec3(0x19f)]();});}['init'](){return new Promise(_0x9ed9fc=>{const _0x4abfb1=_0x1831;this[_0x4abfb1(0x1a0)](),this['infiniteList']?!this['_hasAnimationScene']()?this[_0x4abfb1(0x1b0)][_0x4abfb1(0x1a9)][_0x4abfb1(0x1a3)](()=>{const _0xb6e686=_0x4abfb1;this[_0xb6e686(0x19a)](),_0x9ed9fc(!![]);}):_0x9ed9fc(!![]):_0x9ed9fc(!![]);});}['destroy'](){return new Promise(_0x1e535c=>{const _0x3675cf=_0x1831;this[_0x3675cf(0x1b0)]&&typeof this[_0x3675cf(0x1b0)][_0x3675cf(0x1a5)]===_0x3675cf(0x1b1)?scheduler[_0x3675cf(0x1b2)](()=>{const _0x4071d2=_0x3675cf;this[_0x4071d2(0x1b0)]['destroy']();})[_0x3675cf(0x1a3)](()=>_0x1e535c(!![])):_0x1e535c(!![]);});}['prepareAnimation'](){return new Promise(_0x51fbb8=>{const _0x1acbcc=_0x1831;this[_0x1acbcc(0x192)]({'container':this['element'],'elements':this['innerSelectors']})['finally'](()=>_0x51fbb8(!![]));});}['getRevealAnimation'](){const _0x1c00c2=_0x1831,_0x274a6d=gsap[_0x1c00c2(0x18c)]({'paused':!![]});return this[_0x1c00c2(0x194)][_0x1c00c2(0x1ae)][_0x1c00c2(0x1ab)]&&_0x274a6d[_0x1c00c2(0x19c)](this[_0x1c00c2(0x194)][_0x1c00c2(0x1ae)],{'animateFrom':_0x1c00c2(0x193),'duration':0x2,'ease':'expo.inOut','stagger':distributeByPosition({'from':_0x1c00c2(0x18f),'amount':0.5})}),this[_0x1c00c2(0x1b0)]&&_0x274a6d[_0x1c00c2(0x1a2)](()=>{const _0x29ffde=_0x1c00c2;this[_0x29ffde(0x1b0)]['pluginsReady']['finally'](()=>{const _0x4f5a43=_0x29ffde;this[_0x4f5a43(0x19a)]();});},_0x1c00c2(0x190)),_0x274a6d;}[_0x326393(0x1a0)](){const _0x33d9d8=_0x326393;this[_0x33d9d8(0x1b0)]=new ArtsInfiniteList(this[_0x33d9d8(0x194)][_0x33d9d8(0x1b5)][0x0],{'direction':'vertical','mapWheelEventYtoX':![],'listElementsSelector':this[_0x33d9d8(0x1a7)][_0x33d9d8(0x1ae)],'multiLane':{'laneSelector':this[_0x33d9d8(0x1a7)][_0x33d9d8(0x1a4)],'laneOptionsAttribute':_0x33d9d8(0x18e)},'loop':this[_0x33d9d8(0x1a8)][_0x33d9d8(0x1b3)],'autoClone':!!this[_0x33d9d8(0x1a8)]['loop']&&this[_0x33d9d8(0x1a8)]['autoClone'],'plugins':{'marquee':typeof this['options']['marquee']===_0x33d9d8(0x198)?{'autoInit':![],...this[_0x33d9d8(0x1a8)][_0x33d9d8(0x19b)]}:![],'scroll':![],'opacityEffect':this[_0x33d9d8(0x1a8)][_0x33d9d8(0x18d)]}});}[_0x326393(0x19a)](){const _0x205887=_0x326393;this[_0x205887(0x1b0)]['plugins'][_0x205887(0x19b)]&&(this['infiniteList'][_0x205887(0x19d)](),this['infiniteList']['plugins']['marquee'][_0x205887(0x197)]());}}