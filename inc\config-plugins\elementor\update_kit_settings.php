<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$options_map = array(
	'blogname'        => 'site_name',
	'blogdescription' => 'site_description',
	'site_icon'       => 'site_favicon',
);

$theme_mods_map = array(
	'custom_logo' => 'site_logo',
);

foreach ( $options_map as $option_customizer => $option_elementor ) {
	add_action(
		"update_option_{$option_customizer}",
		function ( $old_value, $value ) use ( $option_elementor ) {
			Utilities::update_kit_settings( $option_elementor, $value );
		},
		10,
		2
	);
}

foreach ( $theme_mods_map as $theme_mod => $option_elementor ) {
	add_action(
		"pre_set_theme_mod_{$theme_mod}",
		function ( $value, $old_value ) use ( $option_elementor ) {
			Utilities::update_kit_settings( $option_elementor, $value );
		},
		10,
		2
	);
}

if ( ! function_exists( 'arts_elementor_update_kit_settings' ) ) {
	/**
	 * Update Elementor kit settings based on the given option and value.
	 *
	 * @param string $option_elementor The Elementor option to update.
	 * @param mixed  $value The value to set for the given option.
	 *
	 * @return bool True on success, false on failure.
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::update_kit_settings()` method instead.
	 */
	function arts_elementor_update_kit_settings( $option_elementor, $value ) {
		return Utilities::update_kit_settings( $option_elementor, $value );
	}
}
