<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Content Post Type: Audio
 */

$defaults = array(
	'id' => get_the_ID(),
);

$args = wp_parse_args( $args, $defaults );

$content = apply_filters( 'the_content', get_the_content( $args['id'] ) );
$audio   = false;

	// Only get audio from the content if a playlist isn't present.
if ( false === strpos( $content, 'wp-playlist-script' ) ) {
	$audio = get_media_embedded_in_content( $content, array( 'audio' ) );
}
if ( ! is_single( $args['id'] ) ) {
	// If not a single post, highlight the audio file.
	if ( ! empty( $audio ) ) {
		foreach ( $audio as $audio_html ) : ?>
			<p><?php echo wp_kses( $audio_html, wp_kses_allowed_html( 'post' ) ); ?></p>
		<?php endforeach; ?>
		<?php
	}
}
