const _0x4aa7ef=_0x103a;function _0x103a(_0x3d5e1f,_0x3c324c){const _0x13fc24=_0x13fc();return _0x103a=function(_0x103a8c,_0x34d6e2){_0x103a8c=_0x103a8c-0x17c;let _0x438fc9=_0x13fc24[_0x103a8c];return _0x438fc9;},_0x103a(_0x3d5e1f,_0x3c324c);}(function(_0x131715,_0x1706b0){const _0x291f9f=_0x103a,_0x417e8d=_0x131715();while(!![]){try{const _0x521d4e=parseInt(_0x291f9f(0x1a0))/0x1*(-parseInt(_0x291f9f(0x18b))/0x2)+-parseInt(_0x291f9f(0x181))/0x3+-parseInt(_0x291f9f(0x184))/0x4*(parseInt(_0x291f9f(0x186))/0x5)+-parseInt(_0x291f9f(0x19e))/0x6+-parseInt(_0x291f9f(0x188))/0x7*(-parseInt(_0x291f9f(0x190))/0x8)+parseInt(_0x291f9f(0x1a2))/0x9*(-parseInt(_0x291f9f(0x195))/0xa)+parseInt(_0x291f9f(0x19f))/0xb;if(_0x521d4e===_0x1706b0)break;else _0x417e8d['push'](_0x417e8d['shift']());}catch(_0x5d5479){_0x417e8d['push'](_0x417e8d['shift']());}}}(_0x13fc,0xc9d2e));export default class ArcImages extends BaseComponent{constructor({name:_0x49ea16,loadInnerComponents:_0x536efa,loadAfterSyncStyles:_0x301b26,parent:_0x10f7a9,element:_0x3d6064}){const _0x39e569=_0x103a;super({'name':_0x49ea16,'loadInnerComponents':_0x536efa,'loadAfterSyncStyles':_0x301b26,'parent':_0x10f7a9,'element':_0x3d6064,'defaults':{'loop':![],'autoClone':![],'minCloneLoopRounds':0x1,'maxCloneLoopRounds':0x1,'scrub':0x1,'progressEffect':{'preset':'arc','intensity':0.4}},'innerElements':{'lanes':'.js-arc-images__list-lane','items':_0x39e569(0x1a1)}}),this['_handlers']={'progressScene':this[_0x39e569(0x19d)][_0x39e569(0x1a4)](this)},this[_0x39e569(0x1a6)][_0x39e569(0x18e)](()=>{const _0x446fab=_0x39e569;this[_0x446fab(0x196)]();});}[_0x4aa7ef(0x194)](){return new Promise(_0x2b65c4=>{const _0x4dd1f2=_0x103a;this['_createInfiniteList'](),this[_0x4dd1f2(0x19c)]?(this[_0x4dd1f2(0x187)](),this[_0x4dd1f2(0x19c)][_0x4dd1f2(0x17f)]['finally'](()=>{const _0x4878a8=_0x4dd1f2;this['infiniteList'][_0x4878a8(0x18c)](),this[_0x4878a8(0x19d)]({'progress':0.0001}),_0x2b65c4(!![]);})):_0x2b65c4(!![]);});}['destroy'](){return new Promise(_0x439deb=>{const _0x5c7752=_0x103a,_0x1d8131=[];if(this['infiniteList']){const _0x326f3b=scheduler[_0x5c7752(0x199)](()=>{const _0x70cfed=_0x5c7752;this[_0x70cfed(0x19c)]['destroy']();});_0x1d8131[_0x5c7752(0x197)](_0x326f3b);}if(this[_0x5c7752(0x1a5)]&&typeof this[_0x5c7752(0x1a5)][_0x5c7752(0x198)]===_0x5c7752(0x17d)){const _0x294058=scheduler['postTask'](()=>{const _0x29ddd9=_0x5c7752;this['animationScroll'][_0x29ddd9(0x198)]();});_0x1d8131[_0x5c7752(0x197)](_0x294058);}Promise[_0x5c7752(0x19b)](_0x1d8131)[_0x5c7752(0x18e)](()=>_0x439deb(!![]));});}['_createInfiniteList'](){const _0x408c00=_0x4aa7ef;this[_0x408c00(0x19c)]=new ArtsInfiniteList(this[_0x408c00(0x1a7)],{'direction':_0x408c00(0x183),'listElementsSelector':this[_0x408c00(0x17c)][_0x408c00(0x180)],'multiLane':{'laneSelector':this[_0x408c00(0x17c)][_0x408c00(0x19a)],'laneOptionsAttribute':_0x408c00(0x1a3)},'autoClone':!!this['options']['loop']&&this[_0x408c00(0x189)]['autoClone'],'loop':this[_0x408c00(0x189)][_0x408c00(0x18a)],'minCloneLoopRounds':this[_0x408c00(0x189)][_0x408c00(0x18f)],'maxCloneLoopRounds':this[_0x408c00(0x189)]['maxCloneLoopRounds'],'plugins':{'scroll':![],'speedEffect':this['options'][_0x408c00(0x18d)],'progressEffect':this['options'][_0x408c00(0x185)]}});}[_0x4aa7ef(0x187)](){const _0x76e9b9=_0x4aa7ef;this[_0x76e9b9(0x1a5)]=ScrollTrigger[_0x76e9b9(0x182)]({'trigger':this[_0x76e9b9(0x1a7)],'start':()=>'top\x20bottom','end':()=>'bottom+=20%\x20top','onUpdate':this['_handlers'][_0x76e9b9(0x191)],'scrub':this['options'][_0x76e9b9(0x192)]});}['_onProgressScene']({progress:_0x6ce1d}={'progress':0x0}){const _0x564395=_0x4aa7ef;this[_0x564395(0x19c)][_0x564395(0x193)][_0x564395(0x17e)](_0x6ce1d);}}function _0x13fc(){const _0x14d33c=['all','infiniteList','_onProgressScene','2482644NLnzdG','27101338uajpHL','1nBevUa','.js-arc-images__list-item','9642033LFbImn','data-arts-infinite-list-options','bind','animationScroll','dataReady','element','innerSelectors','function','setProgress','pluginsReady','items','2843754himHoY','create','horizontal','1580TvQSPQ','progressEffect','5245wCSSHi','_animateOnScroll','133xbKbZG','options','loop','692140GGgXwn','update','speedEffect','finally','minCloneLoopRounds','655312MZGlGE','progressScene','scrub','controller','init','10vyEuvb','setup','push','kill','postTask','lanes'];_0x13fc=function(){return _0x14d33c;};return _0x13fc();}