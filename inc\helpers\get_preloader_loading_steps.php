<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! function_exists( 'arts_get_preloader_loading_steps' ) ) {
	/**
	 * Generate an array of the preloader fake "loading" steps.
	 *
	 * @param int $amount Number of steps to generate. Default is 4.
	 * @param int $limit  Maximum value for the steps. Default is 90.
	 * @return array Array of loading steps.
	 */
	function arts_get_preloader_loading_steps( $amount = 4, $limit = 90 ) {
		if ( $amount < 1 ) {
			return 1;
		}

		$result      = array();
		$equal_parts = $limit / $amount;
		$multiplier  = ( $limit / 100 ) + 1 / $amount;

		for ( $i = 1; $i < $amount; $i++ ) {
			array_push(
				$result,
				array(
					ceil( $equal_parts / $multiplier * $i ),
					ceil( $equal_parts * $multiplier * $i ),
				)
			);
		}

		// last step
		array_push( $result, array( 100, 100 ) );

		return $result;
	}
}
