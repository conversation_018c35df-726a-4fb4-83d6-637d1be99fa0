<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'woocommerce_add_to_cart_fragments', 'arts_add_to_cart_fragments_product_counter_badge' );
if ( ! function_exists( 'arts_add_to_cart_fragments_product_counter_badge' ) ) {
	/**
	 * Update total amount of products in cart
	 */
	function arts_add_to_cart_fragments_product_counter_badge( $fragments ) {
		$product_count = WC()->cart->get_cart_contents_count();
		$dom_selector  = '.mini-cart .mini-cart__badge';

		$fragments[ $dom_selector ]  = '<div class="mini-cart__badge" data-product-count="' . esc_html( $product_count ) . '">';
		$fragments[ $dom_selector ] .= '<div class="mini-cart__counter">';
		$fragments[ $dom_selector ] .= esc_html( $product_count );
		$fragments[ $dom_selector ] .= '</div>';
		$fragments[ $dom_selector ] .= '</div>';

		return $fragments;
	}
}
