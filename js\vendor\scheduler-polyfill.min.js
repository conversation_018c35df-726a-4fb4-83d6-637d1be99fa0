!function(){var i=["user-blocking","user-visible","background"];class t{constructor(){this.channel_=new MessageChannel,this.sendPort_=this.channel_.port2,this.messages_={},this.nextMessageHandle_=1,this.channel_.port1.onmessage=i=>this.onMessageReceived_(i)}queueCallback(i){var t=this.nextMessageHandle_++;return this.messages_[t]=i,this.sendPort_.postMessage(t),t}cancelCallback(i){delete this.messages_[i]}onMessageReceived_(i){var t=i.data;if(t in this.messages_){var e=this.messages_[t];delete this.messages_[t],e()}}}function e(){return e.instance_||(e.instance_=new t),e.instance_}class r{constructor(i,t,e){void 0===e&&(e=0),this.callback_=i,this.callbackType_=null,this.handle_=null,this.canceled_=!1,this.schedule_(t,e)}isIdleCallback(){return 0===this.callbackType_}isMessageChannelCallback(){return 2===this.callbackType_}cancel(){if(!this.canceled_)switch(this.canceled_=!0,this.callbackType_){case 0:cancelIdleCallback(this.handle_);break;case 1:clearTimeout(this.handle_);break;case 2:e().cancelCallback(this.handle_);break;default:throw new TypeError("Unknown CallbackType")}}schedule_(t,r){if(r&&r>0)return this.callbackType_=1,void(this.handle_=setTimeout(()=>{this.runCallback_()},r));if(!i.includes(t))throw new TypeError("Invalid task priority : "+t);return"background"===t&&"function"==typeof requestIdleCallback?(this.callbackType_=0,void(this.handle_=requestIdleCallback(()=>{this.runCallback_()}))):"function"==typeof MessageChannel?(this.callbackType_=2,void(this.handle_=e().queueCallback(()=>{this.runCallback_()}))):(this.callbackType_=1,void(this.handle_=setTimeout(()=>{this.runCallback_()})))}runCallback_(){this.canceled_||this.callback_()}}var s=0;class n{constructor(){this.head_=null,this.tail_=null}isEmpty(){return null==this.head_}push(i){if("object"!=typeof i)throw new TypeError("Task must be an Object");i.tq_sequence_=s++,this.isEmpty()?(i.tq_prev_=null,this.head_=i):(i.tq_prev_=this.tail_,this.tail_.tq_next_=i),i.tq_next_=null,this.tail_=i}takeNextTask(){if(this.isEmpty())return null;var i=this.head_;return this.remove_(i),i}merge(i,t){if("function"!=typeof t)throw new TypeError("Must provide a selector function.");if(null==i)throw new Error("sourceQueue cannot be null");for(var e=this.head_,r=null,s=i.head_;s;){var n=s;if(s=s.tq_next_,t(n)){for(i.remove_(n);e&&e.tq_sequence_<n.tq_sequence_;)r=e,e=e.tq_next_;this.insert_(n,r),r=n}}}toArray(){for(var i=this.head_,t=[];null!==i;)t.push(i),i=i.tq_next_;return t}insert_(i,t){if(t!=this.tail_){var e=t?t.tq_next_:this.head_;i.tq_next_=e,e.tq_prev_=i,i.tq_prev_=t,null!=t?t.tq_next_=i:this.head_=i}else this.push(i)}remove_(i){if(null==i)throw new Error("Expected task to be non-null");i===this.head_&&(this.head_=i.tq_next_),i===this.tail_&&(this.tail_=this.tail_.tq_prev_),i.tq_next_&&(i.tq_next_.tq_prev_=i.tq_prev_),i.tq_prev_&&(i.tq_prev_.tq_next_=i.tq_next_)}}class a extends Event{constructor(t,e){if(!e||!i.includes(e.previousPriority))throw new TypeError("Invalid task priority: '"+e.previousPriority+"'");super(t),this.previousPriority=e.previousPriority}}class l extends AbortController{constructor(t){if(void 0===t&&(t={}),super(),null==t&&(t={}),"object"!=typeof t)throw new TypeError("'init' is not an object");var e,r,s=void 0===t.priority?"user-visible":t.priority;if(!i.includes(s))throw new TypeError("Invalid task priority: '"+s+"'");this.priority_=s,this.isPriorityChanging_=!1,r=(e=this).signal,Object.defineProperties(r,{priority:{get:function(){return e.priority_},enumerable:!0},onprioritychange:{value:null,writable:!0,enumerable:!0}}),r.addEventListener("prioritychange",i=>{r.onprioritychange&&r.onprioritychange(i)})}setPriority(t){if(!i.includes(t))throw new TypeError("Invalid task priority: "+t);if(this.isPriorityChanging_)throw new DOMException("","NotAllowedError");if(this.signal.priority!==t){this.isPriorityChanging_=!0;var e=this.priority_;this.priority_=t;var r=new a("prioritychange",{previousPriority:e});this.signal.dispatchEvent(r),this.isPriorityChanging_=!1}}}void 0===self.scheduler?(self.scheduler=new class{constructor(){this.queues_={},i.forEach(i=>{this.queues_[i]=[new n,new n]}),this.pendingHostCallback_=null,this.signals_=new WeakMap}yield(i){return(i=Object.assign({},i)).signal&&"inherit"==i.signal&&delete i.signal,i.priority&&"inherit"==i.priority&&(i.priority="user-visible"),this.postTaskOrContinuation_(()=>{},i,!0)}postTask(i,t){return this.postTaskOrContinuation_(i,t,!1)}postTaskOrContinuation_(t,e,r){if(void 0!==(e=Object.assign({},e)).signal){if(null===e.signal||!("aborted"in e.signal)||"function"!=typeof e.signal.addEventListener)return Promise.reject(new TypeError("'signal' is not a valid 'AbortSignal'"));if(e.signal&&e.signal.priority&&!i.includes(e.signal.priority))return Promise.reject(new TypeError("Invalid task priority: '"+e.signal.priority+"'"))}if(void 0!==e.priority&&(null===e.priority||!i.includes(e.priority)))return Promise.reject(new TypeError("Invalid task priority: '"+e.priority+"'"));if(void 0===e.delay&&(e.delay=0),e.delay=Number(e.delay),e.delay<0)return Promise.reject(new TypeError("'delay' must be a positive number."));var s={callback:t,options:e,resolve:null,reject:null,hostCallback:null,abortCallback:null,onTaskCompleted:function(){this.options.signal&&this.abortCallback&&(this.options.signal.removeEventListener("abort",this.abortCallback),this.abortCallback=null)},onTaskAborted:function(){this.hostCallback&&(this.hostCallback.cancel(),this.hostCallback=null),this.options.signal.removeEventListener("abort",this.abortCallback),this.abortCallback=null,this.reject(this.options.signal.reason)},isAborted:function(){return this.options.signal&&this.options.signal.aborted},isContinuation:r},n=new Promise((i,t)=>{s.resolve=i,s.reject=t});return this.schedule_(s),n}schedule_(i){var t=i.options.signal;if(t){if(t.aborted)return void i.reject(t.reason);i.abortCallback=()=>{i.onTaskAborted()},t.addEventListener("abort",i.abortCallback)}i.options.delay>0?i.hostCallback=new r(()=>{i.hostCallback=null,this.onTaskDelayExpired_(i)},null,i.options.delay):(this.pushTask_(i),this.scheduleHostCallbackIfNeeded_())}onTaskDelayExpired_(i){this.pushTask_(i),this.pendingHostCallback_&&(this.pendingHostCallback_.cancel(),this.pendingHostCallback_=null),this.schedulerEntryCallback_()}onPriorityChange_(i){var t=this.signals_.get(i);if(void 0===t)throw new Error("Attempting to change priority on an unregistered signal");if(t!==i.priority){for(var e=0;e<2;e++)this.queues_[i.priority][e].merge(this.queues_[t][e],t=>t.options.signal===i);this.signals_.set(i,i.priority)}}schedulerEntryCallback_(){this.pendingHostCallback_=null,this.runNextTask_(),this.scheduleHostCallbackIfNeeded_()}scheduleHostCallbackIfNeeded_(){var{priority:i}=this.nextTaskPriority_();null!=i&&("background"!==i&&this.pendingHostCallback_&&this.pendingHostCallback_.isIdleCallback()&&(this.pendingHostCallback_.cancel(),this.pendingHostCallback_=null),this.pendingHostCallback_||(this.pendingHostCallback_=new r(()=>{this.schedulerEntryCallback_()},i,0)))}pushTask_(t){var e;if(!i.includes(e=t.options.priority?t.options.priority:t.options.signal&&t.options.signal.priority?t.options.signal.priority:"user-visible"))throw new TypeError("Invalid task priority: "+e);if(t.options.signal&&t.options.signal.priority){var r=t.options.signal;this.signals_.has(r)||(r.addEventListener("prioritychange",()=>{this.onPriorityChange_(r)}),this.signals_.set(r,r.priority))}this.queues_[e][t.isContinuation?0:1].push(t)}runNextTask_(){var i=null;do{var{priority:t,type:e}=this.nextTaskPriority_();if(null==t)return;i=this.queues_[t][e].takeNextTask()}while(i.isAborted());try{var r=i.callback();i.resolve(r)}catch(t){i.reject(t)}finally{i.onTaskCompleted()}}nextTaskPriority_(){for(var t=0;t<i.length;t++)for(var e=i[t],r=0;r<2;r++)if(!this.queues_[e][r].isEmpty())return{priority:e,type:r};return{priority:null,type:0}}},self.TaskController=l,self.TaskPriorityChangeEvent=a):self.scheduler.yield||(self.scheduler.yield=function(i){var t=i=>i&&"user-visible"!=i?i:"user-blocking";if((i=Object.assign({},i)).signal&&"inherit"==i.signal&&delete i.signal,i.priority&&"inherit"==i.priority&&delete i.priority,i.signal&&i.signal.aborted)return Promise.reject(i.signal.reason);var e=i.priority;!e&&i.signal&&i.signal.priority&&(e=i.signal.priority),e=t(e);var r={inputSignal:i.signal,controller:new self.TaskController({priority:e}),abortCallback:null,priorityCallback:null,onTaskAborted:function(){this.controller.abort(this.inputSignal.reason),this.abortCallback=null},onPriorityChange:function(){this.controller.setPriority(t(this.inputSignal.priority))},onTaskCompleted:function(){this.abortCallback&&(this.inputSignal.removeEventListener("abort",this.abortCallback),this.abortCallback=null),this.priorityCallback&&(this.inputSignal.removeEventListener("prioritychange",this.priorityCallback),this.priorityCallback=null)}};i.signal&&(r.abortCallback=()=>{r.onTaskAborted()},i.signal.addEventListener("abort",r.abortCallback)),i.signal&&i.signal.priority&&!i.priority&&(r.priorityCallback=()=>{r.onPriorityChange()},i.signal.addEventListener("prioritychange",r.priorityCallback));var s=self.scheduler.postTask(()=>{},{signal:r.controller.signal});return s.then(()=>{r.onTaskCompleted()}).catch(i=>{throw r.onTaskCompleted(),i}),s})}();