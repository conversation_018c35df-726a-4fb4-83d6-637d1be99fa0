<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! function_exists( 'arts_should_print_product_quantity' ) ) {
	function arts_should_print_product_quantity( $args = array() ) {
		global $product;

		if ( ! $product ) {
			return false;
		}

		$defaults = array(
			'grouped_product_child' => false,
		);
		$args     = wp_parse_args( $args, $defaults );

		$min_value = $product->get_min_purchase_quantity();
		$max_value = $product->get_max_purchase_quantity();

		if ( ! $args['grouped_product_child'] && $min_value > 0 && $min_value === $max_value ) {
			return false;
		}

		return true;
	}
}

add_action( 'woocommerce_before_add_to_cart_quantity', 'arts_woocommerce_add_to_cart_quantity_wrapper_start', -9999, 1 );
if ( ! function_exists( 'arts_woocommerce_add_to_cart_quantity_wrapper_start' ) ) {
	function arts_woocommerce_add_to_cart_quantity_wrapper_start( $args ) {
		$container_attributes          = array(
			'class' => array(),
		);
		$should_print_product_quantity = arts_should_print_product_quantity( $args );

		if ( $should_print_product_quantity ) {
			$container_attributes = arts_add_component_attributes(
				$container_attributes,
				array(
					'name'         => 'ProductQuantity',
					'hasAnimation' => false,
				)
			);
		} else {
			$container_attributes['class'][] = 'product-quantity';
			$container_attributes['class'][] = 'd-none';
		}
		?>
		<div <?php arts_print_attributes( $container_attributes ); ?>>
		<?php if ( $should_print_product_quantity ) : ?>
			<div class="product-quantity__button product-quantity__button_minus js-product-quantity__button-minus">-</div>
		<?php endif; ?>
		<?php
	}
}

add_action( 'woocommerce_after_add_to_cart_quantity', 'arts_woocommerce_add_to_cart_quantity_wrapper_end', -9999 );
if ( ! function_exists( 'arts_woocommerce_add_to_cart_quantity_wrapper_end' ) ) {
	function arts_woocommerce_add_to_cart_quantity_wrapper_end( $args ) {
		$should_print_product_quantity = arts_should_print_product_quantity( $args );
		?>
		<?php if ( $should_print_product_quantity ) : ?>
			<div class="product-quantity__button product-quantity__button_plus js-product-quantity__button-plus">+</div>
		<?php endif; ?>
		</div>
		<?php
	}
}
