'use strict';

(function (elementor) {
	let
		isLightboxLoading = false,
		isSmoothScrollLoading = false,
		isCursorLoading = false,
		isPreloaderLoading = false;

	elementor.on('preview:loaded', () => {
		getApp().setLoaded();
	});

	/**
	 * Elementor Document Settings
	 * Live Preview
	 */
	elementor.once('preview:loaded', () => {
		const
			app = getApp(),
			debounceTimeout = app.utilities.getDebounceTime(),
			{ addChangeCallback } = elementor.settings.page,
			siteCursorSettings = [
				'cursor_follower_enabled',
				'cursor_follower_size',
				'cursor_follower_color',
				'cursor_follower_background_color',
				'cursor_follower_mix_blend_mode',
				'cursor_follower_animation_duration',
				'cursor_follower_trailing_effect_enabled',
				'cursor_follower_trailing_effect_factor',
				'cursor_follower_elastic_effect_enabled',
				'cursor_follower_elastic_effect_factor',
				'cursor_follower_highlight_effect_enabled',
				'cursor_follower_highlight_scale',
				'cursor_follower_highlight_opacity',
				'cursor_follower_click_scale_effect_enabled',
				'cursor_follower_click_scale_effect_factor',
				'cursor_progress_enabled',
				'cursor_follower_hide_iframes_enabled',
				'cursor_follower_passive_listeners_enabled',
			],
			pageCursorSettings = [
				'page_cursor_settings_overridden',
				'page_cursor_follower_color',
				'page_cursor_follower_background_color'
			],
			smoothScrollingSettings = [
				'smooth_scrolling_enabled',
				'smooth_scrolling_duration',
				'smooth_scrolling_gsap_raf_enabled',
			],
			lightboxSettings = [
				'lightbox_gallery_captions_enabled',
				'lightbox_images_counter_enabled',
				'lightbox_zoom_button_enabled',
				'lightbox_initial_zoom_level',
				'lightbox_secondary_zoom_level',
				'lightbox_maximum_zoom_level',
				'lightbox_elements_color_hover',
				'lightbox_arrows_color_hover'
			],
			pagePreloaderSettings = [
				'preloader_enabled',
				'preloader_before_loading_text',
				'preloader_after_loading_text',
				'preloader_heading_text',
				'preloader_loader_style',
				'preloader_loader_custom_image',
				'preloader_loader_image_height',
				'preloader_counter_enabled',
				'preloader_sliding_images_enabled',
				'preloader_sliding_images',
				'preloader_sliding_images_custom_dimension',
				'preloader_sliding_images_size',
				'preloader_background_color',
				'preloader_sliding_images_opacity',
				'preloader_typography_counter_text_color',
				'preloader_typography_counter_border_color',
				'preloader_typography_loading_text',
				'preloader_typography_loading_text_text_color',
				'preloader_typography_heading',
				'preloader_typography_heading_text_color',
				'preloader_animation_timescale',
				'preloader_animation_loading_steps',
				'preloader_animation_loading_rotation',
				'preloader_animation_loading_final_rotation',
				'preloader_animation_final_delay',
				'preloader_animation_final_offset',
			],
			virtualScrollSettings = [
				'virtual_scrolling_easing_mouse',
				'virtual_scrolling_easing_touch',
				'virtual_scrolling_speed_mouse',
				'virtual_scrolling_speed_touch',
				'virtual_scrolling_max_delta_mouse',
				'virtual_scrolling_max_delta_touch',
				'virtual_scrolling_snap_delay_mouse',
				'virtual_scrolling_snap_delay_touch',
			],
			lightboxCursorSettings = [
				'cursor_lightbox_clickable_images_enabled',
				'cursor_lightbox_clickable_images_scale_selector',
				'cursor_lightbox_clickable_images_hide_native_enabled',
				'cursor_lightbox_clickable_images_helper_selector',
				'cursor_lightbox_clickable_images_label',
				'cursor_lightbox_clickable_images_custom_scale',
				'cursor_lightbox_clickable_images_custom_color',
				'cursor_lightbox_clickable_images_custom_background',
				'cursor_lightbox_clickable_images_custom_icon',
			],
			onPreloaderSettingChange = app.utilities.debounce(reloadPreloader.bind(reloadPreloader), debounceTimeout),
			onSmoothScrollingSettingChange = app.utilities.debounce(reloadSmoothScroll.bind(reloadSmoothScroll), debounceTimeout),
			onLightboxSettingChange = app.utilities.debounce(reloadLightbox.bind(reloadLightbox), debounceTimeout),
			onVirtualScrollSettingChange = app.utilities.debounce(reloadVirtualScroll.bind(reloadVirtualScroll), debounceTimeout),
			onLightboxCursorSettingChange = app.utilities.debounce(renderLightboxLinksCursorSettings.bind(renderLightboxLinksCursorSettings), debounceTimeout);

		siteCursorSettings.forEach((setting) => addChangeCallback(setting, reloadCursor.bind(reloadCursor, setting, true)));
		pageCursorSettings.forEach((setting) => addChangeCallback(setting, reloadCursor.bind(reloadCursor, setting, false)));
		pagePreloaderSettings.forEach((setting) => addChangeCallback(setting, onPreloaderSettingChange));
		smoothScrollingSettings.forEach((setting) => addChangeCallback(setting, onSmoothScrollingSettingChange));
		lightboxSettings.forEach((setting) => addChangeCallback(setting, onLightboxSettingChange));
		virtualScrollSettings.forEach((setting) => addChangeCallback(setting, onVirtualScrollSettingChange));
		lightboxCursorSettings.forEach((setting) => addChangeCallback(setting, onLightboxCursorSettingChange));

		elementor.$preview.off('arts/elementor/reload_preview').on('arts/elementor/reload_preview', (event, data) => {
			reloadPreview();
		});

		elementor.$preview.off('arts/elementor/render_lightbox_links_cursor_settings').on('arts/elementor/render_lightbox_links_cursor_settings', (event, data) => {
			elementorCommon.elements.$body.addClass('elementor-panel-loading');

			// file deepcode ignore PromiseNotCaughtGeneral: <no catch function available>
			$e.run('document/save/update').then(() => {
				renderLightboxLinksCursorSettings();
				elementorCommon.elements.$body.removeClass('elementor-panel-loading');
			});
		});
	});

	/**
	 * Reload Preview & Open Panel
	 */
	function updatePreview(route, section) {
		elementor.once('preview:loaded', () => {
			// file deepcode ignore PromiseNotCaughtGeneral: <no catch function available>
			$e.data.get('globals/index').then(() => {
				setTimeout(() => {
					if ($e.routes.current.panel.search('panel/global') >= 0) {
						$e.run('panel/global/open').then(() => {
							if (route) {
								$e.route(route);
							}

							elementorCommon.elements.$body.removeClass('elementor-panel-loading');
						});
					} else {

						if (route) {
							$e.route(route);
						}

						if (section) {
							elementor.getPanelView().getCurrentPageView().activateSection(section);
							elementor.getPanelView().getCurrentPageView().openActiveSection();
							elementor.getPanelView().getCurrentPageView().render();
						}

						elementorCommon.elements.$body.removeClass('elementor-panel-loading');
					}
				}, 500);
			});
		});

		$e.run('preview/reload');
	}

	/**
	 * Reload Elementor Preview
	 */
	function reloadPreview(route, section) {
		elementorCommon.elements.$body.addClass('elementor-panel-loading');
		// file deepcode ignore PromiseNotCaughtGeneral: <no catch function available>
		$e.run('document/save/update').then(() => {
			updatePreview(route, section);
		});
	}

	function reloadLightbox() {
		const
			app = getApp(),
			PSWPComponents = app.componentsManager.instances.disposable.filter((component) => component.name === 'PSWP');

		try {
			updateLightboxOptions();
		} catch (err) {

		}

		if (!isLightboxLoading) {

			isLightboxLoading = true;

			if (PSWPComponents.length) {
				PSWPComponents.forEach((component) => {
					component.reload({ options: app.options.gallery });
				});
			}

			isLightboxLoading = false;
		}
	}

	function renderLightboxLinksCursorSettings() {
		const
			lightBoxCursorElements = [...getDocumentRef().querySelectorAll('.has-global-cursor-lightbox')];

		if (!lightBoxCursorElements.length) {
			return;
		}

		const
			cursorSettings = {},
			enabled = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_enabled'];

		if (enabled) {
			const
				scaleSelector = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_scale_selector'],
				hideNative = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_hide_native_enabled'],
				helperSelector = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_helper_selector'],
				customScale = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_custom_scale'];

			if (scaleSelector === 'current') {
				Object.assign(cursorSettings, {
					scale: 'current'
				});
			}

			if (scaleSelector === 'custom') {
				// Unitless scale
				if (customScale.unit === 'scale') {
					Object.assign(cursorSettings, {
						scale: Number(customScale.size)
					});
				} else {
					Object.assign(cursorSettings, {
						scale: `${Number(customScale.size)}${customScale.unit}`
					});
				}
			}

			Object.assign(cursorSettings, {
				hideNative: Boolean(hideNative),
			});

			if (helperSelector) {
				const
					label = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_label'],
					color = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_color'],
					background = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_background'],
					icon = elementor.settings.page.model.attributes['cursor_lightbox_clickable_images_icon'];

				if (helperSelector === 'label' && label && label.length) {
					Object.assign(cursorSettings, {
						label
					});
				}

				if (color && color.length) {
					Object.assign(cursorSettings, {
						color
					});
				}

				if (background && background.length) {
					Object.assign(cursorSettings, {
						background
					});
				}

				if (icon && icon.value && icon.value.length) {
					Object.assign(cursorSettings, {
						className: icon.value
					});
				}
			}

			lightBoxCursorElements.forEach((el) => {
				try {
					const cursorSettingsString = JSON.stringify(cursorSettings);

					el.setAttribute('data-arts-cursor-follower-target', cursorSettingsString);
				} catch (error) {

				}
			});
		} else {
			lightBoxCursorElements.forEach((el) => {
				el.removeAttribute('data-arts-cursor-follower-target');
			});
		}
	}

	function reloadVirtualScroll() {
		try {
			updateVirtualScrollOptions();
		} catch (err) {

		}
	}

	function reloadSmoothScroll() {
		const
			app = getApp(),
			scrollRef = app.componentsManager.getComponentByName('Scroll');

		if (scrollRef) {
			app.componentsManager.disposeComponent(scrollRef.element, 'persistent');
		}

		try {
			updateSmoothScrollOptions();
		} catch (err) {

		}

		if (!isSmoothScrollLoading) {
			isSmoothScrollLoading = true;

			app.loadScroll(false).finally(() => {
				app.componentsManager.updateRef('scrollRef', 'Scroll', app.componentsManager.instances.persistent);
				isSmoothScrollLoading = false;
			});
		}
	}

	function reloadCursor(setting, updateOptions = true) {
		const
			app = getApp(),
			cursorRef = app.componentsManager.getComponentByName('CursorFollower');

		if (cursorRef) {
			app.componentsManager.disposeComponent(cursorRef.element, 'persistent');
		}

		if (updateOptions) {
			try {
				updateCursorOptions();
			} catch (err) {

			}
		}

		if (!isCursorLoading) {
			isCursorLoading = true;

			if (!app.elements.cursor) {
				const body = new FormData();

				body.append('action', 'get_cursor_follower_template');
				body.append('_ajax_nonce', app.options.nonce.elementorEditor);

				fetch(app.options.ajaxURL, {
					method: 'POST',
					body
				}).then((res) => {
					if (res.status >= 200 && res.status < 300) {
						res.text().then((cursorHTML) => {
							app.elements.container.insertAdjacentHTML('afterend', cursorHTML);

							Object.assign(app.elements, {
								cursor: getDocumentRef().querySelector('#js-arts-cursor')
							});

							loadCursor();
						}).catch((e) => {
							console.error(e);
						});
					} else {
						isCursorLoading = false;
						reloadPreview();
					}
				}).catch(() => {
					isCursorLoading = false;
					reloadPreview();
				});
			} else {
				loadCursor();
			}
		}
	}

	function reloadPreloader() {
		const
			app = getApp(),
			preloaderRef = app.componentsManager.getComponentByName('Preloader');

		if (preloaderRef) {
			app.componentsManager.disposeComponent(preloaderRef.element, 'persistent');

			preloaderRef.element.remove();
			Object.assign(app.elements, {
				preloader: null
			});
		}

		try {
			updatePreloaderOptions();
		} catch (err) {

		}

		if (!isPreloaderLoading && app.utilities.isEnabledOption(app.options.preloader)) {
			isPreloaderLoading = true;

			if (!app.elements.preloader) {
				const body = new FormData();

				body.append('action', 'get_preloader_template');
				body.append('settings', JSON.stringify(app.options.preloader));
				body.append('_ajax_nonce', app.options.nonce.elementorEditor);

				fetch(app.options.ajaxURL, {
					method: 'POST',
					body
				}).then((res) => {
					if (res.status >= 200 && res.status < 300) {
						res.text().then((preloaderHTML) => {
							app.elements.container.insertAdjacentHTML('beforebegin', preloaderHTML);

							Object.assign(app.elements, {
								preloader: getDocumentRef().querySelector('#page-preloader')
							});

							loadPreloader();
						}).catch((e) => {
							console.error(e);
						});
					} else {
						isPreloaderLoading = false;
						reloadPreview();
					}
				}).catch(() => {
					isPreloaderLoading = false;
					reloadPreview();
				});
			} else {
				loadPreloader();
			}
		}
	}

	function updateVirtualScrollOptions() {
		const
			app = getApp(),
			currentSettings = elementor.settings.page.model.attributes,
			easingMouse = currentSettings['virtual_scrolling_easing_mouse']['size'],
			easingTouch = currentSettings['virtual_scrolling_easing_touch']['size'],
			speedMouse = currentSettings['virtual_scrolling_speed_mouse']['size'],
			speedTouch = currentSettings['virtual_scrolling_speed_touch']['size'],
			maxDeltaMouse = currentSettings['virtual_scrolling_max_delta_mouse']['size'],
			maxDeltaTouch = currentSettings['virtual_scrolling_max_delta_touch']['size'],
			snapDelayMouse = currentSettings['virtual_scrolling_snap_delay_mouse']['size'],
			snapDelayTouch = currentSettings['virtual_scrolling_snap_delay_touch']['size'];

		Object.assign(app.options, {
			virtualScroll: {
				easing: {
					mouse: Number(easingMouse),
					touch: Number(easingTouch),
				},
				speed: {
					mouse: Number(speedMouse),
					touch: Number(speedTouch),
				},
				maxDelta: {
					mouse: Number(maxDeltaMouse),
					touch: Number(maxDeltaTouch),
				},
				snapDelay: {
					mouse: Number(snapDelayMouse),
					touch: Number(snapDelayTouch),
				},
			}
		});
	}

	function updateLightboxOptions() {
		const
			app = getApp(),
			currentSettings = elementor.settings.page.model.attributes,
			captions = currentSettings['lightbox_gallery_captions_enabled'],
			counter = currentSettings['lightbox_images_counter_enabled'],
			zoom = currentSettings['lightbox_zoom_button_enabled'],
			initialZoomLevel = currentSettings['lightbox_initial_zoom_level'],
			secondaryZoomLevel = currentSettings['lightbox_secondary_zoom_level']['size'],
			maxZoomLevel = currentSettings['lightbox_maximum_zoom_level']['size'],
			elementsHoverColor = currentSettings['lightbox_elements_color_hover'],
			arrowsHoverColor = currentSettings['lightbox_arrows_color_hover'];

		Object.assign(app.options.gallery, {
			itemsSelector: 'a[href]:not(a[href="#"]):not(a[href*="#"]):not(.no-lightbox)',
			// bgOpacity          => floatval( $lightbox_background_opacity ),
			// colorTheme         => esc_js( $lightbox_color_theme ),
			initialZoomLevel: String(initialZoomLevel),
			secondaryZoomLevel: Number(secondaryZoomLevel),
			maxZoomLevel: Number(maxZoomLevel),
			// "X" (close) button
			close: {
				custom: true,
				label: false,
				labelHover: false,
				cursor: {
					magnetic: 0.25,
					scale: 1.3,
					hideNative: false,
					color: elementsHoverColor,
				},
			},
			// Prev & next gallery arrows
			arrows: {
				custom: true,
				cursor: {
					scale: 'current',
					magnetic: 0.25,
					hideNative: false,
					color: arrowsHoverColor,
				}
			},
			// Images counter in gallery (e.g. "2 / 7")
			counter: Boolean(counter) ? {
				custom: true
			} : false,
			// Images captions grabbed from 'data-caption' attribute on <a> link
			// or from "alt" attribute of the currently active image
			captions: Boolean(captions),
			// Media loading indicator
			preloader: {
				custom: true
			},
			zoom: Boolean(zoom) ? {
				custom: true
			} : false
		});
	}

	function updateCursorOptions() {
		const
			app = getApp(),
			currentSettings = elementor.settings.page.model.attributes,
			highlightEnabled = currentSettings['cursor_follower_highlight_effect_enabled'],
			trailingEnabled = currentSettings['cursor_follower_trailing_effect_enabled'],
			elasticEnabled = currentSettings['cursor_follower_elastic_effect_enabled'],
			clickScaleEnabled = currentSettings['cursor_follower_click_scale_effect_enabled'];

		Object.assign(app.options, {
			cursorFollower: {
				enabled: Boolean(currentSettings['cursor_follower_enabled']),
				hideIFramesHover: Boolean(currentSettings['cursor_follower_hide_iframes_enabled']),
				animationDuration: Number(currentSettings['cursor_follower_animation_duration']['size']),
				matchMedia: '(hover: hover) and (pointer: fine)',
				passiveListeners: Boolean(currentSettings['cursor_follower_passive_listeners_enabled']),
				useCSSVars: Boolean(currentSettings['cursor_follower_use_css_vars_enabled']),
				useGSAPRaf: Boolean(currentSettings['cursor_follower_gsap_raf_enabled']),
			},
			cursorLoading: Boolean(currentSettings['cursor_progress_enabled'])
		});

		if (highlightEnabled) {
			Object.assign(app.options.cursorFollower, {
				highlight: {
					includeClass: 'cursor-highlight',
					excludeClass: 'cursor-no-highlight',
					scale: Number(currentSettings['cursor_follower_highlight_scale']['size']) + currentSettings['cursor_follower_highlight_scale']['unit'],
					opacity: Number(currentSettings['cursor_follower_highlight_opacity']['size'])
				}
			})
		} else {
			Object.assign(app.options.cursorFollower, {
				highlight: false
			});
		}

		if (clickScaleEnabled) {
			Object.assign(app.options.cursorFollower, {
				clickScale: Number(currentSettings['cursor_follower_click_scale_effect_factor']['size'])
			});
		} else {
			Object.assign(app.options.cursorFollower, {
				clickScale: false
			});
		}

		if (trailingEnabled) {
			Object.assign(app.options.cursorFollower, {
				trailing: Number(currentSettings['cursor_follower_trailing_effect_factor']['size'])
			});
		} else {
			Object.assign(app.options.cursorFollower, {
				trailing: 1
			});
		}

		if (elasticEnabled) {
			Object.assign(app.options.cursorFollower, {
				elastic: Number(currentSettings['cursor_follower_elastic_effect_factor']['size'])
			});
		} else {
			Object.assign(app.options.cursorFollower, {
				elastic: 0
			});
		}
	}

	function updatePreloaderOptions() {
		const
			app = getApp(),
			currentSettings = elementor.settings.page.model.attributes,
			enabled = currentSettings['preloader_enabled'],
			timeScale = currentSettings['preloader_animation_timescale']['size'],
			loadingRotation = currentSettings['preloader_animation_loading_rotation']['size'],
			loadingSteps = getLoadingSteps(currentSettings['preloader_animation_loading_steps']['size']),
			finalDelay = currentSettings['preloader_animation_final_delay']['size'],
			finalOffset = currentSettings['preloader_animation_final_offset']['size'],
			finalRotation = currentSettings['preloader_animation_loading_final_rotation']['size'],
			//
			beforeLoadingText = currentSettings['preloader_before_loading_text'],
			afterLoadingText = currentSettings['preloader_after_loading_text'],
			headingText = currentSettings['preloader_heading_text'],
			loaderStyle = currentSettings['preloader_loader_style'],
			counterEnabled = loaderStyle === 'counter' && currentSettings['preloader_counter_enabled'],
			customLoaderEnabled = loaderStyle === 'custom_image',
			customLoaderImage = currentSettings['preloader_loader_custom_image'] && currentSettings['preloader_loader_custom_image']['id'] ? currentSettings['preloader_loader_custom_image']['id'] : null,
			slidingImagesEnabled = currentSettings['preloader_sliding_images_enabled'],
			slidingImages = currentSettings['preloader_sliding_images'],
			slidingImagesSize = currentSettings['preloader_sliding_images_size'],
			slidingImagesCustomDimension = currentSettings['preloader_sliding_images_custom_dimension'],
			slidingImagesArray = [];

		if (enabled && slidingImagesEnabled) {
			slidingImages.models.forEach((model) => {
				if ('image' in model.attributes && typeof model.attributes.image['id'] === 'number') {
					slidingImagesArray.push({
						'_id': model.attributes._id,
						'image': model.attributes.image,
					});
				}
			});
		}

		Object.assign(app.options.preloader, {
			enabled: Boolean(enabled),
			timeScale: Number(timeScale),
			loadingRotation: Number(loadingRotation),
			loadingSteps,
			finalDelay: Number(finalDelay),
			finalOffset: Number(finalOffset),
			finalRotation: Number(finalRotation),
			toggleLoadClass: 'preloader_loaded',
			//
			beforeLoadingText: String(beforeLoadingText),
			afterLoadingText: String(afterLoadingText),
			headingText: String(headingText),
			counterEnabled: Boolean(counterEnabled),
			customLoaderEnabled: Boolean(customLoaderEnabled),
			customLoaderImage: customLoaderImage ? String(customLoaderImage) : false,
			slidingImagesEnabled: Boolean(slidingImagesEnabled),
			slidingImages: slidingImagesArray,
			slidingImagesSize,
			slidingImagesCustomDimension
		});
	}

	function updateSmoothScrollOptions() {
		const
			app = getApp(),
			currentSettings = elementor.settings.page.model.attributes,
			enabled = currentSettings['smooth_scrolling_enabled'],
			duration = currentSettings['smooth_scrolling_duration']['size'],
			useGSAPRaf = currentSettings['smooth_scrolling_gsap_raf_enabled'];

		Object.assign(app.options, {
			smoothScroll: {
				enabled: Boolean(enabled),
				duration: Number(duration),
				easing: 'expo.out',
				useGSAPRaf: Boolean(useGSAPRaf),
			}
		});
	}

	function loadCursor() {
		const app = getApp();

		isCursorLoading = true;

		const follower = getDocumentRef().querySelector('.arts-cursor__follower');

		if (follower) {
			follower.removeAttribute('style');
		}

		app.loadCursor().finally(() => {
			app.componentsManager.updateRef('cursorRef', 'CursorFollower', app.componentsManager.instances.persistent);
			isCursorLoading = false;
		});
	}

	function updateLazyImages() {
		const app = getApp();

		return app.utilities.updateLazy();
	}

	function loadPreloader() {
		const app = getApp();

		isPreloaderLoading = true;

		app.loadPreloader()
			.then(() => updateLazyImages())
			.finally(() => {
				app.componentsManager.updateRef('preloaderRef', 'Preloader', app.componentsManager.instances.persistent);
				isPreloaderLoading = false;
		});
	}

	function getLoadingSteps(amount, limit = 90) {
		if (amount < 1) {
			return [[100, 100]];
		}

		const result = [];
		const equal_parts = limit / amount;
		const multiplier = (limit / 100) + 1 / amount;

		for (let i = 1; i < amount; i++) {
			result.push([
				Math.ceil(equal_parts / multiplier * i),
				Math.ceil(equal_parts * multiplier * i)
			]);
		}

		// last step
		result.push([100, 100]);

		return result;
	};

	function getApp() {
		return window.app ? window.app : elementor.$preview.get(0).contentWindow.app;
	}

	function getDocumentRef() {
		return elementor.$preview.get(0).contentWindow.document;
	}
})(elementor);
