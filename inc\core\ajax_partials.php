<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'wp_ajax_get_cursor_follower_template', 'arts_ajax_get_cursor_follower_template' );
add_action( 'wp_ajax_nopriv_get_cursor_follower_template', 'arts_ajax_get_cursor_follower_template' );
if ( ! function_exists( 'arts_ajax_get_cursor_follower_template' ) ) {
	/**
	 * <PERSON>les AJAX request to get the cursor follower template.
	 *
	 * @return void
	 */
	function arts_ajax_get_cursor_follower_template() {
		check_ajax_referer( 'elementor-editor-nonce' );

		get_template_part( 'template-parts/cursor/cursor' );
		wp_die();
	}
}

add_action( 'wp_ajax_get_preloader_template', 'arts_ajax_get_preloader_template' );
add_action( 'wp_ajax_nopriv_get_preloader_template', 'arts_ajax_get_cursor_follower_template' );
if ( ! function_exists( 'arts_ajax_get_preloader_template' ) ) {
	/**
	 * <PERSON>les AJAX request to get the preloader template.
	 *
	 * @return void
	 */
	function arts_ajax_get_preloader_template() {
		check_ajax_referer( 'elementor-editor-nonce' );

		$args = array();

		if ( $_POST['settings'] ) {
			$jsonData = stripslashes( html_entity_decode( $_POST['settings'] ) );
			$args     = json_decode( $jsonData, true );
		}

		// Make BFI to work when doing WP AJAX calls in Elementor editor
		if ( is_admin() && defined( 'DOING_AJAX' ) ) {
			add_filter( 'image_resize_dimensions', 'bfi_image_resize_dimensions', 10, 5 );
		}

		get_template_part( 'template-parts/preloader/preloader', null, $args );

		wp_die();
	}
}
