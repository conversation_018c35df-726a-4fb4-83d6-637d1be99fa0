<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'shortcode_atts_wpcf7', 'arts_shortcode_atts_wpcf7_filter', 10, 3 );
if ( ! function_exists( 'arts_shortcode_atts_wpcf7_filter' ) ) {
	/**
	 * Filter the Contact Form 7 shortcode attributes to add custom styles.
	 *
	 * @param array $out   The output array of shortcode attributes.
	 * @param array $pairs The supported attributes and their defaults.
	 * @param array $atts  The user-defined shortcode attributes.
	 *
	 * @return array The modified output array of shortcode attributes.
	 */
	function arts_shortcode_atts_wpcf7_filter( $out, $pairs, $atts ) {
		if ( isset( $atts['primary_color'] ) ) {
			$color = $atts['primary_color'];

			add_filter(
				'wpcf7_form_additional_atts',
				function( $attrs ) use ( $color ) {
					$var_string = "--form-primary-color: {$color};";

					if ( ! isset( $attrs['style'] ) ) {
						$attrs['style'] = $var_string;
					} else {
						$attrs['style'] .= " {$var_string}";
					}

					return $attrs;
				}
			);
		}

		if ( isset( $atts['secondary_color'] ) ) {
			$color = $atts['secondary_color'];

			add_filter(
				'wpcf7_form_additional_atts',
				function( $attrs ) use ( $color ) {
					$var_string = "--form-secondary-color: {$color};";

					if ( ! isset( $attrs['style'] ) ) {
						$attrs['style'] = $var_string;
					} else {
						$attrs['style'] .= " {$var_string}";
					}

					return $attrs;
				}
			);
		}

		if ( isset( $atts['accent_color'] ) ) {
			$color = $atts['accent_color'];

			add_filter(
				'wpcf7_form_additional_atts',
				function( $attrs ) use ( $color ) {
					$var_string = "--form-accent-color: {$color};";

					if ( ! isset( $attrs['style'] ) ) {
						$attrs['style'] = $var_string;
					} else {
						$attrs['style'] .= " {$var_string}";
					}

					return $attrs;
				}
			);
		}

		return $out;
	}
}
