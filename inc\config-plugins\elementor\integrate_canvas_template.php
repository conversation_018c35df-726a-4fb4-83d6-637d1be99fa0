<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

/**
 * Extra markup for Elementor Canvas template: BEFORE
 */
add_action( 'elementor/page_templates/canvas/before_content', 'arts_elementor_canvas_before_content' );
if ( ! function_exists( 'arts_elementor_canvas_before_content' ) ) {
	function arts_elementor_canvas_before_content() {
		$preloader_enabled            = arts_is_preloader_enabled();
		$ajax_enabled                 = Utilities::get_kit_settings( 'ajax_enabled', false );
		$container_attributes         = arts_get_main_container_attributes();
		$container_content_attributes = arts_get_main_content_attributes();
		?>

		<?php if ( $ajax_enabled ) : ?>
			<!-- PAGE AJAX OUTER WRAPPER -->
			<div data-barba="wrapper">
				<?php do_action( 'asli/barba/after_wrapper_start' ); ?>
		<?php endif; ?>

		<?php if ( $preloader_enabled ) : ?>
			<!-- PAGE PRELOADER -->
				<?php get_template_part( 'template-parts/preloader/preloader' ); ?>
			<!-- - PAGE PRELOADER -->
		<?php endif; ?>

		<!-- PAGE MAIN CONTAINER -->
		<main <?php Utilities::print_attributes( $container_attributes ); ?>>
			<!-- PAGE CONTENT -->
			<div <?php Utilities::print_attributes( $container_content_attributes ); ?>>
			<?php
	}
}

/**
 * Extra markup for Elementor Canvas template: AFTER
 */
add_action( 'elementor/page_templates/canvas/after_content', 'arts_elementor_canvas_after_content' );
if ( ! function_exists( 'arts_elementor_canvas_after_content' ) ) {
	function arts_elementor_canvas_after_content() {
		$ajax_enabled                 = Utilities::get_kit_settings( 'ajax_enabled', false );
		$ajax_spinner_desktop_enabled = Utilities::get_kit_settings( 'ajax_spinner_desktop_enabled', false );
		$ajax_spinner_mobile_enabled  = Utilities::get_kit_settings( 'ajax_spinner_mobile_enabled', true );
		$cursor_follower_enabled      = arts_is_cursor_follower_enabled();
		$outdated_browsers_enabled    = Utilities::get_kit_settings( 'outdated_browsers_enabled', false );

		$spinner_args = arts_get_loading_spinner_attributes();
		?>
				<?php if ( function_exists( 'elementor_theme_do_location' ) ) : ?>
					<!-- Theme Builder Popups -->
					<?php elementor_theme_do_location( 'popup' ); ?>
					<!-- - Theme Builder Popups -->
				<?php endif; ?>
			</div>
			<!-- - PAGE CONTENT -->
			<?php if ( $ajax_enabled ) : ?>
				<?php $body_styles_model = Utilities::get_body_styles_model(); ?>
				<div id="ajax-body-styles" class="d-none" data-body-styles="<?php echo esc_attr( json_encode( $body_styles_model ) ); ?>"></div>
			<?php endif; ?>
		</main>
		<!-- - PAGE MAIN CONTAINER -->

		<?php if ( $cursor_follower_enabled ) : ?>
			<!-- Cursor Follower -->
			<?php get_template_part( 'template-parts/cursor/cursor' ); ?>
			<!-- - Cursor Follower -->
		<?php endif; ?>

		<?php if ( $ajax_enabled ) : ?>
			<?php if ( $ajax_spinner_desktop_enabled || $ajax_spinner_mobile_enabled ) : ?>
				<!-- Loading Spinner -->
				<?php get_template_part( 'template-parts/svg/circle', '', $spinner_args ); ?>
				<!-- - Loading Spinner -->
			<?php endif; ?>
				<!-- Curtain Cursor Blocking -->
				<div class="blocking-curtain" id="page-blocking-curtain"></div>
				<!-- - Curtain Cursor Blocking -->
				<?php do_action( 'asli/barba/before_wrapper_end' ); ?>
			</div>
			<!-- - PAGE AJAX OUTER WRAPPER -->
		<?php endif; ?>

		<!-- Curtain shape holder-->
		<?php get_template_part( 'template-parts/svg/curtain' ); ?>
		<!-- - Curtain shape holder-->

		<?php if ( $outdated_browsers_enabled ) : ?>
			<div id="outdated"></div>
		<?php endif; ?>
		<?php
	}
}
