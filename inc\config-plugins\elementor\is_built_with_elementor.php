<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_is_built_with_elementor' ) ) {
	/**
	 * Check if a post is built with Elementor.
	 *
	 * @param int|null $post_id The ID of the post to check. Defaults to null.
	 *
	 * @return bool True if the post is built with Elementor, false otherwise.
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_built_with_elementor()` method instead.
	 */
	function arts_is_built_with_elementor( $post_id = null ) {
		return Utilities::is_built_with_elementor( $post_id );
	}
}
