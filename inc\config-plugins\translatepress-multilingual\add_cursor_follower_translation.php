<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'init', 'arts_action_extend_trp_node_accessors' );
if ( ! function_exists( 'arts_action_extend_trp_node_accessors' ) ) {
	/**
	 * Extends the TRP node accessors.
	 */
	function arts_action_extend_trp_node_accessors() {
		add_filter( 'trp_node_accessors', 'arts_extend_trp_node_accessors' );
	}
}

if ( ! function_exists( 'arts_extend_trp_node_accessors' ) ) {
	/**
	 * Extends the TRP node accessors map to include cursor follower labels
	 * for translation.
	 *
	 * @param array $map The existing TRP node accessors map.
	 * @return array The modified TRP node accessors map with cursor follower labels.
	 */
	function arts_extend_trp_node_accessors( $map ) {
		$map['cursor_follower_label'] = array(
			'selector'  => '[data-arts-cursor-follower-target*="label"]',
			'accessor'  => 'data-arts-cursor-follower-target',
			'attribute' => true,
		);

		return $map;
	}
}
