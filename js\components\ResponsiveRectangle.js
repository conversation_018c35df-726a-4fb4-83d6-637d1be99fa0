function _0x3b2f(_0x138e1c,_0x3e3f7a){const _0x393895=_0x3938();return _0x3b2f=function(_0x3b2f12,_0x7aff6c){_0x3b2f12=_0x3b2f12-0x10d;let _0x47bc42=_0x393895[_0x3b2f12];return _0x47bc42;},_0x3b2f(_0x138e1c,_0x3e3f7a);}const _0x3b695f=_0x3b2f;(function(_0x2a8a0b,_0x25963d){const _0xc4d71e=_0x3b2f,_0xf53fc2=_0x2a8a0b();while(!![]){try{const _0x4a50e5=-parseInt(_0xc4d71e(0x128))/0x1*(parseInt(_0xc4d71e(0x142))/0x2)+parseInt(_0xc4d71e(0x140))/0x3*(parseInt(_0xc4d71e(0x137))/0x4)+parseInt(_0xc4d71e(0x10e))/0x5*(parseInt(_0xc4d71e(0x118))/0x6)+-parseInt(_0xc4d71e(0x135))/0x7*(parseInt(_0xc4d71e(0x132))/0x8)+parseInt(_0xc4d71e(0x117))/0x9+parseInt(_0xc4d71e(0x13f))/0xa*(-parseInt(_0xc4d71e(0x110))/0xb)+-parseInt(_0xc4d71e(0x12a))/0xc*(-parseInt(_0xc4d71e(0x11d))/0xd);if(_0x4a50e5===_0x25963d)break;else _0xf53fc2['push'](_0xf53fc2['shift']());}catch(_0xd2fac8){_0xf53fc2['push'](_0xf53fc2['shift']());}}}(_0x3938,0xd31b2));export default class ResponsiveRectangle extends BaseComponent{constructor({name:_0x48b01e,loadInnerComponents:_0x5409fa,loadAfterSyncStyles:_0x5e1974,parent:_0x110a19,element:_0x5472fe}){const _0x13ab94=_0x3b2f;super({'name':_0x48b01e,'loadInnerComponents':_0x5409fa,'loadAfterSyncStyles':_0x5e1974,'parent':_0x110a19,'element':_0x5472fe,'defaults':{'fill':'none','stroke':_0x13ab94(0x13c)},'innerElements':{'svg':_0x13ab94(0x13d),'rect':_0x13ab94(0x11b)}}),this[_0x13ab94(0x126)]={'resize':this[_0x13ab94(0x115)]['bind'](this)},this[_0x13ab94(0x136)]={'width':0x0,'height':0x0,'borderWidth':0x0,'radius':0x0},this[_0x13ab94(0x12b)][_0x13ab94(0x111)](()=>{const _0x551282=_0x13ab94;this[_0x551282(0x127)]();});}[_0x3b695f(0x120)](){return new Promise(_0x32e87d=>{const _0x256491=_0x3b2f;this[_0x256491(0x141)][_0x256491(0x13e)]?this['_updateProps']()[_0x256491(0x113)](()=>this[_0x256491(0x130)]())[_0x256491(0x113)](()=>this[_0x256491(0x122)]())['finally'](()=>{const _0x1187a6=_0x256491;this[_0x1187a6(0x12f)](),_0x32e87d(!![]);}):_0x32e87d(!![]);});}[_0x3b695f(0x112)](){return new Promise(_0x2fe4a2=>{const _0x10962c=_0x3b2f;this[_0x10962c(0x124)](),_0x2fe4a2(!![]);});}['update'](){return new Promise(_0x12ffcc=>{const _0x23e636=_0x3b2f;this[_0x23e636(0x12e)]()[_0x23e636(0x113)](()=>this[_0x23e636(0x130)]())[_0x23e636(0x113)](()=>this[_0x23e636(0x122)]())[_0x23e636(0x111)](()=>_0x12ffcc(!![]));});}[_0x3b695f(0x12f)](){const _0x28696c=_0x3b695f;this[_0x28696c(0x13a)]=new ResizeObserver(app[_0x28696c(0x134)][_0x28696c(0x123)](this[_0x28696c(0x126)]['resize'],app[_0x28696c(0x134)]['getDebounceTime']())),this[_0x28696c(0x13a)][_0x28696c(0x121)](this[_0x28696c(0x141)]),this['resizeInstance']['observe'](this[_0x28696c(0x141)][_0x28696c(0x13e)]);}['_detachEvents'](){const _0x2d1ad6=_0x3b695f;this[_0x2d1ad6(0x13a)]&&this[_0x2d1ad6(0x13a)][_0x2d1ad6(0x10f)]();}[_0x3b695f(0x115)](){this['update']();}['_updateProps'](){return new Promise(_0x8a214c=>{const _0x51d5ab=_0x3b2f,{offsetWidth:_0xc908f7,offsetHeight:_0x21f4b5}=this[_0x51d5ab(0x141)]['parentElement'],_0xa3d2af=gsap[_0x51d5ab(0x125)](this['element'][_0x51d5ab(0x13e)],_0x51d5ab(0x133));Object[_0x51d5ab(0x12c)](this['_props'],{'width':_0xc908f7,'height':_0x21f4b5,'borderWidth':_0xa3d2af}),this[_0x51d5ab(0x136)][_0x51d5ab(0x11e)]=this[_0x51d5ab(0x119)](_0x51d5ab(0x10d)),_0x8a214c(!![]);});}[_0x3b695f(0x119)](_0x21fff0=_0x3b695f(0x10d)){const _0x154f2b=_0x3b695f;let _0x15593a=gsap[_0x154f2b(0x125)](this[_0x154f2b(0x141)]['parentElement'],_0x21fff0,'%');const _0x388bd0=Math[_0x154f2b(0x143)](this[_0x154f2b(0x136)][_0x154f2b(0x139)],this[_0x154f2b(0x136)][_0x154f2b(0x12d)]);_0x15593a=_0x15593a[_0x154f2b(0x131)]('%',''),_0x15593a=parseFloat(_0x15593a);if(_0x15593a>=0x32)return this[_0x154f2b(0x136)][_0x154f2b(0x139)]/0x2;;return _0x15593a=_0x15593a/0x64,_0x15593a=_0x15593a*_0x388bd0,+_0x15593a[_0x154f2b(0x138)](0x2);}[_0x3b695f(0x130)](){return new Promise(_0x5e73f5=>{const _0x18ffdb=_0x3b2f;if(this['elements'][_0x18ffdb(0x116)]&&this[_0x18ffdb(0x11a)][_0x18ffdb(0x116)][0x0]){const {width:_0x4e4cd2,height:_0x2c4107,borderWidth:_0x289fb7}=this[_0x18ffdb(0x136)];this[_0x18ffdb(0x11a)][_0x18ffdb(0x116)][0x0][_0x18ffdb(0x11f)](_0x18ffdb(0x12d),_0x4e4cd2),this[_0x18ffdb(0x11a)][_0x18ffdb(0x116)][0x0]['setAttribute'](_0x18ffdb(0x139),_0x2c4107),this[_0x18ffdb(0x11a)][_0x18ffdb(0x116)][0x0][_0x18ffdb(0x129)][_0x18ffdb(0x11c)](_0x18ffdb(0x13b),_0x289fb7+'px'),_0x5e73f5(!![]);}else _0x5e73f5(!![]);});}[_0x3b695f(0x122)](){return new Promise(_0x24eb3b=>{const _0x2937aa=_0x3b2f;if(this['elements'][_0x2937aa(0x114)]&&this[_0x2937aa(0x11a)]['rect'][0x0]){const {width:_0x200ea9,height:_0x24192c,borderWidth:_0x1b5cdd,radius:_0x40b976}=this['_props'];this[_0x2937aa(0x11a)][_0x2937aa(0x114)][0x0][_0x2937aa(0x11f)]('x',_0x1b5cdd/0x2),this[_0x2937aa(0x11a)][_0x2937aa(0x114)][0x0][_0x2937aa(0x11f)]('y',_0x1b5cdd/0x2),this[_0x2937aa(0x11a)]['rect'][0x0][_0x2937aa(0x11f)]('rx',_0x40b976),this[_0x2937aa(0x11a)][_0x2937aa(0x114)][0x0][_0x2937aa(0x11f)](_0x2937aa(0x12d),_0x200ea9-_0x1b5cdd),this['elements']['rect'][0x0]['setAttribute'](_0x2937aa(0x139),_0x24192c-_0x1b5cdd),_0x24eb3b(!![]);}else _0x24eb3b(!![]);});}}function _0x3938(){const _0x46d35e=['30316338mFswPf','radius','setAttribute','init','observe','_updateRect','debounce','_detachEvents','getProperty','_handlers','setup','1pLyOff','style','12FylVBC','dataReady','assign','width','_updateProps','_attachEvents','_updateSVG','replace','336nstwxn','borderWidth','utilities','266756gsMfnW','_props','96380XCCxvg','toFixed','height','resizeInstance','--border-width','currentColor','.js-responsive-rectangle__svg','parentElement','30ubKzyL','39ctcjWj','element','152610yklikD','max','borderRadius','330CpxBqN','disconnect','4181969rNjSDs','finally','destroy','then','rect','_onResize','svg','83277VyHzzM','93414JHZTgs','_getBorderRadius','elements','.js-responsive-rectangle__rect','setProperty'];_0x3938=function(){return _0x46d35e;};return _0x3938();}