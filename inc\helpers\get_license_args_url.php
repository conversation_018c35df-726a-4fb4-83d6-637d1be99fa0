<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_license_args_url' ) ) {
	/**
	 * Generates a URL with license arguments for use with EDD when making remote requests.
	 *
	 * @param string $url    The base URL to append the license arguments to.
	 * @param string $option The option name to retrieve the license key.
	 *
	 * @return string The URL with the license arguments or an empty string if parameters are missing.
	 * @deprecated 2.0.0 Use `Arts\Utilities\Utilities::get_license_args_url()` method instead.
	 */
	function arts_get_license_args_url( $url, $option ) {
		return Utilities::get_license_args_url( $url, $option );
	}
}
