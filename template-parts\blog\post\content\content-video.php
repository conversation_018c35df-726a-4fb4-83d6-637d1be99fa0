<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Content Post Type: Video
 */

$defaults = array(
	'id' => get_the_ID(),
);

$args = wp_parse_args( $args, $defaults );

$content      = apply_filters( 'the_content', get_the_content( null, false, $args['id'] ) );
$media        = '';
$allowed_html = array(
	'iframe' => array(
		'src'             => true,
		'height'          => true,
		'width'           => true,
		'frameborder'     => true,
		'allowfullscreen' => true,
	),
	'video'  => array(
		'loop'        => true,
		'autoplay'    => true,
		'muted'       => true,
		'playsinline' => true,
	),
);

// Only get video from the content if a playlist isn't present.
if ( strpos( $content, 'wp-playlist-script' ) === false ) {
	$media = get_media_embedded_in_content(
		$content,
		array(
			'video',
			'iframe',
		)
	);
}

?>

<?php if ( ! is_single( $args['id'] ) && is_array( $media ) && ! empty( $media ) ) : ?>
	<?php foreach ( $media as $media_html ) : ?>
		<div class="entry-video"><?php echo wp_kses( $media_html, $allowed_html ); ?></div>
	<?php endforeach; ?>
<?php endif; ?>
