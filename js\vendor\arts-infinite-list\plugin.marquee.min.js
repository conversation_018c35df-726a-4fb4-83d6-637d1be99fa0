"use strict";(self.webpackChunkArtsInfiniteList=self.webpackChunkArtsInfiniteList||[]).push([[532],{320:(e,t,s)=>{s.r(t),s.d(t,{default:()=>a});var i=s(199);const n={autoInit:!0,speed:2,onHoverSpeed:1};class a extends i.v{constructor({autoLoad:e=!1,container:t,options:s,controller:i,config:a}){super({autoLoad:e,container:t,options:s,controller:i,config:a,defaults:n}),this._handlers={mouseEnter:this._onMouseEnter.bind(this),mouseLeave:this._onMouseLeave.bind(this),beforeResize:this._onBeforeResize.bind(this),afterResize:this._onAfterResize.bind(this),animationFrame:this._onAnimationFrame.bind(this)},this.config.autoInit?this.init():(this._updateEvent(),this._onAnimationFrame())}init(){this.enabled||(this._updateEvent(),this._attachEvents(),this._attachResizeEvents(),this._attachToggleViewEvents(),this.enabled=!0,this.initialized=!0)}destroy(){this.enabled&&this.initialized&&(this._detachEvents(),this._detachResizeEvents(),this._detachToggleViewEvents(),this.enabled=!1)}enable(){!this.enabled&&this.initialized&&(this._updateEvent(),this._attachEvents(),this.enabled=!0)}disable(){this.enabled&&this.initialized&&(this._detachEvents(),this.enabled=!1)}setSpeed(e){this.config.speed=e,this.config.onHoverSpeed=e,this._updateEvent()}setNormalSpeed(e){this.config.speed=e,this._updateEvent()}setOnHoverSpeed(e){this.config.onHoverSpeed=e,this._updateEvent()}get event(){return this._event}set event(e){this._event=e}_updateEvent(e="speed"){const t={x:0,y:0,deltaX:0,deltaY:0,isDragging:!1};"horizontal"===this.options.direction?t.deltaX=-this.config[e]:t.deltaY=-this.config[e],this.event=t}_attachEvents(){const e={passive:!0};this.container.addEventListener("mouseenter",this._handlers.mouseEnter,e),this.container.addEventListener("touchstart",this._handlers.mouseEnter,e),this.container.addEventListener("mouseleave",this._handlers.mouseLeave,e),this.container.addEventListener("touchend",this._handlers.mouseLeave,e),gsap.ticker.add(this._handlers.animationFrame)}_detachEvents(){this.container.removeEventListener("mouseenter",this._handlers.mouseEnter),this.container.removeEventListener("touchstart",this._handlers.mouseEnter),this.container.removeEventListener("mouseleave",this._handlers.mouseLeave),this.container.removeEventListener("touchend",this._handlers.mouseLeave),gsap.ticker.remove(this._handlers.animationFrame)}_attachResizeEvents(){this.controller.on("beforeResize",this._handlers.beforeResize),this.controller.on("afterResize",this._handlers.afterResize)}_detachResizeEvents(){this.controller.off("beforeResize",this._handlers.beforeResize),this.controller.off("afterResize",this._handlers.afterResize)}_onAnimationFrame(){const e=Object.assign({},this.event);this.controller.scroll(e)}_onMouseEnter(){this._updateEvent("onHoverSpeed")}_onMouseLeave(){this._updateEvent()}_onBeforeResize(){this.disable()}_onAfterResize(){this.enable(),this.controller.updateAllLanes()}}}}]);