<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'after_setup_theme', 'arts_after_setup_theme' );
if ( ! function_exists( 'arts_after_setup_theme' ) ) {
	/**
	 * Sets up theme defaults and registers support for various WordPress features.
	 */
	function arts_after_setup_theme() {
		global $content_width;

		if ( ! isset( $content_width ) ) {
			$content_width = 960;
		}

		add_theme_support( 'automatic-feed-links' );
		add_theme_support( 'post-thumbnails' );
		add_theme_support(
			'html5',
			array(
				'comment-list',
				'comment-form',
				'search-form',
				'gallery',
				'caption',
			)
		);
		add_theme_support(
			'custom-logo',
			array(
				'flex-height' => true,
				'flex-width'  => true,
				'header-text' => array( 'logo__text' ),
			)
		);
		add_theme_support( 'customize-selective-refresh-widgets' );
		add_theme_support(
			'post-formats',
			array(
				'aside',
				'gallery',
				'link',
				'image',
				'quote',
				'status',
				'video',
				'audio',
				'chat',
			)
		);
		add_theme_support( 'title-tag' );

		add_theme_support(
			'woocommerce',
			array(
				'product_grid' => array(
					'default_rows'    => 3,
					'min_rows'        => 2,
					'max_rows'        => 8,
					'default_columns' => 3,
					'min_columns'     => 2,
					'max_columns'     => 4,
				),
			)
		);

		add_theme_support( 'wc-product-gallery-zoom' );
		add_theme_support( 'wc-product-gallery-lightbox' );
		add_theme_support( 'wc-product-gallery-slider' );
	}
}

add_action('init', 'arts_load_asli_theme_textdomain');
function arts_load_asli_theme_textdomain() {
	load_theme_textdomain('asli', ARTS_THEME_PATH . '/languages');
}
