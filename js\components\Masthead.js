function _0x4c87(){const _0x44054b=['3414230GPdMuy','contains','all','expo.inOut','start','hideMask','_createFixedScene','0.0','scheduleLateTask','center\x20center','progress','scrollHeight','postTask','fixed','page-footer','offsetHeight','animationScale','.js-masthead__animation-mask','element','matchMedia','running','fromTo','_shouldAnimateMask','toString','\x20top','371826qnQPVV','getElementById','.js-masthead__fixed-featured-media','masthead__overlay','clientHeight','top','set','animationMaskShape','.js-masthead__fixed-scroll-down','offsetTop','prepareAnimation','fixedAnimation','9eqKOAE','push','length','finally','destroy','autoAlpha','body','top-=','setup','animationFade','add','getComponentByName','AJAX','2948168AiBKoJ','fixedWrapper','forEach','76873UAIJFk','\x20bottom','4jCWbJl','options','string','749568AtnLCB','animateMask','style','.js-masthead__fixed-content','classList','rectangle','endBefore','timeline','_getFixedScrollingDistance','animateScale','.js-masthead__animation-fade','clearProps','componentsManager','.js-masthead__fixed-wrapper','create','animationMask','elements','1579035EQiQYY','documentElement','onUpdate','dataReady','offset','fixedScene','fixedFeaturedMedia','opacity','1244oKqVgt','top\x20bottom','querySelectorAll','kill','fixedContent','closest','innerHeight','.js-masthead__animation-mask-wrapper','power3.out','--opacity-animation','center','img,\x20video','function','fixedScrollDown','822jUThgo','runningSeamlessTransition','.js-ajax-transition-element'];_0x4c87=function(){return _0x44054b;};return _0x4c87();}function _0x5600(_0x4a56e1,_0x57f0a){const _0x4c87f5=_0x4c87();return _0x5600=function(_0x56003c,_0x5cf285){_0x56003c=_0x56003c-0x130;let _0x35168e=_0x4c87f5[_0x56003c];return _0x35168e;},_0x5600(_0x4a56e1,_0x57f0a);}const _0x570d7a=_0x5600;(function(_0x17d5e0,_0x3d98c0){const _0x226120=_0x5600,_0x311250=_0x17d5e0();while(!![]){try{const _0x4de50a=-parseInt(_0x226120(0x133))/0x1*(-parseInt(_0x226120(0x135))/0x2)+parseInt(_0x226120(0x15f))/0x3*(-parseInt(_0x226120(0x151))/0x4)+-parseInt(_0x226120(0x149))/0x5+-parseInt(_0x226120(0x138))/0x6+-parseInt(_0x226120(0x17b))/0x7+-parseInt(_0x226120(0x130))/0x8*(-parseInt(_0x226120(0x187))/0x9)+parseInt(_0x226120(0x162))/0xa;if(_0x4de50a===_0x3d98c0)break;else _0x311250['push'](_0x311250['shift']());}catch(_0x239341){_0x311250['push'](_0x311250['shift']());}}}(_0x4c87,0x457cf));export default class Masthead extends BaseComponent{constructor({name:_0x4b744a,loadInnerComponents:_0x4c4fc0,loadAfterSyncStyles:_0x1246ce,parent:_0x4dc5a1,element:_0x20aa47}){const _0x237bbf=_0x5600;super({'name':_0x4b744a,'loadInnerComponents':_0x4c4fc0,'loadAfterSyncStyles':_0x1246ce,'parent':_0x4dc5a1,'element':_0x20aa47,'defaults':{'fixed':![],'animationMask':_0x237bbf(0x180),'animationMaskShape':_0x237bbf(0x13d)},'innerElements':{'fixedWrapper':_0x237bbf(0x145),'fixedFeaturedMedia':_0x237bbf(0x17d),'fixedContent':_0x237bbf(0x13b),'fixedScrollDown':_0x237bbf(0x183),'animationMask':_0x237bbf(0x173),'animationScale':'.js-masthead__animation-scale','animationFade':_0x237bbf(0x142)}}),this[_0x237bbf(0x14c)][_0x237bbf(0x18a)](()=>{const _0x4ab6b8=_0x237bbf;this[_0x4ab6b8(0x18f)]();});}['init'](){return new Promise(_0x44eacb=>{const _0x24ee62=_0x5600;if(!!this[_0x24ee62(0x136)]['fixed']){const _0x702d45=app[_0x24ee62(0x144)][_0x24ee62(0x192)](_0x24ee62(0x193)),_0x9d6f29=typeof this[_0x24ee62(0x136)][_0x24ee62(0x16f)]['matchMedia']===_0x24ee62(0x137)?this['options']['fixed'][_0x24ee62(0x175)]:'all';this['mm']=gsap[_0x24ee62(0x175)](),_0x702d45&&_0x702d45[_0x24ee62(0x176)]?_0x702d45[_0x24ee62(0x16a)](()=>new Promise(_0x3e173d=>{const _0x3d182f=_0x24ee62;this['mm'][_0x3d182f(0x191)](_0x9d6f29,()=>{const _0x499540=_0x3d182f;this[_0x499540(0x168)]();}),_0x3e173d(!![]);},_0x24ee62(0x13e))):this['mm'][_0x24ee62(0x191)](_0x9d6f29,()=>{this['_createFixedScene']();}),_0x44eacb(!![]);}else _0x44eacb(!![]);});}[_0x570d7a(0x18b)](){return new Promise(_0x590dac=>{const _0x1bb421=_0x5600,_0x17e7c2=[];if(this[_0x1bb421(0x14e)]&&typeof this['fixedScene'][_0x1bb421(0x154)]===_0x1bb421(0x15d)){const _0x58c3a3=scheduler[_0x1bb421(0x16e)](()=>{const _0x540127=_0x1bb421;this[_0x540127(0x14e)][_0x540127(0x154)]();});_0x17e7c2['push'](_0x58c3a3);}if(this[_0x1bb421(0x186)]&&typeof this['fixedAnimation'][_0x1bb421(0x154)]===_0x1bb421(0x15d)){const _0x447fed=scheduler[_0x1bb421(0x16e)](()=>{const _0x430c96=_0x1bb421;this[_0x430c96(0x186)][_0x430c96(0x154)]();});_0x17e7c2[_0x1bb421(0x188)](_0x447fed);}if(this['mm']&&typeof this['mm'][_0x1bb421(0x154)]==='function'){const _0xa58356=scheduler[_0x1bb421(0x16e)](()=>{const _0x4c9914=_0x1bb421;this['mm'][_0x4c9914(0x154)]();});_0x17e7c2[_0x1bb421(0x188)](_0xa58356);}Promise[_0x1bb421(0x164)](_0x17e7c2)[_0x1bb421(0x18a)](()=>_0x590dac(!![]));});}[_0x570d7a(0x185)](){return new Promise(_0x56c883=>{const _0x3dff59=_0x5600,_0x19952e=gsap['timeline']({'onComplete':()=>_0x56c883(!![])});this[_0x3dff59(0x148)]['animationFade']['length']&&this['elements'][_0x3dff59(0x190)][_0x3dff59(0x132)](_0x59f659=>{const _0x141f54=_0x3dff59,_0x476dc3={};_0x59f659[_0x141f54(0x13c)][_0x141f54(0x163)](_0x141f54(0x17e))?_0x59f659[_0x141f54(0x13a)]['setProperty'](_0x141f54(0x15a),_0x141f54(0x169)):(_0x476dc3[_0x141f54(0x18c)]=0x0,_0x19952e[_0x141f54(0x181)](_0x59f659,_0x476dc3));}),this[_0x3dff59(0x148)][_0x3dff59(0x147)][_0x3dff59(0x189)]&&!!this[_0x3dff59(0x136)][_0x3dff59(0x147)]&&this['elements'][_0x3dff59(0x147)][_0x3dff59(0x132)](_0x2240de=>{const _0x2a446d=_0x3dff59;this[_0x2a446d(0x178)](_0x2240de)&&_0x19952e[_0x2a446d(0x167)](_0x2240de,{'clearProps':'','duration':0x0,'animateTo':this[_0x2a446d(0x136)][_0x2a446d(0x147)],'shape':this['options'][_0x2a446d(0x182)],'scaleInner':![]});}),this['elements'][_0x3dff59(0x172)][_0x3dff59(0x189)]&&_0x19952e['set'](this[_0x3dff59(0x148)][_0x3dff59(0x172)],{'scale':0x0,'transformOrigin':_0x3dff59(0x16b)});});}['getRevealAnimation'](){const _0x55cc88=_0x570d7a,_0x3d3292=gsap['timeline']({'paused':!![]});return this[_0x55cc88(0x148)][_0x55cc88(0x147)]['length']&&!!this[_0x55cc88(0x136)][_0x55cc88(0x147)]&&this[_0x55cc88(0x148)]['animationMask'][_0x55cc88(0x132)]((_0x5a2a78,_0xa0c16)=>{const _0x2370e0=_0x55cc88;this['_shouldAnimateMask'](_0x5a2a78)&&_0x3d3292[_0x2370e0(0x139)](_0x5a2a78,{'animateFrom':this[_0x2370e0(0x136)][_0x2370e0(0x147)],'shape':this[_0x2370e0(0x136)]['animationMaskShape'],'duration':1.2,'ease':_0x2370e0(0x165),'scaleInner':_0x2370e0(0x158),'scale':1.1},'<');}),this[_0x55cc88(0x148)]['animationFade'][_0x55cc88(0x189)]&&this[_0x55cc88(0x148)][_0x55cc88(0x190)][_0x55cc88(0x132)]((_0x1a2320,_0x3c3334)=>{const _0x153c3c=_0x55cc88,_0x24609f={'duration':1.2,'ease':'power3.out'};if(_0x1a2320['classList']['contains']('masthead__overlay')){let _0x4eba96=0x0;_0x24609f[_0x153c3c(0x143)]=_0x153c3c(0x15a),_0x24609f[_0x153c3c(0x14b)]=function(){const _0x41246a=_0x153c3c,_0x433bc9=this[_0x41246a(0x16c)]();_0x433bc9>_0x4eba96&&gsap[_0x41246a(0x181)](_0x1a2320,{'--opacity-animation':_0x433bc9}),_0x4eba96=_0x433bc9;};}else _0x24609f[_0x153c3c(0x18c)]=0x1,_0x24609f[_0x153c3c(0x143)]='opacity,visibility';_0x3d3292['to'](_0x1a2320,_0x24609f,'<');}),this[_0x55cc88(0x148)]['animationScale'][_0x55cc88(0x189)]&&this['elements'][_0x55cc88(0x172)]['forEach']((_0x1593aa,_0x392785)=>{const _0x2ce642=_0x55cc88;_0x3d3292[_0x2ce642(0x141)](_0x1593aa,{'ease':_0x2ce642(0x159),'duration':1.2,'animateFrom':_0x2ce642(0x15b)},'<');}),_0x3d3292;}[_0x570d7a(0x178)](_0x1eacfa){const _0x1d8419=_0x570d7a,_0x34182e=app[_0x1d8419(0x144)]['getComponentByName'](_0x1d8419(0x193));if(_0x34182e){const _0x2bcaf0=!!_0x34182e[_0x1d8419(0x160)];if(_0x2bcaf0&&_0x1eacfa[_0x1d8419(0x156)](_0x1d8419(0x161)))return![];return!![];}return!![];}['_createFixedScene'](){const _0x400a43=_0x570d7a,_0x221e3e=this['_getFixedAnimation'](),_0x30cea2=document[_0x400a43(0x17c)](_0x400a43(0x170)),_0x33d0bd=_0x30cea2?_0x30cea2:undefined,_0x432e6b=_0x30cea2?_0x400a43(0x152):()=>this[_0x400a43(0x140)]();this['fixedScene']=ScrollTrigger['create']({'trigger':this[_0x400a43(0x174)],'start':_0x400a43(0x18e)+getComputedStyle(document[_0x400a43(0x14a)])['marginTop']+'\x20top','end':_0x432e6b,'endTrigger':_0x33d0bd,'pin':!![],'pinSpacing':![],'invalidateOnRefresh':!![],'scrub':!![]}),this[_0x400a43(0x186)]=ScrollTrigger[_0x400a43(0x146)]({'trigger':document[_0x400a43(0x14a)],'start':()=>_0x400a43(0x18e)+getComputedStyle(document[_0x400a43(0x14a)])['marginTop']+_0x400a43(0x17a),'end':()=>this[_0x400a43(0x174)][_0x400a43(0x184)]+this[_0x400a43(0x174)][_0x400a43(0x171)]*1.5+_0x400a43(0x134),'animation':_0x221e3e,'invalidateOnRefresh':!![],'scrub':!![]});}['_getFixedAnimation'](){const _0x293df9=_0x570d7a,_0x2de66b=gsap[_0x293df9(0x13f)]({'paused':!![]}),_0xbe290=typeof this[_0x293df9(0x136)][_0x293df9(0x16f)][_0x293df9(0x14d)]==='string'?''+this['options'][_0x293df9(0x16f)][_0x293df9(0x14d)]:'0%',_0x43c62e=typeof this[_0x293df9(0x136)]['fixed'][_0x293df9(0x14d)]===_0x293df9(0x137)?'-'+this[_0x293df9(0x136)][_0x293df9(0x16f)][_0x293df9(0x14d)]:'0%';this[_0x293df9(0x148)][_0x293df9(0x15e)][0x0]&&_0x2de66b['fromTo'](this['elements'][_0x293df9(0x15e)][0x0],{'autoAlpha':0x1},{'autoAlpha':0x0},_0x293df9(0x166));this[_0x293df9(0x148)][_0x293df9(0x155)][0x0]&&_0x2de66b[_0x293df9(0x177)](this[_0x293df9(0x148)]['fixedContent'][0x0],{'autoAlpha':0x1},{'autoAlpha':0x0},'start');this['elements']['fixedWrapper'][0x0]&&_0x2de66b[_0x293df9(0x177)](this[_0x293df9(0x148)][_0x293df9(0x131)][0x0],{'y':'0%'},{'y':_0x43c62e},_0x293df9(0x166));if(this[_0x293df9(0x148)][_0x293df9(0x14f)][0x0]){const _0x314041=!!this[_0x293df9(0x136)]['fixed']['scale']?parseFloat(this[_0x293df9(0x136)][_0x293df9(0x16f)]['scale'][_0x293df9(0x179)]()):0x1,_0x20764e=!!this[_0x293df9(0x136)][_0x293df9(0x16f)]['opacity']?parseFloat(this[_0x293df9(0x136)][_0x293df9(0x16f)][_0x293df9(0x150)][_0x293df9(0x179)]()):0x0,_0x25bb0b=this[_0x293df9(0x148)][_0x293df9(0x14f)][0x0][_0x293df9(0x153)](_0x293df9(0x15c));_0x2de66b[_0x293df9(0x177)](_0x25bb0b||this[_0x293df9(0x148)][_0x293df9(0x14f)][0x0],{'y':'0%','scale':0x1,'autoAlpha':0x1},{'y':_0xbe290,'scale':_0x314041,'autoAlpha':_0x20764e,'transformOrigin':'center\x20center'},_0x293df9(0x166));}return _0x2de66b;}[_0x570d7a(0x140)](){const _0x4767a9=_0x570d7a;return Math['max'](document[_0x4767a9(0x18d)][_0x4767a9(0x16d)],document[_0x4767a9(0x14a)][_0x4767a9(0x16d)],document[_0x4767a9(0x18d)][_0x4767a9(0x171)],document[_0x4767a9(0x14a)][_0x4767a9(0x171)],document[_0x4767a9(0x18d)]['clientHeight'],document['documentElement'][_0x4767a9(0x17f)])-window[_0x4767a9(0x157)];}}