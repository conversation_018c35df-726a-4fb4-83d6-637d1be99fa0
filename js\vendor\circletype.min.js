/*!
 * circletype 2.3.2
 * A JavaScript library that lets you curve type on the web.
 * Copyright © 2014-2022 <PERSON>
 * Licensed MIT
 * https://github.com/peterhry/CircleType#readme
 */
!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.CircleType=n():t.CircleType=n()}(window,function(){return r={},o.m=e=[function(t,n,e){var r=e(13)("wks"),o=e(12),i=e(1).Symbol,u="function"==typeof i;(t.exports=function(t){return r[t]||(r[t]=u&&i[t]||(u?i:o)("Symbol."+t))}).store=r},function(t,n){t=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)},function(t,n){t=t.exports={version:"2.6.12"};"number"==typeof __e&&(__e=t)},function(t,n,e){var r=e(4),o=e(11);t.exports=e(6)?function(t,n,e){return r.f(t,n,o(1,e))}:function(t,n,e){return t[n]=e,t}},function(t,n,e){var r=e(5),o=e(33),i=e(34),u=Object.defineProperty;n.f=e(6)?Object.defineProperty:function(t,n,e){if(r(t),n=i(n,!0),r(e),o)try{return u(t,n,e)}catch(t){}if("get"in e||"set"in e)throw TypeError("Accessors not supported!");return"value"in e&&(t[n]=e.value),t}},function(t,n,e){var r=e(10);t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},function(t,n,e){t.exports=!e(18)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(t,n){var e={}.hasOwnProperty;t.exports=function(t,n){return e.call(t,n)}},function(t,n){var e=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(0<t?r:e)(t)}},function(t,n){t.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},function(t,n){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,n){t.exports=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}}},function(t,n){var e=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++e+r).toString(36))}},function(t,n,e){var r=e(2),o=e(1),i=o["__core-js_shared__"]||(o["__core-js_shared__"]={});(t.exports=function(t,n){return i[t]||(i[t]=void 0!==n?n:{})})("versions",[]).push({version:r.version,mode:e(16)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(t,n){t.exports={}},function(t,n,e){var r=e(13)("keys"),o=e(12);t.exports=function(t){return r[t]||(r[t]=o(t))}},function(t,n){t.exports=!1},function(t,n,e){var d=e(1),v=e(2),y=e(3),m=e(20),g=e(21),_=function(t,n,e){var r,o,i,u=t&_.F,c=t&_.G,a=t&_.S,f=t&_.P,s=t&_.B,l=c?d:a?d[n]||(d[n]={}):(d[n]||{}).prototype,p=c?v:v[n]||(v[n]={}),h=p.prototype||(p.prototype={});for(r in c&&(e=n),e)o=((i=!u&&l&&void 0!==l[r])?l:e)[r],i=s&&i?g(o,d):f&&"function"==typeof o?g(Function.call,o):o,l&&m(l,r,o,t&_.U),p[r]!=o&&y(p,r,i),f&&h[r]!=o&&(h[r]=o)};d.core=v,_.F=1,_.G=2,_.S=4,_.P=8,_.B=16,_.W=32,_.U=64,_.R=128,t.exports=_},function(t,n){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,n,e){var r=e(10),o=e(1).document,i=r(o)&&r(o.createElement);t.exports=function(t){return i?o.createElement(t):{}}},function(t,n,e){var i=e(1),u=e(3),c=e(7),a=e(12)("src"),r=e(35),f=(""+r).split("toString");e(2).inspectSource=function(t){return r.call(t)},(t.exports=function(t,n,e,r){var o="function"==typeof e;o&&(c(e,"name")||u(e,"name",n)),t[n]!==e&&(o&&(c(e,a)||u(e,a,t[n]?""+t[n]:f.join(String(n)))),t===i?t[n]=e:r?t[n]?t[n]=e:u(t,n,e):(delete t[n],u(t,n,e)))})(Function.prototype,"toString",function(){return"function"==typeof this&&this[a]||r.call(this)})},function(t,n,e){var i=e(36);t.exports=function(r,o,t){if(i(r),void 0===o)return r;switch(t){case 1:return function(t){return r.call(o,t)};case 2:return function(t,n){return r.call(o,t,n)};case 3:return function(t,n,e){return r.call(o,t,n,e)}}return function(){return r.apply(o,arguments)}}},function(t,n,e){var r=e(42),o=e(9);t.exports=function(t){return r(o(t))}},function(t,n){var e={}.toString;t.exports=function(t){return e.call(t).slice(8,-1)}},function(t,n,e){var r=e(8),o=Math.min;t.exports=function(t){return 0<t?o(r(t),9007199254740991):0}},function(t,n){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(t,n,e){var r=e(4).f,o=e(7),i=e(0)("toStringTag");t.exports=function(t,n,e){t&&!o(t=e?t:t.prototype,i)&&r(t,i,{configurable:!0,value:n})}},function(t,n,e){var r=e(9);t.exports=function(t){return Object(r(t))}},function(t,n,e){e(29);e=e(54).default;t.exports=e},function(t,n,e){e(30),e(47),t.exports=e(2).Array.from},function(t,n,e){"use strict";var r=e(31)(!0);e(32)(String,"String",function(t){this._t=String(t),this._i=0},function(){var t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(n=r(t,n),this._i+=n.length,{value:n,done:!1})})},function(t,n,e){var u=e(8),c=e(9);t.exports=function(i){return function(t,n){var e,r=String(c(t)),o=u(n),t=r.length;return o<0||t<=o?i?"":void 0:(n=r.charCodeAt(o))<55296||56319<n||o+1===t||(e=r.charCodeAt(o+1))<56320||57343<e?i?r.charAt(o):n:i?r.slice(o,o+2):e-56320+(n-55296<<10)+65536}}},function(t,n,e){"use strict";function g(){return this}var _=e(16),b=e(17),x=e(20),w=e(3),O=e(14),S=e(37),j=e(26),M=e(46),A=e(0)("iterator"),P=!([].keys&&"next"in[].keys());t.exports=function(t,n,e,r,o,i,u){S(e,n,r);function c(t){if(!P&&t in d)return d[t];switch(t){case"keys":case"values":return function(){return new e(this,t)}}return function(){return new e(this,t)}}var a,f,s,l=n+" Iterator",p="values"==o,h=!1,d=t.prototype,v=d[A]||d["@@iterator"]||o&&d[o],y=v||c(o),m=o?p?c("entries"):y:void 0,r="Array"==n&&d.entries||v;if(r&&(s=M(r.call(new t)))!==Object.prototype&&s.next&&(j(s,l,!0),_||"function"==typeof s[A]||w(s,A,g)),p&&v&&"values"!==v.name&&(h=!0,y=function(){return v.call(this)}),_&&!u||!P&&!h&&d[A]||w(d,A,y),O[n]=y,O[l]=g,o)if(a={values:p?y:c("values"),keys:i?y:c("keys"),entries:m},u)for(f in a)f in d||x(d,f,a[f]);else b(b.P+b.F*(P||h),n,a);return a}},function(t,n,e){t.exports=!e(6)&&!e(18)(function(){return 7!=Object.defineProperty(e(19)("div"),"a",{get:function(){return 7}}).a})},function(t,n,e){var o=e(10);t.exports=function(t,n){if(!o(t))return t;var e,r;if(n&&"function"==typeof(e=t.toString)&&!o(r=e.call(t)))return r;if("function"==typeof(e=t.valueOf)&&!o(r=e.call(t)))return r;if(!n&&"function"==typeof(e=t.toString)&&!o(r=e.call(t)))return r;throw TypeError("Can't convert object to primitive value")}},function(t,n,e){t.exports=e(13)("native-function-to-string",Function.toString)},function(t,n){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,n,e){"use strict";var r=e(38),o=e(11),i=e(26),u={};e(3)(u,e(0)("iterator"),function(){return this}),t.exports=function(t,n,e){t.prototype=r(u,{next:o(1,e)}),i(t,n+" Iterator")}},function(t,n,e){function r(){}var o=e(5),i=e(39),u=e(25),c=e(15)("IE_PROTO"),a=function(){var t=e(19)("iframe"),n=u.length;for(t.style.display="none",e(45).appendChild(t),t.src="javascript:",(t=t.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),a=t.F;n--;)delete a.prototype[u[n]];return a()};t.exports=Object.create||function(t,n){var e;return null!==t?(r.prototype=o(t),e=new r,r.prototype=null,e[c]=t):e=a(),void 0===n?e:i(e,n)}},function(t,n,e){var u=e(4),c=e(5),a=e(40);t.exports=e(6)?Object.defineProperties:function(t,n){c(t);for(var e,r=a(n),o=r.length,i=0;i<o;)u.f(t,e=r[i++],n[e]);return t}},function(t,n,e){var r=e(41),o=e(25);t.exports=Object.keys||function(t){return r(t,o)}},function(t,n,e){var u=e(7),c=e(22),a=e(43)(!1),f=e(15)("IE_PROTO");t.exports=function(t,n){var e,r=c(t),o=0,i=[];for(e in r)e!=f&&u(r,e)&&i.push(e);for(;n.length>o;)u(r,e=n[o++])&&(~a(i,e)||i.push(e));return i}},function(t,n,e){var r=e(23);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},function(t,n,e){var a=e(22),f=e(24),s=e(44);t.exports=function(c){return function(t,n,e){var r,o=a(t),i=f(o.length),u=s(e,i);if(c&&n!=n){for(;u<i;)if((r=o[u++])!=r)return!0}else for(;u<i;u++)if((c||u in o)&&o[u]===n)return c||u||0;return!c&&-1}}},function(t,n,e){var r=e(8),o=Math.max,i=Math.min;t.exports=function(t,n){return(t=r(t))<0?o(t+n,0):i(t,n)}},function(t,n,e){e=e(1).document;t.exports=e&&e.documentElement},function(t,n,e){var r=e(7),o=e(27),i=e(15)("IE_PROTO"),u=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=o(t),r(t,i)?t[i]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?u:null}},function(t,n,e){"use strict";var l=e(21),r=e(17),p=e(27),h=e(48),d=e(49),v=e(24),y=e(50),m=e(51);r(r.S+r.F*!e(53)(function(t){Array.from(t)}),"Array",{from:function(t){var n,e,r,o,i=p(t),u="function"==typeof this?this:Array,c=arguments.length,a=1<c?arguments[1]:void 0,f=void 0!==a,s=0,t=m(i);if(f&&(a=l(a,2<c?arguments[2]:void 0,2)),null==t||u==Array&&d(t))for(e=new u(n=v(i.length));s<n;s++)y(e,s,f?a(i[s],s):i[s]);else for(o=t.call(i),e=new u;!(r=o.next()).done;s++)y(e,s,f?h(o,a,[r.value,s],!0):r.value);return e.length=s,e}})},function(t,n,e){var i=e(5);t.exports=function(t,n,e,r){try{return r?n(i(e)[0],e[1]):n(e)}catch(n){var o=t.return;throw void 0!==o&&i(o.call(t)),n}}},function(t,n,e){var r=e(14),o=e(0)("iterator"),i=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||i[o]===t)}},function(t,n,e){"use strict";var r=e(4),o=e(11);t.exports=function(t,n,e){n in t?r.f(t,n,o(0,e)):t[n]=e}},function(t,n,e){var r=e(52),o=e(0)("iterator"),i=e(14);t.exports=e(2).getIteratorMethod=function(t){if(null!=t)return t[o]||t["@@iterator"]||i[r(t)]}},function(t,n,e){var r=e(23),o=e(0)("toStringTag"),i="Arguments"==r(function(){return arguments}());t.exports=function(t){var n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(t=function(t,n){try{return t[n]}catch(t){}}(n=Object(t),o))?t:i?r(n):"Object"==(t=r(n))&&"function"==typeof n.callee?"Arguments":t}},function(t,n,e){var i=e(0)("iterator"),u=!1;try{var r=[7][i]();r.return=function(){u=!0},Array.from(r,function(){throw 2})}catch(t){}t.exports=function(t,n){if(!n&&!u)return!1;var e=!1;try{var r=[7],o=r[i]();o.next=function(){return{done:e=!0}},r[i]=function(){return o},t(r)}catch(t){}return e}},function(t,n,e){"use strict";e.r(n);var o=function(t){var n=t.getBoundingClientRect();return{height:t.offsetHeight,left:n.left+window.pageXOffset,top:n.top+window.pageYOffset,width:t.offsetWidth}};function i(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function s(t,n){return t*(1-Math.cos(n/2*l))}var l=Math.PI/180,p=180/Math.PI;function r(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var u=Math.PI,c=Math.max,h=Math.min,e=(r((e=a).prototype,[{key:"radius",value:function(t){return void 0!==t?(this._radius=c(this._minRadius,t),this._invalidate(),this):this._radius}},{key:"dir",value:function(t){return void 0!==t?(this._dir=t,this._invalidate(),this):this._dir}},{key:"forceWidth",value:function(t){return void 0!==t?(this._forceWidth=t,this._invalidate(),this):this._forceWidth}},{key:"forceHeight",value:function(t){return void 0!==t?(this._forceHeight=t,this._invalidate(),this):this._forceHeight}},{key:"refresh",value:function(){return this._invalidate()}},{key:"destroy",value:function(){return this.element.innerHTML=this.originalHTML,this}},{key:"_invalidate",value:function(){var t=this;return cancelAnimationFrame(this._raf),this._raf=requestAnimationFrame(function(){t._layout()}),this}},{key:"_layout",value:function(){var e,t,r=this,n=this._radius,o=this._dir,i=-1===o?-n+this._lineHeight:n,u="center ".concat(i/this._fontSize,"em"),c=n-this._lineHeight,i=(i=this._metrics,e=c,i.reduce(function(t,n){n=n.width/e*p;return{"θ":t.θ+n,rotations:t.rotations.concat([t.θ+n/2])}},{"θ":0,rotations:[]})),a=i.rotations,f=i.θ;return this._letters.forEach(function(t,n){var e=t.style,t=(-.5*f+a[n])*o,n=-.5*r._metrics[n].width/r._fontSize,t="translateX(".concat(n,"em) rotate(").concat(t,"deg)");e.position="absolute",e.bottom=-1===o?0:"auto",e.left="50%",e.transform=t,e.transformOrigin=u,e.webkitTransform=t,e.webkitTransformOrigin=u}),this._forceHeight&&(t=180<f?s(n,f):s(c,f)+this._lineHeight,this.container.style.height="".concat(t/this._fontSize,"em")),this._forceWidth&&(t=n,n=h(180,f),n=2*t*Math.sin(n/2*l),this.container.style.width="".concat(n/this._fontSize,"em")),this}}]),Object.defineProperty(e,"prototype",{writable:!1}),a);function a(t,n){!function(t){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this),this.element=t,this.originalHTML=this.element.innerHTML;var e=document.createElement("div"),r=document.createDocumentFragment();e.setAttribute("aria-label",t.innerText),e.style.position="relative",this.container=e,this._letters=function(t,n){var e=document.createElement("span");e.style.display="inline-block";var r,t=t.innerText;return(n?n(t):function(t){if(Array.isArray(t))return i(t)}(r=t)||function(){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}()||function(t){if(t){if("string"==typeof t)return i(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(t,void 0):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).map(function(t){var n=e.cloneNode();return n.insertAdjacentHTML("afterbegin"," "===t?"&nbsp;":t),n})}(t,n),this._letters.forEach(function(t){return r.appendChild(t)}),e.appendChild(r),this.element.innerHTML="",this.element.appendChild(e);n=window.getComputedStyle(this.element),e=n.fontSize,n=n.lineHeight;this._fontSize=parseFloat(e),this._lineHeight=parseFloat(n)||this._fontSize,this._metrics=this._letters.map(o);n=this._metrics.reduce(function(t,n){return t+n.width},0);this._minRadius=n/u/2+this._lineHeight,this._dir=1,this._forceWidth=!1,this._forceHeight=!0,this._radius=this._minRadius,this._invalidate()}n.default=e}],o.c=r,o.d=function(t,n,e){o.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:e})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(n,t){if(1&t&&(n=o(n)),8&t)return n;if(4&t&&"object"==typeof n&&n&&n.__esModule)return n;var e=Object.create(null);if(o.r(e),Object.defineProperty(e,"default",{enumerable:!0,value:n}),2&t&&"string"!=typeof n)for(var r in n)o.d(e,r,function(t){return n[t]}.bind(null,r));return e},o.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(n,"a",n),n},o.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},o.p="",o(o.s=28);function o(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,o),n.l=!0,n.exports}var e,r});