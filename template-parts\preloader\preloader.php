<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$preloader_loader_style          = Utilities::get_kit_settings( 'preloader_loader_style', 'counter' );
$preloader_counter_enabled       = $preloader_loader_style === 'counter' && Utilities::get_kit_settings( 'preloader_counter_enabled', true );
$preloader_custom_loader_enabled = $preloader_loader_style === 'custom_image';

$defaults = array(
	'beforeLoadingText'            => Utilities::get_kit_settings(
		'preloader_before_loading_text',
		esc_html__( 'Loading...', 'asli' )
	),
	'afterLoadingText'             => Utilities::get_kit_settings(
		'preloader_after_loading_text',
		esc_html__( 'Filmmaker & Photographer', 'asli' )
	),
	'headingText'                  => Utilities::get_kit_settings(
		'preloader_heading_text',
		esc_html__( 'Asli Wells', 'asli' )
	),
	'counterEnabled'               => $preloader_counter_enabled,
	'customLoaderEnabled'          => $preloader_custom_loader_enabled,
	'slidingImagesEnabled'         => Utilities::get_kit_settings( 'preloader_sliding_images_enabled', true ),
	'slidingImages'                => Utilities::get_kit_settings( 'preloader_sliding_images' ),
	'slidingImagesSize'            => Utilities::get_kit_settings( 'preloader_sliding_images_size', 'full' ),
	'slidingImagesCustomDimension' => Utilities::get_kit_settings( 'preloader_sliding_images_custom_dimension', null ),
);
$args     = wp_parse_args( $args, $defaults );

$attributes = array(
	'class' => array(
		'section-fullheight',
		'text-center',
	),
	'id'    => 'page-preloader',
);

$attributes = Utilities::get_component_attributes(
	$attributes,
	array(
		'name'         => 'Preloader',
		'hasAnimation' => true,
	)
);

?>

<div <?php Utilities::print_attributes( $attributes ); ?>>
	<div class="preloader__wrapper preloader__lcp-visible js-preloader__wrapper">
		<?php get_template_part( 'template-parts/preloader/partials/fast-sliding-images', null, $args ); ?>
		<?php get_template_part( 'template-parts/preloader/partials/heading', null, $args ); ?>
		<?php get_template_part( 'template-parts/preloader/partials/counter', null, $args ); ?>
		<?php get_template_part( 'template-parts/preloader/partials/custom-loader', null, $args ); ?>
		<?php get_template_part( 'template-parts/preloader/partials/bottom-content', null, $args ); ?>
	</div>
</div>
