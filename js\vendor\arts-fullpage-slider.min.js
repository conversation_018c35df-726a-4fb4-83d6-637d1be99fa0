(()=>{var t={110:t=>{function e(t,e,i){var n,s,o,r,a;function h(){var l=Date.now()-r;l<e&&l>=0?n=setTimeout(h,e-l):(n=null,i||(a=t.apply(o,s),o=s=null))}null==e&&(e=100);var l=function(){o=this,s=arguments,r=Date.now();var l=i&&!n;return n||(n=setTimeout(h,e)),l&&(a=t.apply(o,s),o=s=null),a};return l.clear=function(){n&&(clearTimeout(n),n=null)},l.flush=function(){n&&(a=t.apply(o,s),o=s=null,clearTimeout(n),n=null)},l}e.debounce=e,t.exports=e},417:t=>{"use strict";var e=function(t){return function(t){return!!t&&"object"==typeof t}(t)&&!function(t){var e=Object.prototype.toString.call(t);return"[object RegExp]"===e||"[object Date]"===e||function(t){return t.$$typeof===i}(t)}(t)},i="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(t,e){return!1!==e.clone&&e.isMergeableObject(t)?a((i=t,Array.isArray(i)?[]:{}),t,e):t;var i}function s(t,e,i){return t.concat(e).map((function(t){return n(t,i)}))}function o(t){return Object.keys(t).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return t.propertyIsEnumerable(e)})):[]}(t))}function r(t,e){try{return e in t}catch(t){return!1}}function a(t,i,h){(h=h||{}).arrayMerge=h.arrayMerge||s,h.isMergeableObject=h.isMergeableObject||e,h.cloneUnlessOtherwiseSpecified=n;var l=Array.isArray(i);return l===Array.isArray(t)?l?h.arrayMerge(t,i,h):function(t,e,i){var s={};return i.isMergeableObject(t)&&o(t).forEach((function(e){s[e]=n(t[e],i)})),o(e).forEach((function(o){(function(t,e){return r(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))})(t,o)||(r(t,o)&&i.isMergeableObject(e[o])?s[o]=function(t,e){if(!e.customMerge)return a;var i=e.customMerge(t);return"function"==typeof i?i:a}(o,i)(t[o],e[o],i):s[o]=n(e[o],i))})),s}(t,i,h):n(i,h)}a.all=function(t,e){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce((function(t,i){return a(t,i,e)}),{})};var h=a;t.exports=h},100:t=>{"use strict";var e,i="object"==typeof Reflect?Reflect:null,n=i&&"function"==typeof i.apply?i.apply:function(t,e,i){return Function.prototype.apply.call(t,e,i)};e=i&&"function"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var s=Number.isNaN||function(t){return t!=t};function o(){o.init.call(this)}t.exports=o,t.exports.once=function(t,e){return new Promise((function(i,n){function s(i){t.removeListener(e,o),n(i)}function o(){"function"==typeof t.removeListener&&t.removeListener("error",s),i([].slice.call(arguments))}_(t,e,o,{once:!0}),"error"!==e&&function(t,e,i){"function"==typeof t.on&&_(t,"error",e,{once:!0})}(t,s)}))},o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var r=10;function a(t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function h(t){return void 0===t._maxListeners?o.defaultMaxListeners:t._maxListeners}function l(t,e,i,n){var s,o,r,l;if(a(i),void 0===(o=t._events)?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,i.listener?i.listener:i),o=t._events),r=o[e]),void 0===r)r=o[e]=i,++t._eventsCount;else if("function"==typeof r?r=o[e]=n?[i,r]:[r,i]:n?r.unshift(i):r.push(i),(s=h(t))>0&&r.length>s&&!r.warned){r.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+r.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=t,c.type=e,c.count=r.length,l=c,console&&console.warn&&console.warn(l)}return t}function c(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function u(t,e,i){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:i},s=c.bind(n);return s.listener=i,n.wrapFn=s,s}function d(t,e,i){var n=t._events;if(void 0===n)return[];var s=n[e];return void 0===s?[]:"function"==typeof s?i?[s.listener||s]:[s]:i?function(t){for(var e=new Array(t.length),i=0;i<e.length;++i)e[i]=t[i].listener||t[i];return e}(s):m(s,s.length)}function p(t){var e=this._events;if(void 0!==e){var i=e[t];if("function"==typeof i)return 1;if(void 0!==i)return i.length}return 0}function m(t,e){for(var i=new Array(e),n=0;n<e;++n)i[n]=t[n];return i}function _(t,e,i,n){if("function"==typeof t.on)n.once?t.once(e,i):t.on(e,i);else{if("function"!=typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function s(o){n.once&&t.removeEventListener(e,s),i(o)}))}}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return r},set:function(t){if("number"!=typeof t||t<0||s(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");r=t}}),o.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||s(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},o.prototype.getMaxListeners=function(){return h(this)},o.prototype.emit=function(t){for(var e=[],i=1;i<arguments.length;i++)e.push(arguments[i]);var s="error"===t,o=this._events;if(void 0!==o)s=s&&void 0===o.error;else if(!s)return!1;if(s){var r;if(e.length>0&&(r=e[0]),r instanceof Error)throw r;var a=new Error("Unhandled error."+(r?" ("+r.message+")":""));throw a.context=r,a}var h=o[t];if(void 0===h)return!1;if("function"==typeof h)n(h,this,e);else{var l=h.length,c=m(h,l);for(i=0;i<l;++i)n(c[i],this,e)}return!0},o.prototype.addListener=function(t,e){return l(this,t,e,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(t,e){return l(this,t,e,!0)},o.prototype.once=function(t,e){return a(e),this.on(t,u(this,t,e)),this},o.prototype.prependOnceListener=function(t,e){return a(e),this.prependListener(t,u(this,t,e)),this},o.prototype.removeListener=function(t,e){var i,n,s,o,r;if(a(e),void 0===(n=this._events))return this;if(void 0===(i=n[t]))return this;if(i===e||i.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete n[t],n.removeListener&&this.emit("removeListener",t,i.listener||e));else if("function"!=typeof i){for(s=-1,o=i.length-1;o>=0;o--)if(i[o]===e||i[o].listener===e){r=i[o].listener,s=o;break}if(s<0)return this;0===s?i.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(i,s),1===i.length&&(n[t]=i[0]),void 0!==n.removeListener&&this.emit("removeListener",t,r||e)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(t){var e,i,n;if(void 0===(i=this._events))return this;if(void 0===i.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==i[t]&&(0==--this._eventsCount?this._events=Object.create(null):delete i[t]),this;if(0===arguments.length){var s,o=Object.keys(i);for(n=0;n<o.length;++n)"removeListener"!==(s=o[n])&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=i[t]))this.removeListener(t,e);else if(void 0!==e)for(n=e.length-1;n>=0;n--)this.removeListener(t,e[n]);return this},o.prototype.listeners=function(t){return d(this,t,!0)},o.prototype.rawListeners=function(t){return d(this,t,!1)},o.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):p.call(t,e)},o.prototype.listenerCount=p,o.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]}},177:(t,e,i)=>{"use strict";i.r(e)},242:(t,e,i)=>{"use strict";i.r(e)},723:(t,e,i)=>{"use strict";e.TypedEmitter=i(100).EventEmitter}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,i),o.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};(()=>{"use strict";i.d(n,{default:()=>x});var t=i(417);const e={init:!0,interaction:{observer:{wheelSpeed:-.5,tolerance:50,preventDefault:!0},wheel:!0,touch:!0,drag:!1},keyboard:!0,controls:!0,animation:"slide",direction:"vertical",loop:!1,autoplay:!1,sectionElementsSelector:'[data-arts-fullpage-slider="section"]',controlsPrevSelector:'[data-arts-fullpage-slider-arrow="prev"]',controlsNextSelector:'[data-arts-fullpage-slider-arrow="next"]',indicatorsSelector:"[data-arts-fullpage-slider-indicator]",matchMedia:!1,visibilityObserver:{pauseAutoplay:!0},resizeObserver:{debounceTime:300}};class s{static getElementByStringSelector(t,e=document){if("string"==typeof t){const i=e.querySelector(t);if(i&&null!==i)return i}if(s.isHTMLElement(t))return t}static isHTMLElement(t,e="Element"){if(!t)return!1;let i=t.__proto__;for(;null!==i;){if(i.constructor.name===e)return!0;i=i.__proto__}return!1}static getElementsInContainer(t,e){return"string"==typeof e&&t?[...t.querySelectorAll(e)]:"object"==typeof e?[...e]:void 0}}class o{constructor({container:t,attributeSelector:i="data-arts-fullpage-slider-options",options:n}){this._data=e,s.isHTMLElement(t)&&this._transformOptions({container:t,attributeSelector:i,options:n})}get data(){return this._data}set data(t){this._data=t}_transformOptions({container:i,attributeSelector:n,options:s}){if(!i)return{};let r={};if(s&&e&&(r=t(e,s)),n){let e;e="DATA"===n?function(t,e={separator:"-",pattern:/^/}){let i={};var n;return void 0===e.separator&&(e.separator="-"),Array.prototype.slice.call(t.attributes).filter((n=e.pattern,function(t){let e;return e=/^data\-/.test(t.name),void 0===n?e:e&&n.test(t.name.slice(5))})).forEach((function(t){t.name.slice(5).split(e.separator).reduce((function(e,i,n,s){return"data"===i?e:(n===s.length-1?e[i]=t.value:e[i]="part"in e?e[i]:{},e[i])}),i)})),i}(i):o.parseOptionsStringObject(i.getAttribute(n)),e&&0!==Object.keys(e).length&&(e=o.transformPluginOptions(e),r=t(r,e))}this.data=r}static parseOptionsStringObject(t){let e={};if(!t)return e;try{e=JSON.parse(o.convertStringToJSON(t))}catch(e){console.warn(`${t} is not a valid parameters object`)}return e}static convertStringToJSON(t){if(t)return t.replace(/'/g,'"').replace(/(?=[^"]*(?:"[^"]*"[^"]*)*$)(\w+:)|(\w+ :)/g,(function(t){return'"'+t.substring(0,t.length-1)+'":'}))}static transformPluginOptions(t){return t}}var r=i(723);class a extends r.TypedEmitter{constructor({options:t,sections:e}){super(),this._animating=!1,this._currentIndex=0,this._prevIndex=-1,this._nextIndex=1,this.options=t,this.sections=e}init(){return new Promise((t=>{this._updateWrap(),t(!0)}))}get visible(){return this._visible}set visible(t){this._visible=t}get direction(){return this._direction}set direction(t){this._direction=t}updateDirection(){if(this.options.loop){if(0===this.currentIndex&&this.targetIndex===this.sections.length-1)return void(this.direction="prev");if(this.currentIndex===this.sections.length-1&&0===this.targetIndex)return void(this.direction="next")}this.currentIndex<this.targetIndex?this.direction="next":this.direction="prev"}updateSectionsStates(){this.sections.forEach(((t,e)=>{switch(e){case this.currentIndex:t.state="current";break;case this.nextIndex:t.state="next";break;case this.prevIndex:t.state="prev";break;default:t.state=""}}))}get options(){return this._options}set options(t){this._options=t}get animating(){return this._animating}set animating(t){this._animating=t}get currentIndex(){return this._currentIndex}set currentIndex(t){this._currentIndex=t}get prevIndex(){return this._prevIndex}set prevIndex(t){this._prevIndex=t}get nextIndex(){return this._nextIndex}set nextIndex(t){this._nextIndex=t}get targetIndex(){return this._targetIndex}set targetIndex(t){this._targetIndex=t}updateTargetIndex(t){this.targetIndex=t,this.options.loop&&(this.targetIndex=this.wrap(t)),t===this.currentIndex&&(this.targetIndex=-1)}updateIndex(t=0){this.options.loop?(this.prevIndex=this.wrap(t-1),this.currentIndex=this.wrap(t),this.nextIndex=this.wrap(t+1)):(this.currentIndex=this.wrap(t),this.prevIndex=this.currentIndex-1<0?-1:this.currentIndex-1,this.nextIndex=this.currentIndex+1>this.sections.length-1?-1:this.currentIndex+1)}get sections(){return this._sections}set sections(t){this._sections=t}get wrap(){return this._wrap}set wrap(t){this._wrap=t}_updateWrap(){this.wrap=gsap.utils.wrap(0,this.sections.length)}}class h{constructor({options:t,controller:e,sections:i}){this._handlers={onTransitionStart:this._onTransitionStart.bind(this),onTransitionComplete:this._onTransitionComplete.bind(this)},this.options=t,this.controller=e,this.sections=i}init(){return new Promise((t=>{this._updateAnimation(),t(!0)}))}slideTo(t,e){if(this.controller.animating)return;if(this.controller.updateTargetIndex(t),this.controller.targetIndex<0||this.controller.targetIndex>this.sections.length-1)return;this.controller.updateDirection();const i=this.controller.currentIndex,n=this.controller.targetIndex;"slide"===this.options.animation?(e||(e=this._getAnimationToDirection(t)),this._animateSlide(t,e)):"fade"===this.options.animation?this._animateFade(t):this._animateNone(t),this.controller.updateSectionsStates(),this.controller.emit("sectionChange",{fromIndex:i,toIndex:n,direction:this.controller.direction})}slidePrev(){const t="vertical"===this.options.direction?"up":"right";this.options.direction,this.slideTo(this.controller.currentIndex-1,t)}slideNext(){const t="vertical"===this.options.direction?"down":"left";this.options.direction,this.slideTo(this.controller.currentIndex+1,t)}_animateSlide(t,e){const i=this._getAnimationFromDirection(t);if(this.controller.targetIndex<0)return;let n;this.animation.clear(),this.sections[this.controller.currentIndex].animationOut&&(this.animation.add(this.sections[this.controller.currentIndex].animationOut.play()),n="-=100%"),this.animation.add([this.sections[this.controller.currentIndex].move(e),this.sections[this.controller.targetIndex].move("center",i)],n),this.sections[this.controller.targetIndex].animationIn&&this.animation.add(this.sections[this.controller.targetIndex].animationIn.play(),"-=25%"),this.animation.to({},{duration:.01}),this.controller.updateIndex(t)}_animateFade(t){this.controller.targetIndex<0||(this.animation.clear().add(this.sections[this.controller.currentIndex].fade("out"),"start").add(this.sections[this.controller.targetIndex].fade("in"),"start"),this.controller.updateIndex(t))}_animateNone(t){this.controller.targetIndex<0||(this.animation.clear(),this.options.animationIn&&this.animation.add(this.options.animationIn(this.sections[this.controller.currentIndex].element,this.sections[this.controller.targetIndex].element,this.controller.direction)),this.options.animationOut&&this.animation.add(this.options.animationOut(this.sections[this.controller.currentIndex].element,this.sections[this.controller.targetIndex].element,this.controller.direction)),this.animation.to({},{duration:.01}),this.controller.updateIndex(t))}get options(){return this._options}set options(t){this._options=t}get sections(){return this._sections}set sections(t){this._sections=t}get controller(){return this._controller}set controller(t){this._controller=t}get animation(){return this._animation}set animation(t){this._animation=t}_updateAnimation(){this.animation=gsap.timeline({onStart:this._handlers.onTransitionStart,onComplete:this._handlers.onTransitionComplete})}_onTransitionStart(){this.controller.animating=!0,this.controller.emit("animationStart")}_onTransitionComplete(){this.controller.animating=!1,this.controller.emit("animationComplete")}_getAnimationFromDirection(t){if(t<this.controller.currentIndex){if("horizontal"===this.options.direction)return"left";if("vertical"===this.options.direction)return"down"}else if(t>this.controller.currentIndex){if("horizontal"===this.options.direction)return"right";if("vertical"===this.options.direction)return"up"}}_getAnimationToDirection(t){if(t<this.controller.currentIndex){if("horizontal"===this.options.direction)return"right";if("vertical"===this.options.direction)return"up"}else if(t>this.controller.currentIndex){if("horizontal"===this.options.direction)return"left";if("vertical"===this.options.direction)return"down"}}}class l{constructor({id:t,element:e,options:i,animationIn:n,animationOut:s}){this.id=t,this.element=e,this.options=i,n&&(this.animationIn=n),s&&(this.animationOut=s)}init(){return new Promise((t=>{this._setStyles().then((()=>t(!0)))}))}destroy(){return new Promise((t=>{this._clearStyles().then((()=>t(!0)))}))}_setStyles(){return new Promise((t=>{0!==this.id?("slide"===this.options.animation&&("vertical"===this.options.direction?this.move("down","center",!0):this.move("right","center",!0)),"fade"===this.options.animation&&this.fade("out",!0),setTimeout((()=>{t(!0)}),0)):t(!0)}))}_clearStyles(){return new Promise((t=>{gsap.set(this.element,{clearProps:"transform,opacity",onComplete:()=>t(!0)})}))}get id(){return this._id}set id(t){this._id=t}get state(){return this._state}set state(t){this._state=t,this.element.dataset.artsFullpageSliderSectionState=this._state}get options(){return this._options}set options(t){this._options=t}get element(){return this._element}set element(t){this._element=t}get animationIn(){return this._animationIn}set animationIn(t){this._animationIn=t}get animationOut(){return this._animationOut}set animationOut(t){this._animationOut=t}move(t,e,i){const n={xPercent:0,yPercent:0},s={xPercent:0,yPercent:0,duration:.8,ease:"power4.inOut"};switch(t){case"up":s.yPercent=100;break;case"right":s.xPercent=100;break;case"down":s.yPercent=-100;break;case"left":s.xPercent=-100}switch(e){case"up":n.yPercent=100;break;case"right":n.xPercent=100;break;case"down":n.yPercent=-100;break;case"left":n.xPercent=-100}return i?gsap.set(this.element,s):gsap.fromTo(this.element,n,s)}fade(t,e=!1){return e?gsap.set(this.element,{autoAlpha:"in"===t?1:0}):gsap.to(this.element,{autoAlpha:"in"===t?1:0,duration:.6})}}class c{constructor({visible:t,autoload:e=!0,controller:i,transition:n,sections:s=[],options:o,containerElement:r}){this._initialized=!1,this.controller=i,this.transition=n,this.options=o,this.containerElement=r,this.sections=s,e&&this.init()}init(){this.enabled=!0}destroy(){this.enabled&&this.initialized&&(this.enabled=!1)}enable(){!this.enabled&&this.initialized&&this.init()}disable(){this.enabled&&this.initialized&&this.destroy()}set enabled(t){this._enabled=t}get enabled(){return this._enabled}set initialized(t){this._initialized=t}get initialized(){return this._initialized}get containerElement(){return this._containerElement}set containerElement(t){this._containerElement=t}get sections(){return this._sections}set sections(t){this._sections=t}get controller(){return this._controller}set controller(t){this._controller=t}get transition(){return this._transition}set transition(t){this._transition=t}get options(){return this._options}set options(t){this._options=t}}class u extends c{constructor({options:t,controller:e,transition:i,containerElement:n}){super({autoload:!1,options:t,controller:e,transition:i,containerElement:n}),this._handlers={onUp:this._onUp.bind(this),onRight:this._onRight.bind(this),onDown:this._onDown.bind(this),onLeft:this._onLeft.bind(this)}}init(){return new Promise((t=>{this._updateConfig(),this._setTouchAction().then((()=>{this.options.interaction&&this.options.interaction.wheel&&this._updateInstanceWheel(),this.options.interaction&&this.options.interaction.touch&&this._updateInstanceTouch(),this.enabled=!0,t(!0)}))}))}destroy(){return new Promise((t=>{this._setTouchAction(!0).then((()=>{this.instanceWheel&&this.instanceWheel.kill(),this.instanceTouch&&this.instanceTouch.kill(),this.enabled=!1,t(!0)}))}))}enable(){this.enabled||(this.instanceWheel&&this.instanceWheel.enable(),this.instanceTouch&&this.instanceTouch.enable(),this._setTouchAction(),this.enabled=!0)}disable(){this.enabled&&(this.instanceWheel&&this.instanceWheel.disable(),this.instanceTouch&&this.instanceTouch.disable(),this._setTouchAction(!0),this.enabled=!1)}get config(){return this._config}set config(t){this._config=t}_updateConfig(){this.options.interaction&&this.options.interaction.observer&&(this.config=Object.assign({target:this.containerElement},this.options.interaction.observer))}get instanceWheel(){return this._instanceWheel}set instanceWheel(t){this._instanceWheel=t}_updateInstanceWheel(){const t=Object.assign({type:"wheel"},this.config);"horizontal"===this.options.direction&&Object.assign(t,{onUp:this._handlers.onLeft,onDown:this._handlers.onRight}),"vertical"===this.options.direction&&Object.assign(t,{onUp:this._handlers.onUp,onDown:this._handlers.onDown}),this.instanceWheel=ScrollTrigger.observe(t)}get instanceTouch(){return this._instanceTouch}set instanceTouch(t){this._instanceTouch=t}_updateInstanceTouch(){const t=Object.assign({type:"touch"},this.config);this.options.interaction&&this.options.interaction.drag&&(t.type="touch, pointer"),"horizontal"===this.options.direction&&Object.assign(t,{onLeft:this._handlers.onLeft,onRight:this._handlers.onRight}),"vertical"===this.options.direction&&Object.assign(t,{onUp:this._handlers.onUp,onDown:this._handlers.onDown}),this.instanceTouch=ScrollTrigger.observe(t)}_onUp(){this.transition.slideTo(this.controller.currentIndex+1,"down")}_onRight(){this.transition.slideTo(this.controller.currentIndex-1,"right")}_onDown(){this.transition.slideTo(this.controller.currentIndex-1,"up")}_onLeft(){this.transition.slideTo(this.controller.currentIndex+1,"left")}_setTouchAction(t=!1){return new Promise((e=>{t?setTimeout((()=>{this.containerElement.style.touchAction="",e(!0)}),0):setTimeout((()=>{this.containerElement.style.touchAction="horizontal"===this.options.direction?"pan-y":"pan-x",e(!0)}),0)}))}}class d extends c{constructor({options:t,controller:e,transition:i,containerElement:n}){super({autoload:!1,options:t,controller:e,transition:i,containerElement:n}),this._handlers={onKeyDown:this._onKeyDown.bind(this)}}init(){return new Promise((t=>{this._attachEvents(),this._setTabIndex().then((()=>{this.enabled=!0,t(!0)}))}))}destroy(){return new Promise((t=>{this._detachEvents(),this._setTabIndex(!0).then((()=>{this.enabled=!1,t(!0)}))}))}enable(){this.enabled||(this._attachEvents(),this._setTabIndex(),this.enabled=!0)}disable(){this.enabled&&(this._detachEvents(),this._setTabIndex(!0),this.enabled=!1)}_attachEvents(){this.containerElement.addEventListener("keydown",this._handlers.onKeyDown)}_detachEvents(){this.containerElement.removeEventListener("keydown",this._handlers.onKeyDown)}_onUp(){this.transition.slideTo(this.controller.currentIndex+1,"down")}_onRight(){this.transition.slideTo(this.controller.currentIndex+1,"left")}_onDown(){this.transition.slideTo(this.controller.currentIndex-1,"up")}_onLeft(){this.transition.slideTo(this.controller.currentIndex-1,"right")}_onKeyDown(t){if("horizontal"===this.options.direction)switch(t.key){case"ArrowLeft":this._onLeft();break;case"ArrowRight":this._onRight()}if("vertical"===this.options.direction)switch(t.key){case"ArrowUp":this._onDown();break;case"ArrowDown":this._onUp()}}_setTabIndex(t=!1){return new Promise((e=>{t?setTimeout((()=>{this.containerElement.removeAttribute("tabindex"),e(!0)}),0):setTimeout((()=>{this.containerElement.setAttribute("tabindex","0"),this.containerElement.focus({preventScroll:!0}),e(!0)}),0)}))}}class p extends c{constructor({options:t,controller:e,transition:i,sections:n,containerElement:s}){super({autoload:!1,options:t,controller:e,sections:n,transition:i,containerElement:s}),this._controlsPrev=[],this._controlsNext=[],this._indicators=[],this._handlers={onClick:this._onClick.bind(this),onSectionChange:this._onSectionChange.bind(this)}}init(){return new Promise((t=>{this._updateElements(),this._attachEvents(),this._updateStateControlsPrev(),this._updateStateControlsNext(),this._updateStateIndicators(),this.enabled=!0,t(!0)}))}destroy(){return new Promise((t=>{this._detachEvents(),this.enabled=!1,t(!0)}))}enable(){this.enabled||(this._attachEvents(),this.enabled=!0)}disable(){this.enabled&&(this._detachEvents(),this.enabled=!1)}_attachEvents(){this.containerElement.addEventListener("click",this._handlers.onClick),this.controller.addListener("sectionChange",this._handlers.onSectionChange)}_detachEvents(){this.containerElement.removeEventListener("click",this._handlers.onClick),this.controller.removeListener("sectionChange",this._handlers.onSectionChange)}get controlsPrev(){return this._controlsPrev}set controlsPrev(t){this._controlsPrev=t}get controlsNext(){return this._controlsNext}set controlsNext(t){this._controlsNext=t}get indicators(){return this._indicators}set indicators(t){this._indicators=t}_updateElements(){this.controlsPrev=[...this.containerElement.querySelectorAll(this.options.controlsPrevSelector)],this.controlsNext=[...this.containerElement.querySelectorAll(this.options.controlsNextSelector)],this.indicators=[...this.containerElement.querySelectorAll(this.options.indicatorsSelector)]}_onClick(t){if(s.isHTMLElement(t.target)){const e=t.target;if("artsFullpageSliderArrow"in e.dataset){const i=e.dataset.artsFullpageSliderArrow;t.preventDefault(),"prev"===i&&this.transition.slidePrev(),"next"===i&&this.transition.slideNext()}if("artsFullpageSliderIndicator"in e.dataset){const i=parseInt(e.dataset.artsFullpageSliderIndicator);t.preventDefault(),this.sections[i]&&this.transition.slideTo(i)}}}_onSectionChange(){this._updateStateControlsPrev(),this._updateStateControlsNext(),this._updateStateIndicators()}_updateStateControlsPrev(){this.controlsPrev.length&&!this.options.loop&&this.controlsPrev.forEach((t=>{this.controller.prevIndex<0?t.setAttribute("disabled",""):t.removeAttribute("disabled")}))}_updateStateControlsNext(){this.controlsNext.length&&!this.options.loop&&this.controlsNext.forEach((t=>{this.controller.nextIndex<0?t.setAttribute("disabled",""):t.removeAttribute("disabled")}))}_updateStateIndicators(){this.indicators.length&&this.indicators.forEach((t=>{parseInt(t.dataset.artsFullpageSliderIndicator)===this.controller.currentIndex?t.setAttribute("current",""):t.removeAttribute("current")}))}}class m extends c{constructor({options:t,controller:e,transition:i,containerElement:n}){super({autoload:!1,options:t,controller:e,transition:i,containerElement:n}),this._handlers={onAnimationStart:this._onAnimationStart.bind(this),onAnimationComplete:this._onAnimationComplete.bind(this),visibleUpdate:this._onVisibleUpdate.bind(this)}}init(){return new Promise((t=>{this.enabled||"object"==typeof this.options.autoplay&&"number"==typeof this.options.autoplay.duration&&(this._attachEvents(),this._updateAnimation(),this._updateTimer(),this.initialized=!0,this.enabled=!0),t(!0)}))}destroy(){return new Promise((t=>{this.enabled&&this.initialized?(this._detachEvents(),this._stopTimer(),this.controller.emit("autoplayStop"),this.enabled=!1,t(!0)):t(!0)}))}get timer(){return this._timer}set timer(t){this._timer=t}get restartRequired(){return this._restartRequired}set restartRequired(t){this._restartRequired=t}_updateTimer(){"object"==typeof this.options.autoplay&&"number"==typeof this.options.autoplay.duration&&(this.controller.emit("autoplayStart"),this.timer=gsap.delayedCall(this.options.autoplay.duration,(()=>{this.controller.emit("autoplayComplete"),this.transition.slideNext()})))}_stopTimer(){"object"==typeof this.options.autoplay&&"number"==typeof this.options.autoplay.duration&&(this.controller.emit("autoplayStop"),this.timer.pause(),this.animation.pause())}_pauseTimer(){"object"==typeof this.options.autoplay&&"number"==typeof this.options.autoplay.duration&&(this.controller.emit("autoplayPause"),this.timer.pause(),this.animation.pause())}_resumeTimer(){"object"==typeof this.options.autoplay&&"number"==typeof this.options.autoplay.duration&&this.timer.paused()&&(this.restartRequired?this._restartTimer():(this.controller.emit("autoplayResume"),this.timer.play(),this.animation.play()))}_restartTimer(){"object"==typeof this.options.autoplay&&"number"==typeof this.options.autoplay.duration&&this.controller.nextIndex>=0&&(this.controller.emit("autoplayStart"),this.timer.restart(!0),this.animation.restart(!0),this.restartRequired=!1)}get animation(){return this._animation}set animation(t){this._animation=t}_updateAnimation(){if("object"==typeof this.options.autoplay&&"number"==typeof this.options.autoplay.duration){const t=this;this.animation=gsap.timeline().to({},{duration:this.options.autoplay.duration,onUpdate:function(){t.controller.emit("autoplayProgress",this.progress())}})}}_attachEvents(){this.controller.addListener("animationStart",this._handlers.onAnimationStart),this.controller.addListener("animationComplete",this._handlers.onAnimationComplete),this.options.visibilityObserver&&this.options.visibilityObserver.pauseAutoplay&&this.controller.addListener("visibleUpdate",this._handlers.visibleUpdate)}_detachEvents(){this.controller.removeListener("animationStart",this._handlers.onAnimationStart),this.controller.removeListener("animationComplete",this._handlers.onAnimationComplete),this.options.visibilityObserver&&this.options.visibilityObserver.pauseAutoplay&&this.controller.removeListener("visibleUpdate",this._handlers.visibleUpdate)}_onAnimationStart(){this._stopTimer()}_onAnimationComplete(){this.controller.visible?this._restartTimer():this.restartRequired=!0}_onVisibleUpdate(t){t?this._resumeTimer():this._pauseTimer()}}class _ extends r.TypedEmitter{constructor({container:t,options:e={}}){super(),this._enabled=!1,this._initialized=!1,this._sections=[],t&&e&&(this._updateContainerElement(t),this._updateOptions(this.containerElement,e),this._updateSectionsElements(this.options.sectionElementsSelector))}get enabled(){return this._enabled}set enabled(t){this._enabled=t}get initialized(){return this._initialized}set initialized(t){this._initialized=t}get containerElement(){return this._containerElement}set containerElement(t){this._containerElement=t}_updateContainerElement(t){this.containerElement=s.getElementByStringSelector(t)}get sectionsElements(){return this._sectionsElements}set sectionsElements(t){this._sectionsElements=t}_updateSectionsElements(t){this.sectionsElements=[...this.containerElement.querySelectorAll(t)]}get sections(){return this._sections}set sections(t){this._sections=t}_updateSections(){return new Promise((t=>{const e=[];this.sectionsElements.length&&this.sectionsElements.forEach(((t,i)=>{const n=new l({id:i,options:this.options,element:t});this.sections.push(n),0===i&&(this.sections[0].state="current"),1===i&&(this.sections[1].state="next"),i===this.sectionsElements.length-1&&(this.sections[this.sectionsElements.length-1].state="prev"),e.push(n.init())})),Promise.all(e).then((()=>t(!0))).catch((()=>t(!0)))}))}get transition(){return this._transition}set transition(t){this._transition=t}_updateTransition(){return new Promise((t=>{this.transition=new h({options:this.options,sections:this.sections,controller:this.controller}),this.transition.init().then((()=>t(!0)))}))}get controller(){return this._controller}set controller(t){this._controller=t}_updateController(){return new Promise((t=>{this.controller=new a({options:this.options,sections:this.sections}),this.controller.init().then((()=>{this._attachControllerEvents(),t(!0)}))}))}_attachControllerEvents(){this.controller.on("sectionChange",(t=>this.emit("sectionChange",t))).on("autoplayStart",(t=>this.emit("autoplayStart",t))).on("autoplayStop",(t=>this.emit("autoplayStop",t))).on("autoplayResume",(t=>this.emit("autoplayResume",t))).on("autoplayPause",(t=>this.emit("autoplayPause",t))).on("autoplayComplete",(t=>this.emit("autoplayComplete",t))).on("autoplayProgress",(t=>this.emit("autoplayProgress",t))).on("visibleUpdate",(t=>this.emit("visibleUpdate",t)))}get options(){return this._options}set options(t){this._options=t}_updateOptions(t,e){this.options=new o({container:t,attributeSelector:"data-arts-fullpage-slider-options",options:e}).data}get interaction(){return this._interaction}set interaction(t){this._interaction=t}get keyboard(){return this._keyboard}set keyboard(t){this._keyboard=t}get controls(){return this._controls}set controls(t){this._controls=t}get autoplay(){return this._autoplay}set autoplay(t){this._autoplay=t}_initPlugins(){return new Promise((t=>{const e=[];this.options.interaction&&(this.interaction=new u({options:this.options,controller:this.controller,transition:this.transition,containerElement:this.containerElement}),e.push(this.interaction.init())),this.options.keyboard&&(this.keyboard=new d({options:this.options,controller:this.controller,transition:this.transition,containerElement:this.containerElement}),e.push(this.keyboard.init())),this.options.controls&&(this.controls=new p({options:this.options,controller:this.controller,sections:this.sections,transition:this.transition,containerElement:this.containerElement}),e.push(this.controls.init())),this.options.autoplay&&(this.autoplay=new m({options:this.options,controller:this.controller,transition:this.transition,containerElement:this.containerElement}),"object"==typeof this.options.autoplay&&"number"==typeof this.options.autoplay.duration&&this.options.autoplay.autoInit&&e.push(this.autoplay.init())),Promise.all(e).then((()=>t(!0))).catch((()=>t(!0)))}))}get matchMedia(){return this._matchMedia}set matchMedia(t){this._matchMedia=t}get visible(){return this._visible}set visible(t){this._visible=t}updateVisible({target:t,isIntersecting:e}){this.visible=e,this.controller.visible=e,this.controller.emit("visibleUpdate",this.visible)}}class f{constructor({condition:t,callbackMatch:e,callbackNoMatch:i}){this._handlers={change:this._onChange.bind(this)},this.condition=t,this.callbacks={match:e,noMatch:i},(this._hasMatchFunction()||this._hasNoMatchFunction())&&this.init()}init(){this.mediaQuery=this._addMatchMedia(),this._attachEvents()}destroy(){this._detachEvents(),this.mediaQuery=null}get mediaQuery(){return this._mediaQuery}set mediaQuery(t){this._mediaQuery=t}get callbacks(){return this._callbacks}set callbacks(t){this._callbacks=t}get condition(){return this._condition}set condition(t){this._condition=t}_hasMatchFunction(){return"function"==typeof this.callbacks.match}_hasNoMatchFunction(){return"function"==typeof this.callbacks.noMatch}_addMatchMedia(){return window.matchMedia(`${this.condition}`)}_attachEvents(){"function"==typeof this.mediaQuery.addEventListener?this.mediaQuery.addEventListener("change",this._handlers.change):this.mediaQuery.addListener(this._handlers.change)}_detachEvents(){"function"==typeof this.mediaQuery.removeEventListener?this.mediaQuery.removeEventListener("change",this._handlers.change):this.mediaQuery.removeListener(this._handlers.change)}_onChange(t){t.matches?this._hasMatchFunction()&&this.callbacks.match():t.matches||this._hasNoMatchFunction()&&this.callbacks.noMatch()}}class b{constructor({elements:t,callback:e,options:i={}}){this._handlers={update:this._onUpdate.bind(this)},this.elements=t,this.callbacks=e,this.options=i,this.elements.length&&this._hasAnyIntersectCallbacks()&&this.init()}set instance(t){this._instance=t}get instance(){return this._instance}set callbacks(t){"function"==typeof t&&(this._onIntersectCallback=t,this._offIntersectCallback=t),"object"==typeof t&&(this._onIntersectCallback=t.onIntersect,this._offIntersectCallback=t.offIntersect)}get callbacks(){return this._callbacks}set elements(t){this._elements=t}get elements(){return this._elements}set options(t){this._options=t}get options(){return this._options}init(){this.instance=new IntersectionObserver(this._handlers.update,this.options),this._observeElements()}destroy(){this.instance&&this.instance&&(this.instance.disconnect(),this.instance=null)}_observeElements(){if(this.instance)for(let t=0;t<this.elements.length;t++)this.instance.observe(this.elements[t])}_onUpdate(t){for(const e of t)e.isIntersecting?this._onIntersectCallback({target:e.target,isIntersecting:e.isIntersecting,entry:e}):this._offIntersectCallback({target:e.target,isIntersecting:e.isIntersecting,entry:e})}_hasAnyIntersectCallbacks(){return"function"==typeof this._onIntersectCallback||"function"==typeof this._offIntersectCallback}}class g{constructor({elements:t,callback:e}){this.elements=t,this.callback=e,this.elements.length&&this._hasCallback()&&this.init()}set instance(t){this._instance=t}get instance(){return this._instance}set callback(t){this._callback=t}get callback(){return this._callback}set elements(t){this._elements=t}get elements(){return this._elements}init(){this.instance=new ResizeObserver(this._onUpdate.bind(this)),this._observeElements()}destroy(){this.instance&&(this.instance.disconnect(),this.instance=null)}_onUpdate(t){const e=[];for(const i of t)e.push(i.target);this.callback(e)}_observeElements(){if(this.instance)for(let t=0;t<this.elements.length;t++)this.instance.observe(this.elements[t])}_hasCallback(){return"function"==typeof this.callback}}var v=i(110),y=i.n(v);gsap.registerPlugin(ScrollTrigger),i(177),i(242);const x=class extends _{constructor(t=document.querySelector('[data-arts-fullpage-slider="container"]'),e){super({container:t,options:e}),this.ready=new Promise((t=>{this.setReady=t})),this._resized=!1,this.options.init&&(this.options.matchMedia&&!window.matchMedia(`${this.options.matchMedia}`).matches?(this.matchMedia=new f({condition:this.options.matchMedia,callbackMatch:this.init.bind(this)}),this._onInitReject()):this.init())}init(){return new Promise((t=>{this.initialized?t(!0):(this.matchMedia&&this.matchMedia.destroy(),this.options.matchMedia&&(this.matchMedia=new f({condition:this.options.matchMedia,callbackMatch:this.init.bind(this),callbackNoMatch:this.destroy.bind(this)})),this._updateSections().then((()=>this._updateController())).then((()=>this._updateTransition())).then((()=>this._initPlugins())).then((()=>{this._updateResize(),this._onInitSuccess(),this.options.visibilityObserver&&(this.intersectionObserver=new b({elements:[this.containerElement],callback:this.updateVisible.bind(this)})),this.initialized=!0,this.enabled=!0,this.setReady(),t(!0)})))}))}destroy(){this.enabled=!1,this.initialized=!1,this.intersectionObserver&&this.intersectionObserver.destroy(),this._onInitReject()}enable(){}disable(){}update(){}slideTo(t){this.transition.slideTo(t,void 0)}slidePrev(){this.transition.slidePrev()}slideNext(){this.transition.slideNext()}addAnimationIn(t,e){this.sections[e]?this.sections[e].animationIn=t:console.warn(`Cannot add section animation. Section with index ${e} doesn't exist`)}addAnimationOut(t,e){this.sections[e]?this.sections[e].animationOut=t:console.warn(`Cannot add section animation. Section with index ${e} doesn't exist`)}get animating(){return this.controller.animating}set animating(t){this.controller.animating=t}get targetIndex(){return this.controller.targetIndex}get currentIndex(){return this.controller.currentIndex}get prevIndex(){return this.controller.prevIndex}get nextIndex(){return this.controller.nextIndex}set resize(t){this._resize=t}get resize(){return this._resize}set resized(t){this._resized=t}get resized(){return this._resized}get intersectionObserver(){return this._intersectionObserver}set intersectionObserver(t){this._intersectionObserver=t}_updateResize(){if(this.options.resizeObserver){let t,e=0;"object"==typeof this.options.resizeObserver&&"number"==typeof this.options.resizeObserver.debounceTime&&(e=this.options.resizeObserver.debounceTime),t=e>0?y()(this._onResize.bind(this),e):this._onResize.bind(this),this.resize=new g({elements:[this.containerElement],callback:t})}}_onResize(){this.enabled&&(this.resized&&this.update(),this.resized=!0)}_onInitSuccess(){gsap.ticker.add((()=>this.containerElement.classList.add("has-fullpage-slider")),!0,!1),gsap.ticker.add((()=>this.containerElement.classList.remove("no-fullpage-slider")),!0,!1)}_onInitReject(){gsap.ticker.add((()=>this.containerElement.classList.add("no-fullpage-slider")),!0,!1),gsap.ticker.add((()=>this.containerElement.classList.remove("has-fullpage-slider")),!0,!1)}setReady(){}}})(),this.ArtsFullpageSlider=n.default})();