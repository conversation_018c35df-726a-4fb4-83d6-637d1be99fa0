<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'arts/optimizer/inline_script_load_lcp_image_enabled', 'arts_filter_optimizer_inline_script_load_lcp_image_enabled' );
if ( ! function_exists( 'arts_filter_optimizer_inline_script_load_lcp_image_enabled' ) ) {
	/**
	 * Filter to enable/disable loading of LCP image in inline script.
	 *
	 * @return bool
	 */
	function arts_filter_optimizer_inline_script_load_lcp_image_enabled() {
		return ( function_exists( 'arts_is_preloader_enabled' ) && ! arts_is_preloader_enabled() ) || ! function_exists( 'arts_is_preloader_enabled' );
	}
}
