{"name": "arts/merlin-wp", "description": "Better WordPress Theme Onboarding. Forked from Merlin WP by <PERSON>.", "type": "library", "minimum-stability": "dev", "license": "GPL-3.0+", "repositories": [{"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsContentImporter", "options": {"symlink": true}}, {"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsUtilities", "options": {"symlink": true}}], "autoload": {"psr-4": {"Arts\\Merlin\\": "src/"}}, "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://artemsemkin.com"}, {"name": "<PERSON>", "homepage": "https://richtabor.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.proteusthemes.com/"}], "require": {"monolog/monolog": "^2.10", "arts/wp-content-importer-v2": "@dev", "arts/utilities": "@dev"}}