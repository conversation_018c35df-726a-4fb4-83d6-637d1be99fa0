<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$defaults = array(
	'id'            => get_the_ID(),
	'attributes'    => array(
		'class' => array(
			'post-meta',
			'post-meta-blog',
			'subheading',
		),
	),
	'set'           => array(),
	'hasTransition' => false,
);

$args = Utilities::parse_args_recursive( $args, $defaults );

$post_meta_set          = $args['set'];
$blog_posts_date_style  = 'info';
$date_link              = get_month_link( get_post_time( 'Y', false, $args['id'] ), get_post_time( 'm', false, $args['id'] ) );
$author                 = Utilities::get_post_author( $args['id'] );
$post_categories        = wp_get_post_categories( $args['id'] );
$post_categories_amount = count( $post_categories );

?>

<div <?php Utilities::print_attributes( $args['attributes'] ); ?>>
	<?php if ( in_array( 'date', $post_meta_set ) && ( is_single() ) ) : ?>
		<div class="post-meta__item no-hover-group">
			<a href="<?php echo esc_attr( $date_link ); ?>"><i class="post-meta__icon material-icons">event</i> <span><?php echo esc_html( get_the_date( '', $args['id'] ) ); ?></span></a>
		</div>
	<?php endif; ?>
	<?php if ( in_array( 'categories', $post_meta_set ) && $post_categories_amount > 0 ) : ?>
		<div class="post-meta__item no-hover-group">
			<?php if ( $post_categories_amount === 1 ) : ?>
				<a href="<?php echo esc_url( get_category_link( $post_categories[0] ) ); ?>"><i class="post-meta__icon material-icons">subject</i><span><?php echo esc_html( get_cat_name( $post_categories[0] ) ); ?></span></a>
			<?php else : ?>
				<i class="post-meta__icon material-icons">subject</i><?php the_category( ',&nbsp;', '', $args['id'] ); ?>
			<?php endif; ?>
		</div>
	<?php endif; ?>
	<?php if ( in_array( 'comments', $post_meta_set ) ) : ?>
		<div class="post-meta__item">
			<a href="<?php echo get_comments_link( $args['id'] ); ?>"<?php if ( $args['hasTransition'] ) : ?>data-ajax-transition='flyingImage'<?php endif; ?>>
				<i class="post-meta__icon material-icons">comment</i><span><?php comments_number( esc_html__( 'No Comments', 'asli' ), esc_html__( '1 Comment', 'asli' ), esc_html__( '% Comments', 'asli' ), $args['id'] ); ?></span>
			</a>
		</div>
	<?php endif; ?>
	<?php if ( ! empty( $author['name'] ) && in_array( 'author', $post_meta_set ) ) : ?>
		<div class="post-meta__item no-hover-group">
		<span><span><?php echo esc_html__( 'by', 'asli' ); ?>&nbsp;</span><a href="<?php echo esc_url( $author['url'] ); ?>"><span><?php echo esc_html( $author['name'] ); ?></span></a></span>
		</div>
	<?php endif; ?>
</div>
