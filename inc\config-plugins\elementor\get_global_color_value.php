<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_global_color_value' ) ) {
	/**
	 * Retrieve the global color value from Elementor settings.
	 *
	 * @param string   $option_name The name of the color option.
	 * @param int|null $post_id The ID of the post to retrieve settings for. Defaults to null.
	 * @param string   $fallback_value The fallback color value if the option is not found. Defaults to '#ffffff'.
	 *
	 * @return string The color value.
	 * @deprecated 2.0.0 Use \Arts\Utilities\Utilities::get_global_color_value() method instead.
	 */
	function arts_get_global_color_value( $option_name, $post_id = null, $fallback_value = '#ffffff' ) {
		return Utilities::get_global_color_value( $option_name, $post_id, $fallback_value );
	}
}
