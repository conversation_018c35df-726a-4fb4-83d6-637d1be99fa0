"use strict";(self.webpackChunkArtsInfiniteList=self.webpackChunkArtsInfiniteList||[]).push([[461],{258:(t,i,o)=>{o.r(i),o.d(i,{default:()=>s});var e=o(199);const n={from:1,to:1};class s extends e.v{constructor({autoLoad:t=!1,container:i,options:o,controller:e,config:s}){super({autoLoad:t,container:i,options:o,controller:e,config:s,defaults:n}),this.init()}init(){if(!this.enabled)if(this.config.multiLane)for(let t=0;t<this.controller.lanes.length;t++)t in this.config.multiLane&&"from"in this.config.multiLane[t]&&"to"in this.config.multiLane[t]&&this._registerOpacityHook(t);else this._registerOpacityHook()}destroy(){this.enabled&&this.controller.removeOpacityHooks()}_registerOpacityHook(t){this.controller.addOpacityHook(this._transformerOpacity.bind(this),t)}_transformerOpacity({indexLane:t,indexItem:i,progressItem:o,opacityItem:e,laneGeometry:n}){let s=1;const r=this.getConfigOption("from",t),a=this.getConfigOption("to",t);return s=o<0?a+o+r:a-o+r,s}}}}]);