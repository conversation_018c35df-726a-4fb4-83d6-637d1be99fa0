<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

global $wp_query;

$posts_count = $wp_query->post_count;

$i = 0;

$border_attributes = array(
	'class'                       => array(
		'w-100',
		'bt-opacity',
	),
	'data-arts-os-animation-name' => 'animatedBorderHorizontal',
);

?>

<?php while ( have_posts() ) : ?>
	<?php the_post(); ?>
	<?php
		$args_post = array(
			'class' => array(),
		);

		if ( $posts_count > 1 ) {
			if ( $i === 0 ) { // first item
				$args_post['class'][] = 'pb-xsmall';
			} elseif ( $i === $posts_count - 1 ) { // last item
				$args_post['class'][] = 'pt-xsmall';
			} else { // middle item
				$args_post['class'][] = 'py-xsmall';
			}
		}
		?>
	<?php get_template_part( 'template-parts/blog/post/post', '', $args_post ); ?>

	<?php if ( $i !== $posts_count - 1 ) : // don't display divider for the last item or after the sticky item ?>
		<div <?php Utilities::print_attributes( $border_attributes ); ?>></div>
	<?php endif; ?>

	<?php $i++; ?>
<?php endwhile; ?>
