<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$defaults = array();
$args     = wp_parse_args( $args, $defaults );

$heading_wrapper_attributes = array(
	'class'                       => array(
		'preloader__heading',
		'js-preloader__heading',
	),
	'data-arts-split-text-preset' => 'animatedChars',
);
?>

<?php if ( ! empty( $args['headingText'] ) ) : ?>
	<!-- Heading -->
	<div <?php Utilities::print_attributes( $heading_wrapper_attributes ); ?>>
		<div class="preloader__heading-text"><?php echo esc_html( $args['headingText'] ); ?></div>
	</div>
	<!-- - Heading -->
<?php endif; ?>
