<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$section_attributes = array(
	'class' => array(
		'section',
		'section-fullheight',
		'masthead',
	),
);

$section_attributes = Utilities::get_component_attributes(
	$section_attributes,
	array(
		'name'         => 'Masthead',
		'hasAnimation' => true,
	)
);

$masthead_header_attributes = array(
	'class' => array(
		'masthead__header',
		'text-center',
		'overflow-hidden',
		'py-small',
	),
);

$masthead_heading_attributes = array(
	'class' => array(
		'my-0',
		'entry-title',
		'masthead__heading',
		'display-xl',
		'color-accent',
		'js-marquee-header__label',
	),
);

$masthead_text_attributes = array(
	'class' => array(
		'masthead__text',
		'h6',
	),
);

$button_args = array(
	'title'       => esc_html__( 'Take Me Home', 'asli' ),
	'title_hover' => esc_html__( 'Take Me Home', 'asli' ),
	'attributes'  => array(
		'href'                             => home_url( '/' ),
		'class'                            => array( 'button', 'button_icon', 'button-circle' ),
		'data-arts-cursor-follower-target' => wp_json_encode(
			array(
				'magnetic' => 0.25,
				'scale'    => 'current',
			)
		),
	),
);

$masthead_header_attributes = Utilities::get_component_attributes(
	$masthead_header_attributes,
	array(
		'name'         => 'MarqueeHeader',
		'options'      => arts_get_marquee_options(),
		'hasAnimation' => true,
	)
);
?>

<?php get_header(); ?>

<!-- Section 404 -->
<div <?php Utilities::print_attributes( $section_attributes ); ?>>
	<div class="section-fullheight__inner pt-header-height">
		<div <?php Utilities::print_attributes( $masthead_header_attributes ); ?>>
			<!-- Title -->
			<div class="overflow-hidden">
				<div class="d-flex flex-nowrap text-nowrap js-marquee-header__list-lane" data-arts-os-animation-name="animatedReveal">
					<div class="marquee-header__item d-inline-block js-marquee-header__list-item">
						<h1 <?php Utilities::print_attributes( $masthead_heading_attributes ); ?>><?php echo esc_html__( 'Error 404', 'asli' ); ?></h1>
					</div>
				</div>
			</div>
			<!-- - Title -->
			<div class="container content-width masthead__content">
				<!-- Message -->
				<div class="w-100 mt-xxsmall" data-arts-split-text-preset="animatedLines">
					<div <?php Utilities::print_attributes( $masthead_text_attributes ); ?>><?php echo esc_html__( 'It looks like nothing found here. Try to navigate the menu or return to the home page.', 'asli' ); ?></div>
				</div>
				<!-- - Message -->
				<!-- Button -->
				<div class="d-block masthead__button mt-xsmall" data-arts-os-animation-name="animatedJumpScale">
					<?php get_template_part( 'template-parts/button/button', 'normal', $button_args ); ?>
				</div>
				<!-- - Button -->
			</div>
		</div>
	</div>
</div>
<!-- - Section 404 -->

<?php get_footer(); ?>
