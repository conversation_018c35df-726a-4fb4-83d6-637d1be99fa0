<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_animations_options' ) ) {
	/**
	 * Get the global options for the various theme animations.
	 *
	 * @return array $options
	 */
	function arts_get_animations_options() {
		$animations_trigger_hook                  = Utilities::get_kit_settings( 'animations_trigger_hook', 0.1 );
		$animations_timescale                     = Utilities::get_kit_settings( 'animations_timescale', 1.0 );
		$animations_curtains                      = Utilities::get_kit_settings( 'animations_curtains', 'curved' );
		$animations_curtains_curved_force_repaint = Utilities::get_kit_settings( 'animations_curtains_curved_force_repaint', true );

		$options = array(
			'triggerHook'             => floatval( $animations_trigger_hook ),
			'timeScale'               => floatval( $animations_timescale ),
			'curvedMasks'             => $animations_curtains === 'curved',
			'curvedMasksForceRepaint' => $animations_curtains_curved_force_repaint,
		);

		return $options;
	}
}
