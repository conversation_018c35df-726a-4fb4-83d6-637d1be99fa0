<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_body_document_option' ) ) {
	/**
	 * Retrieve the body document option for a given post.
	 *
	 * @param string   $option_name The name of the option to retrieve. Default is 'background_color'.
	 * @param int|null $post_id The ID of the post. Default is null.
	 * @param string   $fallback_value The fallback value if the option is not found. Default is '#ffffff'.
	 *
	 * @return string The value of the document option or the fallback value.
	 * @deprecated 2.0.0 Use \Arts\Utilities\Utilities::get_body_document_option() method instead.
	 */
	function arts_get_body_document_option( $option_name = 'background_color', $post_id = null, $fallback_value = '#ffffff' ) {
		Utilities::get_body_document_option( $option_name, $post_id, $fallback_value );
	}
}
