<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$titles                        = Utilities::get_page_titles();
$page_title                    = $titles['title'];
$page_subtitle                 = $titles['subtitle'];
$page_description              = $titles['description'];
$blog_masthead_marquee_enabled = true;
$is_shop                       = Utilities::is_shop();

$section_attributes = array(
	'class' => array(
		'masthead',
		'masthead_archive',
		'pt-header-height',
		'text-center',
		'js-masthead',
	),
	'id'    => 'page-masthead',
);

$section_attributes = Utilities::get_component_attributes(
	$section_attributes,
	array(
		'name'         => 'Masthead',
		'options'      => array(),
		'hasAnimation' => false,
	),
);

$marquee_container_attributes = array(
	'class' => array(
		'marquee-header',
		'overflow-hidden',
		'py-2',
		'js-marquee-header',
		'js-masthead__ajax-updated-content',
	),
);

$heading_attributes = array(
	'class' => array(
		'masthead__heading',
		'my-0',
		'h1',
	),
);

$marquee_container_attributes = Utilities::get_component_attributes(
	$marquee_container_attributes,
	array(
		'name'         => 'MarqueeHeader',
		'options'      => arts_get_marquee_options(),
		'hasAnimation' => true,
	)
);

$heading_attributes['class'][] = 'js-marquee-header__label';

if ( $is_shop ) {
	$heading_attributes['class'][] = 'woocommerce-products-header__title';
	$heading_attributes['class'][] = 'page-title';
}
?>

<div <?php Utilities::print_attributes( $section_attributes ); ?>>
	<div class="js-masthead__ajax-updated-wrapper overflow-hidden position-relative">
		<?php if ( $blog_masthead_marquee_enabled ) : ?>
			<!-- Marquee header -->
			<div <?php Utilities::print_attributes( $marquee_container_attributes ); ?>>
				<div class="marquee-header__inner js-marquee-header__wrapper">
					<div class="overflow-hidden">
						<div class="marquee-header__lane js-marquee-header__list-lane">
							<div class="marquee-header__item d-inline-block js-marquee-header__list-item">
								<h1 <?php Utilities::print_attributes( $heading_attributes ); ?>><?php echo esc_html( $page_title ); ?></h1>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- - Marquee header -->
		<?php else : ?>
			<div class="container py-2 js-masthead__ajax-updated-content">
				<h1 <?php Utilities::print_attributes( $heading_attributes ); ?>><?php echo esc_html( $page_title ); ?></h1>
			</div>
		<?php endif; ?>
	</div>
	<div class="w-100 bb-auto-opacity-solid"></div>
	<?php if ( $is_shop && function_exists( 'woocommerce_breadcrumb' ) ) : ?>
		<?php
			$args = array(
				'delimiter'   => '<span class="woocommerce-breadcrumb__divider">&nbsp;&nbsp;&#47;&nbsp;&nbsp;</span>',
				'wrap_before' => '<nav class="woocommerce-breadcrumb marquee-header__woocommerce-breadcrumb" aria-label="Breadcrumb">',
				'wrap_after'  => '</nav>',
			);
			?>
		<?php woocommerce_breadcrumb( $args ); ?>
	<?php endif; ?>
</div>
