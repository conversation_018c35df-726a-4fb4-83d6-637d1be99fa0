<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit0c3d46cfdedd55a80811a685bfd6c7ee
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'ProteusThemes\\WPContentImporter2\\' => 33,
        ),
        'M' => 
        array (
            'Monolog\\' => 8,
        ),
        'A' => 
        array (
            'Arts\\WizardSetup\\' => 17,
            'Arts\\Utilities\\' => 15,
            'Arts\\Merlin\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/Psr/Log',
        ),
        'ProteusThemes\\WPContentImporter2\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/wp-content-importer-v2/src',
        ),
        'Monolog\\' => 
        array (
            0 => __DIR__ . '/..' . '/monolog/monolog/src/Monolog',
        ),
        'Arts\\WizardSetup\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
        'Arts\\Utilities\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/utilities/src/php',
        ),
        'Arts\\Merlin\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/merlin-wp/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit0c3d46cfdedd55a80811a685bfd6c7ee::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit0c3d46cfdedd55a80811a685bfd6c7ee::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit0c3d46cfdedd55a80811a685bfd6c7ee::$classMap;

        }, null, ClassLoader::class);
    }
}
