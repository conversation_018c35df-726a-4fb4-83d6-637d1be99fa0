function _0x25e2(){const _0x29b79a=['_setEllipse','filter','_onClassChange','240fVEcMG','topLevelCurrentElement','target','_onMouseEnter','destroy','querySelector','current-menu-item','remove','timeline','_handlers','hoverDrawEffect','utilities','316204gPWMeS','_unObserveChanges','closest',':scope\x20>\x20li','element','_setHoverDrawEffect','elements','topLevelElements','100%\x200%','topLevelCurrentLink','setup','_detachEvents','396555HbtHdg','insertAdjacentHTML','disconnect','forEach','shouldPreventLinkClick','ellipse','click','svg','30FILUfQ','topLevelLinks','5oCkDKy','circleTemplate','_onClick','dataReady','100%\x20100%','79590epmpqS','_onMouseLeave','attachEvents','finally',':scope\x20>\x20li.current-menu-item:not(.menu-item-has-children)\x20>\x20a','oldValue','power2.inOut','48663UUPSkn','100%','removeEventListener','push','set','detachEvents','clear','_addSVGShape','start','bind',':scope\x20>\x20li:not(.menu-item-has-children)\x20>\x20a','4713040FFqWqv','shapes','529224NrAECn',':scope\x20>\x20a','all','384168DUwkWq','_observeChanges','length','classList','observer','class','add','options','hoverEffect','contains','isHTMLElement','beforeend','postTask',':scope\x20>\x20li.current-menu-item:not(.menu-item-has-children)'];_0x25e2=function(){return _0x29b79a;};return _0x25e2();}const _0xd1bbfd=_0x2d94;function _0x2d94(_0x5b294e,_0x3f2b82){const _0x25e2a8=_0x25e2();return _0x2d94=function(_0x2d9465,_0x5c940d){_0x2d9465=_0x2d9465-0xbb;let _0x1e02da=_0x25e2a8[_0x2d9465];return _0x1e02da;},_0x2d94(_0x5b294e,_0x3f2b82);}(function(_0x5922f1,_0x4aee26){const _0x24eb58=_0x2d94,_0x2c1587=_0x5922f1();while(!![]){try{const _0x43e00a=parseInt(_0x24eb58(0xfb))/0x1+parseInt(_0x24eb58(0xc3))/0x2+parseInt(_0x24eb58(0xec))/0x3+parseInt(_0x24eb58(0xc0))/0x4*(parseInt(_0x24eb58(0xf6))/0x5)+parseInt(_0x24eb58(0xf4))/0x6*(parseInt(_0x24eb58(0xe0))/0x7)+-parseInt(_0x24eb58(0xd4))/0x8*(parseInt(_0x24eb58(0x102))/0x9)+-parseInt(_0x24eb58(0xbe))/0xa;if(_0x43e00a===_0x4aee26)break;else _0x2c1587['push'](_0x2c1587['shift']());}catch(_0x5d187e){_0x2c1587['push'](_0x2c1587['shift']());}}}(_0x25e2,0x1f5ff));export default class MenuClassic extends BaseComponent{constructor({name:_0x3c20e4,loadInnerComponents:_0xc2b0c0,loadAfterSyncStyles:_0x50debe,parent:_0x5d1268,element:_0x3fa672}){const _0x5533ba=_0x2d94;super({'name':_0x3c20e4,'loadInnerComponents':_0xc2b0c0,'loadAfterSyncStyles':_0x50debe,'parent':_0x5d1268,'element':_0x3fa672,'defaults':{'hoverDrawEffect':!![]},'innerElements':{'topLevelElements':_0x5533ba(0xe3),'topLevelLinks':_0x5533ba(0xbd),'topLevelCurrentElement':':scope\x20>\x20li.current-menu-item:not(.menu-item-has-children)','topLevelCurrentLink':_0x5533ba(0xff)}}),this['_handlers']={'hoverIn':this[_0x5533ba(0xd7)][_0x5533ba(0xbc)](this),'hoverOut':this['_onMouseLeave'][_0x5533ba(0xbc)](this),'click':this[_0x5533ba(0xf8)][_0x5533ba(0xbc)](this),'classChange':this[_0x5533ba(0xd3)]['bind'](this)},this[_0x5533ba(0xf9)][_0x5533ba(0xfe)](()=>{const _0x400e8d=_0x5533ba;this['shapes']=[],this['tl']=gsap[_0x400e8d(0xdc)]({'defaults':{'ease':_0x400e8d(0x101),'duration':0.6}}),this[_0x400e8d(0xea)]();});}['init'](){return new Promise(_0x83769b=>{const _0x4ea3b5=_0x2d94;!!this[_0x4ea3b5(0xca)][_0x4ea3b5(0xde)]&&(this[_0x4ea3b5(0xe5)](),this['_observeChanges']()),_0x83769b(!![]);});}[_0xd1bbfd(0xd8)](){return new Promise(_0x4caf88=>{const _0x11ada0=_0x2d94;!!this[_0x11ada0(0xca)][_0x11ada0(0xde)]?(this['tl'][_0x11ada0(0x108)](),this['_detachEvents'](),this['_removeShapes']()[_0x11ada0(0xfe)](()=>{const _0x441be7=_0x11ada0;this[_0x441be7(0xe1)](),_0x4caf88(!![]);})):_0x4caf88(!![]);});}[_0xd1bbfd(0xe5)](){const _0x21c7a9=_0xd1bbfd;this['elements'][_0x21c7a9(0xf5)][_0x21c7a9(0xef)](_0x428c11=>{const _0xfef7f0=_0x21c7a9;this[_0xfef7f0(0xbf)][_0xfef7f0(0x105)](this[_0xfef7f0(0x109)](_0x428c11));const _0x503b99=this[_0xfef7f0(0xe6)][_0xfef7f0(0xe9)][0x0]&&_0x428c11===this['elements']['topLevelCurrentLink'][0x0]?!![]:![];this[_0xfef7f0(0xd1)](_0x428c11,_0x503b99),app[_0xfef7f0(0xcb)][_0xfef7f0(0xfd)](_0x428c11,this[_0xfef7f0(0xdd)]['hoverIn'],this['_handlers']['hoverOut']),_0x428c11['addEventListener'](_0xfef7f0(0xf2),this[_0xfef7f0(0xdd)]['click'],!![]);});}[_0xd1bbfd(0x109)](_0x5d7a1c){const _0x57fc1c=_0xd1bbfd;if(_0x5d7a1c&&typeof app[_0x57fc1c(0xca)][_0x57fc1c(0xf7)]==='string'&&!_0x5d7a1c[_0x57fc1c(0xd9)](_0x57fc1c(0xf3)))return _0x5d7a1c[_0x57fc1c(0xed)](_0x57fc1c(0xce),app[_0x57fc1c(0xca)][_0x57fc1c(0xf7)]);}[_0xd1bbfd(0xd1)](_0xae74cd,_0xb5878b=!![]){const _0x54313b=_0xd1bbfd;if(_0xae74cd){const _0x33f397=_0xae74cd[_0x54313b(0xd9)](_0x54313b(0xf1));_0x33f397&&gsap['set'](_0x33f397,{'drawSVG':_0xb5878b?_0x54313b(0x103):'0%'});}}['_onMouseEnter'](_0x19415f){const _0x58ed67=_0xd1bbfd,_0x1c7384=_0x19415f[_0x58ed67(0xd6)];if(app[_0x58ed67(0xdf)][_0x58ed67(0xcd)](_0x1c7384)){const _0x5b5ace=_0x1c7384[_0x58ed67(0xe2)]('a');if(_0x5b5ace){const _0x223057=[],_0x28615d=_0x19415f[_0x58ed67(0xd6)]['querySelector'](_0x58ed67(0xf1));this['tl'][_0x58ed67(0x108)](),this['elements'][_0x58ed67(0xf5)][_0x58ed67(0xef)](_0x477bc8=>{const _0x19c0c6=_0x58ed67;if(_0x19415f[_0x19c0c6(0xd6)]!==_0x477bc8){const _0xdea24=_0x477bc8['querySelector'](_0x19c0c6(0xf1));_0xdea24&&_0x223057['push'](_0xdea24);}}),_0x28615d&&this['tl']['to'](_0x28615d,{'drawSVG':_0x58ed67(0xe8)},_0x58ed67(0xbb)),_0x223057[_0x58ed67(0xc5)]&&this['tl']['to'](_0x223057,{'drawSVG':_0x58ed67(0xfa)},_0x58ed67(0xbb));}}}[_0xd1bbfd(0xf8)](_0x252a44){const _0x167e48=_0xd1bbfd;if(app[_0x167e48(0xdf)][_0x167e48(0xf0)](_0x252a44))return;_0x252a44[_0x167e48(0xd6)]['closest']('li')[_0x167e48(0xc6)][_0x167e48(0xc9)]('current-menu-item');}[_0xd1bbfd(0xfc)](){const _0x48a47f=_0xd1bbfd,_0x4a4614=[];this[_0x48a47f(0xe6)][_0x48a47f(0xf5)][_0x48a47f(0xd2)](_0x4c932b=>_0x4c932b!==this[_0x48a47f(0xe6)][_0x48a47f(0xe9)][0x0])[_0x48a47f(0xef)](_0x26de6b=>{const _0x4acd65=_0x48a47f,_0x4fc756=_0x26de6b[_0x4acd65(0xd9)](_0x4acd65(0xf1));_0x4fc756&&_0x4a4614[_0x4acd65(0x105)](_0x4fc756);}),this['tl']['clear']();if(this[_0x48a47f(0xe6)]['topLevelCurrentLink'][0x0]){const _0x24ae73=this[_0x48a47f(0xe6)][_0x48a47f(0xe9)][0x0]['querySelector'](_0x48a47f(0xf1));this['tl']['to'](_0x24ae73,{'drawSVG':_0x48a47f(0xe8)},_0x48a47f(0xbb));}_0x4a4614[_0x48a47f(0xc5)]&&this['tl']['to'](_0x4a4614,{'drawSVG':_0x48a47f(0xfa)},'start')['set'](_0x4a4614,{'drawSVG':'0%'});}[_0xd1bbfd(0xeb)](){const _0x208f02=_0xd1bbfd;this[_0x208f02(0xe6)]['topLevelLinks'][_0x208f02(0xef)](_0x16e88d=>{const _0x4843e4=_0x208f02;app[_0x4843e4(0xcb)][_0x4843e4(0x107)](_0x16e88d,this['_handlers']['hoverIn'],this[_0x4843e4(0xdd)]['hoverOut']),_0x16e88d[_0x4843e4(0x104)]('click',this[_0x4843e4(0xdd)][_0x4843e4(0xf2)]);});}['_removeShapes'](){return new Promise(_0x2d0516=>{const _0x3ceb7a=_0x2d94,_0x1c56ca=[];this[_0x3ceb7a(0xe6)][_0x3ceb7a(0xf5)][_0x3ceb7a(0xef)](_0x30246b=>{const _0x104764=_0x3ceb7a,_0x5066c3=_0x30246b[_0x104764(0xd9)]('svg');if(_0x5066c3){const _0x92ec64=scheduler[_0x104764(0xcf)](()=>{const _0xba4a88=_0x104764;_0x5066c3[_0xba4a88(0xdb)]();});_0x1c56ca[_0x104764(0x105)](_0x92ec64);}}),Promise[_0x3ceb7a(0xc2)](_0x1c56ca)['finally'](()=>{const _0x31ae13=_0x3ceb7a;this[_0x31ae13(0xbf)]=[],_0x2d0516(!![]);});});}[_0xd1bbfd(0xc4)](){const _0x49d23f=_0xd1bbfd;this['observer']=new MutationObserver(this[_0x49d23f(0xdd)]['classChange']),this[_0x49d23f(0xe6)][_0x49d23f(0xe7)]['forEach'](_0x288b09=>{const _0xb9bebc=_0x49d23f;this['observer']['observe'](_0x288b09,{'attributes':!![],'attributeFilter':[_0xb9bebc(0xc8)],'attributeOldValue':!![]});});}[_0xd1bbfd(0xe1)](){const _0x24f384=_0xd1bbfd;this[_0x24f384(0xc7)]&&(this[_0x24f384(0xc7)][_0x24f384(0xee)](),this[_0x24f384(0xc7)]=null);}['_onClassChange'](_0x2d39b1){const _0x249ce8=_0xd1bbfd;_0x2d39b1[_0x249ce8(0xef)](_0x2b5534=>{const _0xc43a7c=_0x249ce8,_0x8c28b7=_0x2b5534[_0xc43a7c(0xd6)];if(_0x8c28b7[_0xc43a7c(0xc6)]['contains'](_0xc43a7c(0xda))){this['tl']['clear'](),this['elements'][_0xc43a7c(0xd5)][0x0]=_0x8c28b7,this[_0xc43a7c(0xe6)]['topLevelCurrentLink'][0x0]=_0x8c28b7[_0xc43a7c(0xd9)](_0xc43a7c(0xc1));const _0x346f5a=[],_0x372dc7=_0x8c28b7[_0xc43a7c(0xd9)](_0xc43a7c(0xf1));this['elements']['topLevelElements'][_0xc43a7c(0xef)](_0x3949b7=>{const _0x411ce6=_0xc43a7c;if(_0x8c28b7!==_0x3949b7){const _0x5c978f=_0x3949b7[_0x411ce6(0xd9)](_0x411ce6(0xf1));_0x5c978f&&_0x346f5a[_0x411ce6(0x105)](_0x5c978f);}}),_0x372dc7&&this['tl']['to'](_0x372dc7,{'drawSVG':_0xc43a7c(0xe8)},_0xc43a7c(0xbb)),_0x346f5a[_0xc43a7c(0xc5)]&&this['tl']['to'](_0x346f5a,{'drawSVG':_0xc43a7c(0xfa)},_0xc43a7c(0xbb));}if(!_0x8c28b7[_0xc43a7c(0xc6)][_0xc43a7c(0xcc)](_0xc43a7c(0xda))&&_0x2b5534[_0xc43a7c(0x100)]['includes'](_0xc43a7c(0xda))){this['tl'][_0xc43a7c(0x108)](),this[_0xc43a7c(0xe6)][_0xc43a7c(0xd5)][0x0]=this[_0xc43a7c(0xe4)]['querySelector'](_0xc43a7c(0xd0)),this[_0xc43a7c(0xe6)][_0xc43a7c(0xe9)][0x0]=this[_0xc43a7c(0xe4)][_0xc43a7c(0xd9)](_0xc43a7c(0xff));const _0x1471b1=_0x8c28b7[_0xc43a7c(0xd9)](_0xc43a7c(0xf1));this['tl']['to'](_0x1471b1,{'drawSVG':_0xc43a7c(0xfa)},_0xc43a7c(0xbb))[_0xc43a7c(0x106)](_0x1471b1,{'drawSVG':'0%'});}});}}