<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_cursor_follower_options' ) ) {
	/**
	 * Get the cursor follower options.
	 *
	 * @return array|false $options
	 */
	function arts_get_cursor_follower_options() {
		$options                 = false;
		$cursor_follower_enabled = Utilities::get_kit_settings( 'cursor_follower_enabled', false );

		if ( $cursor_follower_enabled ) {
			$cursor_follower_hide_iframes_enabled       = Utilities::get_kit_settings( 'cursor_follower_hide_iframes_enabled', true );
			$cursor_follower_animation_duration         = Utilities::get_kit_settings( 'cursor_follower_animation_duration', 0.25 );
			$cursor_follower_passive_listeners_enabled  = Utilities::get_kit_settings( 'cursor_follower_passive_listeners_enabled', true );
			$cursor_follower_elastic_effect_enabled     = Utilities::get_kit_settings( 'cursor_follower_elastic_effect_enabled', true );
			$cursor_follower_elastic_effect_factor      = Utilities::get_kit_settings( 'cursor_follower_elastic_effect_factor', 1.5 );
			$cursor_follower_highlight_effect_enabled   = Utilities::get_kit_settings( 'cursor_follower_highlight_effect_enabled', true );
			$cursor_follower_trailing_effect_enabled    = Utilities::get_kit_settings( 'cursor_follower_trailing_effect_enabled', true );
			$cursor_follower_trailing_effect_factor     = Utilities::get_kit_settings( 'cursor_follower_trailing_effect_factor', 0.2 );
			$cursor_follower_click_scale_effect_enabled = Utilities::get_kit_settings( 'cursor_follower_click_scale_effect_enabled', true );
			$cursor_follower_click_scale_effect_factor  = Utilities::get_kit_settings( 'cursor_follower_click_scale_effect_factor', 0.8 );
			$cursor_follower_gsap_raf_enabled           = Utilities::get_kit_settings( 'cursor_follower_gsap_raf_enabled', false );
			$cursor_follower_use_css_vars_enabled       = Utilities::get_kit_settings( 'cursor_follower_use_css_vars_enabled', true );

			$highlight = false;

			if ( $cursor_follower_highlight_effect_enabled ) {
				$cursor_follower_highlight_scale   = Utilities::get_kit_settings( 'cursor_follower_highlight_scale', 90 );
				$cursor_follower_highlight_opacity = Utilities::get_kit_settings( 'cursor_follower_highlight_opacity', 0.2 );

				$highlight = array(
					'includeClass' => 'cursor-highlight',
					'excludeClass' => 'cursor-no-highlight',
					'scale'        => floatval( $cursor_follower_highlight_scale ) . 'px',
					'opacity'      => floatval( $cursor_follower_highlight_opacity ),
				);
			}

			$options = array(
				'hideNativeElementSelector' => 'body',
				'hideIFramesHover'          => boolval( $cursor_follower_hide_iframes_enabled ),
				'animationDuration'         => floatval( $cursor_follower_animation_duration ),
				'trailing'                  => $cursor_follower_trailing_effect_enabled ? floatval( $cursor_follower_trailing_effect_factor ) : 1,
				'elastic'                   => $cursor_follower_elastic_effect_enabled ? floatval( $cursor_follower_elastic_effect_factor ) : 0,
				'highlight'                 => $highlight,
				'clickScale'                => $cursor_follower_click_scale_effect_enabled ? floatval( $cursor_follower_click_scale_effect_factor ) : false,
				'matchMedia'                => '(hover: hover) and (pointer: fine)',
				'passiveListeners'          => boolval( $cursor_follower_passive_listeners_enabled ),
				'useCSSVars'                => boolval( $cursor_follower_use_css_vars_enabled ),
				'useGSAPRaf'                => boolval( $cursor_follower_gsap_raf_enabled ),
			);
		}

		return $options;
	}
}
