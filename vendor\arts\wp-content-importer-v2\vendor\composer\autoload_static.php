<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit44c36648e33e73738e606722b2b3e0e5
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'ProteusThemes\\WPContentImporter2\\' => 33,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'ProteusThemes\\WPContentImporter2\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit44c36648e33e73738e606722b2b3e0e5::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit44c36648e33e73738e606722b2b3e0e5::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit44c36648e33e73738e606722b2b3e0e5::$classMap;

        }, null, ClassLoader::class);
    }
}
