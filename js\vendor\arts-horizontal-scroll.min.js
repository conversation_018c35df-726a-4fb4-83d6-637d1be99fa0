(()=>{var t={110:t=>{function e(t,e,i){var r,s,n,o,a;function l(){var h=Date.now()-o;h<e&&h>=0?r=setTimeout(l,e-h):(r=null,i||(a=t.apply(n,s),n=s=null))}null==e&&(e=100);var h=function(){n=this,s=arguments,o=Date.now();var h=i&&!r;return r||(r=setTimeout(l,e)),h&&(a=t.apply(n,s),n=s=null),a};return h.clear=function(){r&&(clearTimeout(r),r=null)},h.flush=function(){r&&(a=t.apply(n,s),n=s=null,clearTimeout(r),r=null)},h}e.debounce=e,t.exports=e},454:t=>{"use strict";var e=function(t){return function(t){return!!t&&"object"==typeof t}(t)&&!function(t){var e=Object.prototype.toString.call(t);return"[object RegExp]"===e||"[object Date]"===e||function(t){return t.$$typeof===i}(t)}(t)},i="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function r(t,e){return!1!==e.clone&&e.isMergeableObject(t)?a((i=t,Array.isArray(i)?[]:{}),t,e):t;var i}function s(t,e,i){return t.concat(e).map((function(t){return r(t,i)}))}function n(t){return Object.keys(t).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return Object.propertyIsEnumerable.call(t,e)})):[]}(t))}function o(t,e){try{return e in t}catch(t){return!1}}function a(t,i,l){(l=l||{}).arrayMerge=l.arrayMerge||s,l.isMergeableObject=l.isMergeableObject||e,l.cloneUnlessOtherwiseSpecified=r;var h=Array.isArray(i);return h===Array.isArray(t)?h?l.arrayMerge(t,i,l):function(t,e,i){var s={};return i.isMergeableObject(t)&&n(t).forEach((function(e){s[e]=r(t[e],i)})),n(e).forEach((function(n){(function(t,e){return o(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))})(t,n)||(o(t,n)&&i.isMergeableObject(e[n])?s[n]=function(t,e){if(!e.customMerge)return a;var i=e.customMerge(t);return"function"==typeof i?i:a}(n,i)(t[n],e[n],i):s[n]=r(e[n],i))})),s}(t,i,l):r(i,l)}a.all=function(t,e){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce((function(t,i){return a(t,i,e)}),{})};var l=a;t.exports=l},567:(t,e,i)=>{"use strict";i.r(e)},438:(t,e,i)=>{"use strict";i.r(e)}},e={};function i(r){var s=e[r];if(void 0!==s)return s.exports;var n=e[r]={exports:{}};return t[r](n,n.exports,i),n.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};(()=>{"use strict";i.d(r,{default:()=>d});var t=i(454);const e={init:!0,mode:"modular",wrapperElementSelector:'[data-arts-horizontal-scroll="wrapper"]',sectionElementsSelector:'[data-arts-horizontal-scroll="section"]',matchMedia:!1,resizeObserver:300,toggleInViewClass:!1,hasSmoothScrolling:()=>"undefined"!=typeof ScrollSmoother&&void 0!==ScrollSmoother.get()};class s{static getElementByStringSelector(t,e=document){if("string"==typeof t){const i=e.querySelector(t);if(i&&null!==i)return i}if(s.isHTMLElement(t))return t}static isHTMLElement(t,e="Element"){if(!t)return!1;let i=t.__proto__;for(;null!==i;){if(i.constructor.name===e)return!0;i=i.__proto__}return!1}static getElementsInContainer(t,e){return"string"==typeof e&&t&&null!==t?[...t.querySelectorAll(e)]:"object"==typeof e?[...e]:void 0}}class n{constructor({container:t,attributeSelector:i="data-arts-horizontal-scroll-options",options:r}){this._data=e,s.isHTMLElement(t)&&this._transformOptions({container:t,attributeSelector:i,options:r})}get data(){return this._data}set data(t){this._data=t}_transformOptions({container:i,attributeSelector:r,options:s}){if(!i)return{};let o={};if(s&&e&&(o=t(e,s)),r){let e;e="DATA"===r?function(t,e={separator:"-",pattern:/^/}){let i={};var r;return void 0===e.separator&&(e.separator="-"),Array.prototype.slice.call(t.attributes).filter((r=e.pattern,function(t){let e;return e=/^data\-/.test(t.name),void 0===r?e:e&&r.test(t.name.slice(5))})).forEach((function(t){t.name.slice(5).split(e.separator).reduce((function(e,i,r,s){return"data"===i?e:(r===s.length-1?e[i]=t.value:e[i]=e[i]||{},e[i])}),i)})),i}(i):n.parseOptionsStringObject(i.getAttribute(r)),e&&0!==Object.keys(e).length&&(e=n.transformPluginOptions(e),o=t(o,e))}this.data=o}static parseOptionsStringObject(t){let e={};if(!t)return e;try{e=JSON.parse(n.convertStringToJSON(t))}catch(e){console.warn(`${t} is not a valid parameters object`)}return e}static convertStringToJSON(t){if(t)return t.replace(/'/g,'"').replace(/(?=[^"]*(?:"[^"]*"[^"]*)*$)(\w+:)|(\w+ :)/g,(function(t){return'"'+t.substring(0,t.length-1)+'":'}))}static transformPluginOptions(t){return t}}class o{constructor({element:t,wrapper:e,options:i,hasSmoothScrolling:r,hasModularScrolling:s}){this._hasSmoothScrolling=!1,this._hasModularScrolling=!1,this.element=t,this.wrapper=e,this.options=i,this.hasSmoothScrolling=r,this.hasModularScrolling=s}init(){return new Promise((t=>{this._updateHeight().then((()=>this._createScrollTriggerInstance())).then((()=>t(!0)))}))}get element(){return this._element}set element(t){this._element=t}get wrapper(){return this._wrapper}set wrapper(t){this._wrapper=t}get options(){return this._options}set options(t){this._options=t}get hasSmoothScrolling(){return this._hasSmoothScrolling}set hasSmoothScrolling(t){this._hasSmoothScrolling=t}get hasModularScrolling(){return this._hasModularScrolling}set hasModularScrolling(t){this._hasModularScrolling=t}_updateHeight(){return new Promise((t=>{gsap.set(this.element,{overflow:"hidden",clipPath:"inset(0px)",onComplete:()=>t(!0)})}))}get triggerInstance(){return this._triggerInstance}set triggerInstance(t){this._triggerInstance=t}_createScrollTriggerInstance(){return new Promise((t=>{if(this.triggerInstance)return void t(!0);const e=new CustomEvent("scroll");this.triggerInstance=ScrollTrigger.create({animation:this.wrapper.animation,trigger:this.element,pin:!this.hasSmoothScrolling&&this.wrapper.element,pinSpacing:"margin",pinType:"fixed",scrub:!0,toggleClass:this.hasModularScrolling?null:{targets:this.wrapper.element,className:this.options.toggleInViewClass.toString()},invalidateOnRefresh:!0,onUpdate:()=>{this.wrapper.element.dispatchEvent(e)},start:()=>"top top",end:()=>`top+=${this.wrapper.rect.scrollWidth-(this.wrapper.rect.width-this.wrapper.rect.height)} bottom`}),t(!0)}))}_killScrollTriggerInstance(){this.triggerInstance&&"function"==typeof this.triggerInstance.kill&&(this.triggerInstance.kill(!0),this.triggerInstance=null)}_refreshScrollTriggerInstance(){this.triggerInstance&&"function"==typeof this.triggerInstance.refresh&&this.triggerInstance.refresh()}update(){return new Promise((t=>{this._updateHeight().then((()=>{this._refreshScrollTriggerInstance(),t(!0)}))}))}destroy(){return new Promise((t=>{this._killScrollTriggerInstance(),gsap.set(this.element,{clearProps:"overflow, clip-path",onComplete:()=>t(!0)})}))}}class a{constructor({element:t,container:e,wrapper:i,hasSmoothScrolling:r,toggleInViewClass:s}){this._animation=gsap.timeline({paused:!0,defaults:{ease:"none"}}),this._hasSmoothScrolling=!1,this._toggleInViewClass=!1,this.element=t,this.container=e,this.wrapper=i,this.hasSmoothScrolling=r,this.toggleInViewClass=s}init(){return new Promise((t=>{this._updateRect().then((()=>this._updatePosition())).then((()=>this._updateTriggerData())).then((()=>this._updateAnimation())).then((()=>this._createScrollTriggerInstance())).then((()=>t(!0)))}))}get element(){return this._element}set element(t){this._element=t}get container(){return this._container}set container(t){this._container=t}get wrapper(){return this._wrapper}set wrapper(t){this._wrapper=t}get hasSmoothScrolling(){return this._hasSmoothScrolling}set hasSmoothScrolling(t){this._hasSmoothScrolling=t}get toggleInViewClass(){return this._toggleInViewClass}set toggleInViewClass(t){this._toggleInViewClass=t}toggleInViewClasses(t){"string"==typeof this.toggleInViewClass&&function(t,e,i){if(t&&s.isHTMLElement(t)){const r=e.split(" ");r.length&&r.map((e=>t.classList.toggle(e,i)))}}(this.element,this.toggleInViewClass,t)}_updateInViewClass(){return new Promise((t=>{if("string"==typeof this.toggleInViewClass){const t=this.element.getBoundingClientRect();t.right-this.wrapper.rect.left<=0||t.left>this.wrapper.rect.width+this.wrapper.rect.left?this.toggleInViewClasses(!1):this.toggleInViewClasses(!0)}t(!0)}))}get rect(){return this._rect}set rect(t){this._rect=t}_updateRect(){return new Promise((t=>{const{offsetLeft:e,offsetHeight:i,offsetWidth:r}=this.element;this.rect={left:e,height:i,width:r},this.rect.right=this.rect.left+this.rect.width,t(!0)}))}get position(){return this._position}set position(t){this._position=t}_updatePosition(){return new Promise((t=>{let e="scrollScreen";this.rect.left<=this.wrapper.rect.width&&(e="startScreen"),this.rect.right>this.wrapper.rect.width*(this.wrapper.rect.screenViews-1)&&(e="endScreen"),this.position=e,t(!0)}))}get triggerData(){return this._triggerData}set triggerData(t){this._triggerData=t}_updateTriggerData(){return new Promise((t=>{const e=this.wrapper.rect.maxScrollWidth>2*this.wrapper.rect.width?this.rect.left-this.wrapper.rect.width-(this.hasSmoothScrolling?this.wrapper.rect.left:0):0;let i=0;switch(this.hasSmoothScrolling&&(i=this.wrapper.rect.left),this.position){case"startScreen":this.triggerData={fromX:0,fromY:0,toX:-this.rect.right+i,toY:this.hasSmoothScrolling?this.rect.right-this.wrapper.rect.left:-i,start:"top top",end:`top+=${this.rect.right-i} top`};break;case"endScreen":this.triggerData={fromX:-e,fromY:this.hasSmoothScrolling?e:0,toX:0-this.wrapper.rect.maxScrollWidth,toY:this.hasSmoothScrolling?this.wrapper.rect.maxScrollWidth:-i,start:`top+=${e} top`,end:`top+=${this.wrapper.rect.maxScrollWidth-0} top`};break;default:this.triggerData={fromX:-e,fromY:this.hasSmoothScrolling?e:0,toX:-this.rect.right+i,toY:this.hasSmoothScrolling?this.rect.right-this.wrapper.rect.left:-i,start:`top+=${e} top`,end:`top+=${this.rect.right-i} top`}}t(!0)}))}get animation(){return this._animation}set animation(t){this._animation=t}_updateAnimation(){return new Promise((t=>{this.animation.fromTo(this.element,{"--translateX":()=>`${this.triggerData.fromX}px`,"--translateY":()=>`${this.triggerData.fromY}px`,immediateRender:!0},{"--translateX":()=>`${this.triggerData.toX}px`,"--translateY":()=>`${this.triggerData.toY}px`}),t(!0)}))}get triggerInstance(){return this._triggerInstance}set triggerInstance(t){this._triggerInstance=t}_createScrollTriggerInstance(){return new Promise((t=>{this.triggerInstance&&this.triggerInstance.isActive||("string"==typeof this.toggleInViewClass&&"startScreen"===this.position&&this.toggleInViewClasses(!0),this.triggerInstance=ScrollTrigger.create({animation:this.animation,trigger:this.container.element,scrub:!0,invalidateOnRefresh:!0,start:()=>this.triggerData.start,end:()=>this.triggerData.end,onEnter:()=>{"startScreen"!==this.position&&"endScreen"!==this.position||this.toggleInViewClasses(!0)},onEnterBack:()=>{"startScreen"!==this.position&&"endScreen"!==this.position||this.toggleInViewClasses(!0)},onLeave:()=>{"startScreen"===this.position&&this.toggleInViewClasses(!1)},onLeaveBack:()=>{"endScreen"===this.position&&this.toggleInViewClasses(!1)},onToggle:t=>{"scrollScreen"===this.position&&this.toggleInViewClasses(t.isActive)},onRefreshInit:()=>{this.element.style.removeProperty("--translateX"),this.element.style.removeProperty("--translateY")}}),t(!0))}))}_killScrollTriggerInstance(){this.triggerInstance&&"function"==typeof this.triggerInstance.kill&&this.triggerInstance.kill(!0,!1)}_refreshScrollTriggerInstance(){this.triggerInstance&&"function"==typeof this.triggerInstance.refresh&&this.triggerInstance.refresh()}update(){return new Promise((t=>{this.triggerInstance.disable(!0,!1),this._updateRect().then((()=>this._updatePosition())).then((()=>this._updateTriggerData())).then((()=>this._updateInViewClass())).then((()=>{this.triggerInstance.enable(!0,!0),t(!0)}))}))}destroy(){return new Promise((t=>{this._killScrollTriggerInstance(),setTimeout((()=>{this.toggleInViewClasses(!1),t(!0)}),0)}))}}class l{constructor({element:t,options:e,hasSmoothScrolling:i,hasModularScrolling:r}){this._animation=gsap.timeline({paused:!0,defaults:{ease:"none"}}),this._hasSmoothScrolling=!1,this._hasModularScrolling=!1,this.element=t,this.options=e,this.hasSmoothScrolling=i,this.hasModularScrolling=r}init(){return new Promise((t=>{this._updateRect().then((()=>this._updateTotalWidth())).then((()=>this._updateScreenViews())).then((()=>{this.hasModularScrolling&&!this.hasSmoothScrolling&&gsap.set(this.element,{overflow:"hidden"}),this.hasModularScrolling||this._updateAnimation(),t(!0)}))}))}get element(){return this._element}set element(t){this._element=t}get options(){return this._options}set options(t){this._options=t}get hasSmoothScrolling(){return this._hasSmoothScrolling}set hasSmoothScrolling(t){this._hasSmoothScrolling=t}get hasModularScrolling(){return this._hasModularScrolling}set hasModularScrolling(t){this._hasModularScrolling=t}get rect(){return this._rect}set rect(t){this._rect=t}_updateRect(){return new Promise((t=>{const{offsetLeft:e,offsetHeight:i,offsetWidth:r,scrollWidth:s}=this.element;this.rect={left:e,height:i,width:r,scrollWidth:s},t(!0)}))}_updateTotalWidth(){return new Promise((t=>{const e=[];let i=0;for(const t of this.element.querySelectorAll(":scope > *")){const r=new Promise((e=>{i+=t.offsetWidth,e(!0)}));e.push(r)}Promise.all(e).then((()=>{this.rect.scrollWidth=i,t(!0)}))}))}_updateScreenViews(){return new Promise((t=>{this.rect.right=this.rect.left+this.rect.width,this.rect.maxScrollWidth=Math.max(0,this.rect.scrollWidth-this.rect.width),this.rect.screenViews=this.rect.scrollWidth/this.rect.width,t(!0)}))}get animation(){return this._animation}set animation(t){this._animation=t}_updateAnimation(){this.animation.fromTo(this.element,{"--translateX":"0px","--translateY":"0px"},{"--translateX":()=>`-${this.rect.maxScrollWidth}px`,"--translateY":()=>this.hasSmoothScrolling?`${this.rect.maxScrollWidth}px`:"0px"})}update(){return new Promise((t=>{this._updateRect().then((()=>this._updateTotalWidth())).then((()=>this._updateScreenViews())).then((()=>t(!0)))}))}destroy(){return new Promise((t=>{this.animation.kill(),this.hasModularScrolling&&!this.hasSmoothScrolling?gsap.set(this.element,{clearProps:"overflow",overwrite:!0,onComplete:()=>t(!0)}):t(!0)}))}}class h{constructor({elements:t,callback:e}){this.elements=t,this.callback=e,this.elements.length&&this._hasCallback()&&this.init()}set instance(t){this._instance=t}get instance(){return this._instance}set callback(t){this._callback=t}get callback(){return this._callback}set elements(t){this._elements=t}get elements(){return this._elements}init(){this.instance=new ResizeObserver(this._onUpdate.bind(this)),this._observeElements()}destroy(){this.instance&&(this.instance.disconnect(),this.instance=null)}_onUpdate(t){const e=[];for(const i of t)e.push(i.target);this.callback(e)}_observeElements(){if(this.instance)for(let t=0;t<this.elements.length;t++)this.instance.observe(this.elements[t])}_hasCallback(){return"function"==typeof this.callback}}var c=i(110),g=i.n(c);class p{constructor({container:t,options:e={}}){this._enabled=!1,this._initialized=!1,this._resized=!1,this._updating=!1,this._sections=[],t&&e&&(this._updateContainerElement(t),this._updateOptions(t,e),this._updateWrapperElement(this.options.wrapperElementSelector),this._updateSectionsElements(this.options.sectionElementsSelector),this._updateModularScrollingEnabled(),this._updateSmoothScrollingEnabled())}init(){}destroy(){}update(){}get enabled(){return this._enabled}set enabled(t){this._enabled=t}get initialized(){return this._initialized}set initialized(t){this._initialized=t,this._initialized?(this.containerElement.classList.add("has-horizontal-scroll"),this.containerElement.classList.remove("no-horizontal-scroll")):(this.containerElement.classList.add("no-horizontal-scroll"),this.containerElement.classList.remove("has-horizontal-scroll"))}set resize(t){this._resize=t}get resize(){return this._resize}get resized(){return this._resized}set resized(t){this._resized=t}_updateResize(){if(this.options.resizeObserver){let t,e=0;"number"==typeof this.options.resizeObserver&&(e=this.options.resizeObserver),t=e>0?g()(this._onResize.bind(this),e):this._onResize.bind(this),this.resize=new h({elements:[this.container.element,...this.sectionsElements],callback:t})}}_onResize(){this.enabled&&(this.resized&&this.update(),this.resized=!0)}set updating(t){this._updating=t}get updating(){return this._updating}get containerElement(){return this._containerElement}set containerElement(t){this._containerElement=t}_updateContainerElement(t){this.containerElement=s.getElementByStringSelector(t)}get container(){return this._container}set container(t){this._container=t}_updateContainer(){return new Promise((t=>{this.container=new o({element:this.containerElement,wrapper:this.wrapper,options:this.options,hasSmoothScrolling:this.smoothScrollingEnabled,hasModularScrolling:this.modularScrollingEnabled}),this.container.init().then((()=>t(!0)))}))}get wrapperElement(){return this._wrapperElement}set wrapperElement(t){this._wrapperElement=t}_updateWrapperElement(t){this.wrapperElement=s.getElementByStringSelector(t,this.containerElement)}get wrapper(){return this._wrapper}set wrapper(t){this._wrapper=t}_updateWrapper(){return new Promise((t=>{this.wrapper=new l({element:this.wrapperElement,options:this.options,hasSmoothScrolling:this.smoothScrollingEnabled,hasModularScrolling:this.modularScrollingEnabled}),this.wrapper.init().then((()=>t(!0)))}))}get sectionsElements(){return this._sectionsElements}set sectionsElements(t){this._sectionsElements=t}_updateSectionsElements(t){this.sectionsElements=[...this.containerElement.querySelectorAll(t)]}get sections(){return this._sections}set sections(t){this._sections=t}_updateSections(){return new Promise((t=>{const e=[];if(this.modularScrollingEnabled)for(const t of this.sectionsElements){const i=new a({element:t,wrapper:this.wrapper,container:this.container,hasSmoothScrolling:this.smoothScrollingEnabled,toggleInViewClass:this.options.toggleInViewClass});this.sections.push(i),e.push(i.init())}Promise.all(e).then((()=>t(!0)))}))}get options(){return this._options}set options(t){this._options=t}_updateOptions(t,e){this.options=new n({container:t,attributeSelector:"data-arts-horizontal-scroll-options",options:e}).data}get matchMedia(){return this._matchMedia}set matchMedia(t){this._matchMedia=t}get smoothScrollingEnabled(){return this._smoothScrollingEnabled}set smoothScrollingEnabled(t){this._smoothScrollingEnabled=t}_updateSmoothScrollingEnabled(){"function"==typeof this.options.hasSmoothScrolling?this.smoothScrollingEnabled=this.options.hasSmoothScrolling():this.smoothScrollingEnabled=Boolean(this.options.hasSmoothScrolling)}get modularScrollingEnabled(){return this._modularScrollingEnabled}set modularScrollingEnabled(t){this._modularScrollingEnabled=t}_updateModularScrollingEnabled(){this.modularScrollingEnabled=!1,"modular"===this.options.mode&&(this.sectionsElements.length>0?this.modularScrollingEnabled=!0:console.warn(`Couldn't find inner sections for modular scrolling with given selector "${this.options.sectionElementsSelector}". Falling back to wrapper scrolling.`))}}class u{constructor({condition:t,callbackMatch:e,callbackNoMatch:i}){this._handlers={change:this._onChange.bind(this)},this.condition=t,this.callbacks={match:e,noMatch:i},(this._hasMatchFunction()||this._hasNoMatchFunction())&&this.init()}init(){this.mediaQuery=this._addMatchMedia(),this._attachEvents()}destroy(){this._detachEvents(),this.mediaQuery=null}get mediaQuery(){return this._mediaQuery}set mediaQuery(t){this._mediaQuery=t}get callbacks(){return this._callbacks}set callbacks(t){this._callbacks=t}get condition(){return this._condition}set condition(t){this._condition=t}_hasMatchFunction(){return"function"==typeof this.callbacks.match}_hasNoMatchFunction(){return"function"==typeof this.callbacks.noMatch}_addMatchMedia(){return window.matchMedia(`${this.condition}`)}_attachEvents(){"function"==typeof this.mediaQuery.addEventListener?this.mediaQuery.addEventListener("change",this._handlers.change):this.mediaQuery.addListener(this._handlers.change)}_detachEvents(){"function"==typeof this.mediaQuery.removeEventListener?this.mediaQuery.removeEventListener("change",this._handlers.change):this.mediaQuery.removeListener(this._handlers.change)}_onChange(t){t.matches?this._hasMatchFunction()&&this.callbacks.match():t.matches||this._hasNoMatchFunction()&&this.callbacks.noMatch()}}gsap.registerPlugin(ScrollTrigger),i(567),i(438);const d=class extends p{constructor(t=document.querySelector('[data-arts-horizontal-scroll="container"]'),e={}){super({container:t,options:e}),this.ready=new Promise((t=>{this.setReady=t})),this.options.init?this.options.matchMedia&&!window.matchMedia(`${this.options.matchMedia}`).matches?(this.initialized=!1,this.matchMedia=new u({condition:this.options.matchMedia,callbackMatch:this.init.bind(this)})):this.init():this.initialized=!1}init(){return new Promise((t=>{this.initialized||(this.matchMedia&&this.matchMedia.destroy(),this.options.matchMedia&&(this.matchMedia=new u({condition:this.options.matchMedia,callbackMatch:this.init.bind(this),callbackNoMatch:this.destroy.bind(this)})),this.initialized=!0,this._updateWrapper().then((()=>this._updateContainer())).then((()=>this._updateSections())).then((()=>{this._updateResize(),this.enabled=!0,this.setReady(),t(!0)})))}))}destroy(){return new Promise((t=>{if(!this.initialized)return;const e=[];this.enabled=!1,this.initialized=!1,this.resized=!1,e.push(this.container.destroy()),e.push(this.wrapper.destroy()),this.modularScrollingEnabled&&this.sections.forEach((t=>{e.push(t.destroy())})),Promise.all(e).then((()=>{this.modularScrollingEnabled&&(this.sections=[]),this.resize.destroy(),t(!0)}))}))}update(){return new Promise((t=>{const e=[];this.wrapper.update().then((()=>this.container.update())).then((()=>{this.modularScrollingEnabled?(this.sections.forEach((t=>{e.push(t.update())})),Promise.all(e).then((()=>t(!0))).catch((()=>t(!0)))):t(!0)}))}))}getContainerAnimation(t){if("wrapper"===this.options.mode)return this.wrapper?this.wrapper.animation:null;if("modular"===this.options.mode&&t){const e=t.closest(this.options.sectionElementsSelector);if(e){const t=this.sections.filter((t=>t.element===e));if(t&&t[0])return t[0].animation}}}setReady(){}}})(),this.ArtsHorizontalScroll=r.default})();