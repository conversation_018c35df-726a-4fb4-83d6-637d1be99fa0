<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'elementor/editor/after_enqueue_scripts', 'arts_enqueue_elementor_preview_js', 99 );
if ( ! function_exists( 'arts_enqueue_elementor_preview_js' ) ) {
	/**
	 * Enqueue the theme scripts for Elementor editor
	 *
	 * @return void
	 */
	function arts_enqueue_elementor_preview_js() {
		wp_enqueue_script(
			'asli-elementor-preview-document-settings',
			esc_url( ARTS_THEME_URL . '/js/vendor/elementor-preview-document-settings.js' ),
			array(),
			ARTS_THEME_VERSION,
			true
		);
	}
}
