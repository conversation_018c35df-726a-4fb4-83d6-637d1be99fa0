<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_lazy_image_attributes' ) ) {
	/**
	 * Generates lazy loading image attributes.
	 *
	 * @param array $args {
	 *   Arguments for generating image attributes.
	 *
	 *   @type int    $id                Attachment ID.
	 *   @type string $size              Image size.
	 *   @type string $type              Type of image (e.g., 'image', 'background', 'texture').
	 *   @type array  $image             Additional image attributes.
	 *   @type array  $lazy_wrapper      Additional lazy wrapper attributes.
	 *   @type bool   $has_lazy_wrapper  Whether to include lazy wrapper.
	 * }
	 * @return array Image attributes for lazy loading.
	 */
	function arts_get_lazy_image_attributes( $args ) {
		$attributes = array(
			'type'         => 'image',
			'image'        => array(),
			'lazy_wrapper' => array(),
		);

		if ( ! $args['id'] ) {
			return $attributes;
		}

		$lazy_placeholder_src       = '#';
		$lazy_placeholder_type      = Utilities::get_kit_settings( 'lazy_placeholder_type', 'inline' );
		$lazy_placeholder_inline    = Utilities::get_kit_settings( 'lazy_placeholder_inline', "data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='100%25'%20height='100%25'%3E%3C/svg%3E" );
		$lazy_placeholder_image_url = Utilities::get_kit_settings( 'lazy_placeholder_image_url', '' );

		if ( $lazy_placeholder_type === 'inline' && ! empty( $lazy_placeholder_inline ) ) {
			$lazy_placeholder_src = $lazy_placeholder_inline;
		}

		if ( $lazy_placeholder_type === 'custom_image' && ! empty( $lazy_placeholder_image_url ) ) {
			$lazy_placeholder_src = $lazy_placeholder_image_url;
		}

		$full_size_images_enabled = Utilities::get_kit_settings( 'full_size_images_enabled', false );
		$attrs                    = wp_get_attachment_image_src( $args['id'], $args['size'] );
		$srcset                   = '';
		$sizes                    = '';

		if ( ! $full_size_images_enabled ) {
			$srcset = wp_get_attachment_image_srcset( $args['id'], $args['size'] );
			$sizes  = wp_get_attachment_image_sizes( $args['id'], $args['size'] );
		}

		if ( $args['image'] ) {
			$attributes['image'] = Utilities::parse_args_recursive( $attributes['image'], $args['image'] );

			if ( $args['type'] === 'background' ) {
				$attributes['image']['class'][] = 'of-cover-absolute';
			}

			if ( $args['type'] === 'image' || $args['type'] === 'image_limited' ) {
				$attributes['image']['class'][] = 'of-contain';
			}
		}

		if ( $args['lazy_wrapper'] ) {
			$attributes['lazy_wrapper'] = Utilities::parse_args_recursive( $attributes['lazy_wrapper'], $args['lazy_wrapper'] );

			if ( $args['type'] === 'image_limited' ) {
				$attributes['lazy_wrapper']['class'][] = 'has-limited-width';
			}
		}

		$attributes['image']['src'] = $lazy_placeholder_src;

		if ( $attrs ) {
			if ( $attrs[0] ) {
				if ( $args['type'] === 'texture' ) {
					// $attributes['image']['src']              = $attrs[0];
					$attributes['image']['fetchpriority']    = 'low';
					$attributes['image']['data-texture-src'] = $attrs[0];
					$attributes['image']['crossorigin']      = 'anonymous';
				} else {
					$attributes['image']['data-src'] = $attrs[0];
				}
			}

			if ( $attrs[1] ) {
				$attributes['image']['width'] = $attrs[1];
			}

			if ( $attrs[2] ) {
				$attributes['image']['height'] = $attrs[2];
			}
		}

		if ( ! $full_size_images_enabled ) {
			if ( $srcset ) {
				$attributes['image']['data-srcset'] = $srcset;
			}

			if ( $sizes ) {
				$attributes['image']['data-sizes'] = $sizes;
			}
		}

		$attributes['image']['alt'] = get_post_meta( $args['id'], '_wp_attachment_image_alt', true );

		if ( $args['has_lazy_wrapper'] ) {
			$should_set_aspect_ratio = true;

			if ( array_key_exists( 'class', $attributes['image'] ) && is_array( $attributes['image']['class'] ) ) {
				$should_set_aspect_ratio = ! ( in_array( 'of-cover-absolute', $attributes['image']['class'] ) );
			}

			if ( $should_set_aspect_ratio && $attrs && $attrs[1] && $attrs[2] ) {
				$attributes['lazy_wrapper']['class'][] = 'has-aspect-ratio';
				$attributes['lazy_wrapper']['style']   = "--media-width:$attrs[1]; --media-height:$attrs[2];";
			}
		}

		return $attributes;
	}
}
