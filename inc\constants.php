<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! defined( 'ARTS_THEME_DISTANCE_ARRAY' ) ) {
	/**
	 * Fluid Distance Presets
	 */
	define(
		'ARTS_THEME_DISTANCE_ARRAY',
		array(
			'padding' => array(
				'top'        => array(
					''                     => 'Auto',
					'pt-gutter-horizontal' => '+ Horizontal Gutter',
					'pt-gutters'           => '+ Vertical Gutter',
					'pt-header-height'     => '+ Header Height',
					'pt-xsmall'            => '+ XSmall',
					'pt-small'             => '+ Small',
					'pt-medium'            => '+ Medium',
					'pt-large'             => '+ Large',
					'pt-xlarge'            => '+ XLarge',
				),
				'bottom'     => array(
					''                     => 'Auto',
					'pb-gutter-horizontal' => '+ Horizontal Gutter',
					'pb-gutters'           => '+ Vertical Gutter',
					'pb-header-height'     => '+ Header Height',
					'pb-xsmall'            => '+ XSmall',
					'pb-small'             => '+ Small',
					'pb-medium'            => '+ Medium',
					'pb-large'             => '+ Large',
					'pb-xlarge'            => '+ XLarge',
				),
				'vertical'   => array(
					''                      => 'Auto',
					'py-gutters-horizontal' => '+ Horizontal Gutters',
					'py-gutters'            => '+ Vertical Gutters',
					'py-xsmall'             => '+ XSmall',
					'py-small'              => '+ Small',
					'py-medium'             => '+ Medium',
					'py-large'              => '+ Large',
					'py-xlarge'             => '+ XLarge',
				),
				'horizontal' => array(
					''                    => 'Auto',
					'px-gutters'          => '+ Horizontal Gutters',
					'px-gutters-vertical' => '+ Vertical Gutters',
					'px-xsmall'           => '+ XSmall',
					'px-small'            => '+ Small',
					'px-medium'           => '+ Medium',
					'px-large'            => '+ Large',
					'px-xlarge'           => '+ XLarge',
				),
				'all'        => array(
					''          => 'Auto',
					'p-gutters' => '+ Gutters',
					'p-xsmall'  => '+ XSmall',
					'p-small'   => '+ Small',
					'p-medium'  => '+ Medium',
					'p-large'   => '+ Large',
					'p-xlarge'  => '+ XLarge',
				),
			),
			'margin'  => array(
				'top'          => array(
					''                 => 'Auto',
					'mt-header-height' => '+ Header Height',
					'mt-xsmall'        => '+ XSmall',
					'mt-small'         => '+ Small',
					'mt-medium'        => '+ Medium',
					'mt-large'         => '+ Large',
					'mt-xlarge'        => '+ XLarge',
				),
				'minus_top'    => array(
					'mt-minus-xsmall' => '- XSmall',
					'mt-minus-small'  => '- Small',
					'mt-minus-medium' => '- Medium',
					'mt-minus-large'  => '- Large',
					'mt-minus-xlarge' => '- XLarge',
				),
				'bottom'       => array(
					''                 => 'Auto',
					'mb-header-height' => '+ Header Height',
					'mb-xsmall'        => '+ XSmall',
					'mb-small'         => '+ Small',
					'mb-medium'        => '+ Medium',
					'mb-large'         => '+ Large',
					'mb-xlarge'        => '+ XLarge',
				),
				'minus_bottom' => array(
					'mb-minus-xsmall' => '- XSmall',
					'mb-minus-small'  => '- Small',
					'mb-minus-medium' => '- Medium',
					'mb-minus-large'  => '- Large',
					'mb-minus-xlarge' => '- XLarge',
				),
				'vertical'     => array(
					''          => 'Auto',
					'my-xsmall' => '+ XSmall',
					'my-small'  => '+ Small',
					'my-medium' => '+ Medium',
					'my-large'  => '+ Large',
					'my-xlarge' => '+ XLarge',
				),
				'horizontal'   => array(
					''          => 'Auto',
					'mx-xsmall' => '+ XSmall',
					'mx-small'  => '+ Small',
					'mx-medium' => '+ Medium',
					'mx-large'  => '+ Large',
					'mx-xlarge' => '+ XLarge',
				),
				'all'          => array(
					''         => 'Auto',
					'm-xsmall' => '+ XSmall',
					'm-small'  => '+ Small',
					'm-medium' => '+ Medium',
					'm-large'  => '+ Large',
					'm-xlarge' => '+ XLarge',
				),
			),
		)
	);
}

if ( ! defined( 'ARTS_THEME_HTML_TAGS_ARRAY' ) ) {
	/**
	 * HTML Tags
	 */
	define(
		'ARTS_THEME_HTML_TAGS_ARRAY',
		array(
			'div'        => '&lt;div&gt;',
			'span'       => '&lt;span&gt;',
			'h1'         => '&lt;h1&gt;',
			'h2'         => '&lt;h2&gt;',
			'h3'         => '&lt;h3&gt;',
			'h4'         => '&lt;h4&gt;',
			'h5'         => '&lt;h5&gt;',
			'h6'         => '&lt;h6&gt;',
			'p'          => '&lt;p&gt;',
			'blockquote' => '&lt;blockquote&gt;',
		)
	);
}

if ( ! defined( 'ARTS_THEME_CONTAINERS_ARRAY' ) ) {
	/**
	 * Containers
	 */
	define(
		'ARTS_THEME_CONTAINERS_ARRAY',
		array(
			'container'               => 'Boxed',
			'container-fluid'         => 'Fullwidth / Bootstrap gutters',
			'container-fluid-gutters' => 'Fullwidth / Theme gutters',
			'w-100'                   => 'Fullwidth / No gutters',
		)
	);
}

if ( ! defined( 'ARTS_THEME_SHAPES' ) ) {
	/**
	 * Shapes
	 */
	define(
		'ARTS_THEME_SHAPES',
		array(
			''                        => 'Auto',
			'shape-rounded-rectangle' => 'Rounded Rectangle',
			'shape-oval'              => 'Oval',
			'shape-top-arc'           => 'Top Arc',
			'shape-bottom-arc'        => 'Bottom Arc',
		)
	);
}
