{"name": "arts/wizard-setup", "description": "Theme onboarding based on MerlinWP by <PERSON>", "type": "library", "minimum-stability": "dev", "repositories": [{"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsMerlin", "options": {"symlink": true}}, {"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsUtilities", "options": {"symlink": true}}, {"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsContentImporter", "options": {"symlink": true}}], "autoload": {"psr-4": {"Arts\\WizardSetup\\": "src/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"arts/merlin-wp": "@dev", "arts/utilities": "@dev"}}