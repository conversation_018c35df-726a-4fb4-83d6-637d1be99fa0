const _0x184c23=_0x9d09;function _0x9d09(_0x236f73,_0x3beca4){const _0xcc7ea6=_0xcc7e();return _0x9d09=function(_0x9d09b2,_0x5743cf){_0x9d09b2=_0x9d09b2-0x1c9;let _0x2bf95=_0xcc7ea6[_0x9d09b2];return _0x2bf95;},_0x9d09(_0x236f73,_0x3beca4);}function _0xcc7e(){const _0x4e8bcc=['init','add','all','finally','postTask','10LylQXC','903342HBVWuK','6rZoqhJ','1461690zXleLL','_createFixedScene','create','offsetHeight','20UxBMbu','element','53769KOOxEJ','top\x20center-=','string','680fcLuoI','dataReady','3217328bhreKh','927269iYciYM','14992yATNBz','(min-width:\x20576px)','function','destroy','matchMedia','fixedWrapper','.js-fixed-header__fixed-wrapper','bottom\x20bottom','kill','innerSelectors','847392wdCany','options','querySelector'];_0xcc7e=function(){return _0x4e8bcc;};return _0xcc7e();}(function(_0x509333,_0x2f07b7){const _0x488408=_0x9d09,_0x235eb8=_0x509333();while(!![]){try{const _0x63d103=parseInt(_0x488408(0x1cc))/0x1*(-parseInt(_0x488408(0x1e5))/0x2)+parseInt(_0x488408(0x1dd))/0x3+parseInt(_0x488408(0x1d3))/0x4*(-parseInt(_0x488408(0x1cf))/0x5)+-parseInt(_0x488408(0x1e7))/0x6*(-parseInt(_0x488408(0x1d2))/0x7)+parseInt(_0x488408(0x1d1))/0x8+-parseInt(_0x488408(0x1e8))/0x9*(-parseInt(_0x488408(0x1ca))/0xa)+-parseInt(_0x488408(0x1e6))/0xb;if(_0x63d103===_0x2f07b7)break;else _0x235eb8['push'](_0x235eb8['shift']());}catch(_0x209806){_0x235eb8['push'](_0x235eb8['shift']());}}}(_0xcc7e,0x44a86));export default class FixedHeader extends BaseComponent{constructor({name:_0x29e734,loadInnerComponents:_0xe82509,loadAfterSyncStyles:_0x4a474a,parent:_0x488db4,element:_0x1c679a}){const _0x2e8ea8=_0x9d09;super({'name':_0x29e734,'loadInnerComponents':_0xe82509,'loadAfterSyncStyles':_0x4a474a,'parent':_0x488db4,'element':_0x1c679a,'defaults':{'matchMedia':_0x2e8ea8(0x1d4)},'innerElements':{'fixedWrapper':_0x2e8ea8(0x1d9)}}),this[_0x2e8ea8(0x1d0)][_0x2e8ea8(0x1e3)](()=>{this['setup']();});}[_0x184c23(0x1e0)](){return new Promise(_0x40e0c5=>{const _0x4f73cb=_0x9d09,_0x1c6055=typeof this[_0x4f73cb(0x1de)]['matchMedia']===_0x4f73cb(0x1ce)?this['options'][_0x4f73cb(0x1d7)]:_0x4f73cb(0x1e2);this['mm']=gsap[_0x4f73cb(0x1d7)](),this['mm'][_0x4f73cb(0x1e1)](_0x1c6055,()=>{this['_createFixedScene']();}),_0x40e0c5(!![]);});}[_0x184c23(0x1d6)](){return new Promise(_0x38c6a2=>{const _0x451318=_0x9d09,_0x431c09=[];if(this['mm']&&typeof this['mm'][_0x451318(0x1db)]===_0x451318(0x1d5)){const _0x489aec=scheduler[_0x451318(0x1e4)](()=>{const _0x5a1fb5=_0x451318;this['mm'][_0x5a1fb5(0x1db)]();});_0x431c09['push'](_0x489aec);}Promise[_0x451318(0x1e2)](_0x431c09)[_0x451318(0x1e3)](()=>_0x38c6a2(!![]));});}[_0x184c23(0x1e9)](){const _0x3fe482=_0x184c23,_0x379b71=this[_0x3fe482(0x1cb)][_0x3fe482(0x1df)](this[_0x3fe482(0x1dc)][_0x3fe482(0x1d8)]);this['fixedScene']=ScrollTrigger[_0x3fe482(0x1ea)]({'start':()=>_0x3fe482(0x1cd)+_0x379b71[_0x3fe482(0x1c9)],'end':()=>_0x3fe482(0x1da),'pin':_0x379b71,'pinSpacing':![],'trigger':this[_0x3fe482(0x1cb)],'invalidateOnRefresh':!![],'scrub':!![]});}}