<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'wp_enqueue_scripts', 'arts_inline_theme_components', 60 );
if ( ! function_exists( 'arts_inline_theme_components' ) ) {
	/**
	 * Localize the theme components
	 *
	 * @return void
	 */
	function arts_inline_theme_components() {
		$default_components = array(
			'Preloader'                      => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Preloader.js' ),
			),
			'Header'                         => array(
				'dependencies' => array( 'arts-header' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Header.js' ),
			),
			'MenuOverlay'                    => array(
				'dependencies' => array( 'arts-infinite-list' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/MenuOverlay.js' ),
			),
			'MenuClassic'                    => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/MenuClassic.js' ),
			),
			'SliderFullpageBackgroundsMask'  => array(
				'dependencies' => array( 'arts-fullpage-slider' ),
				'files'        => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/components/SliderFullpageBase.js' ),
						'id'   => 'slider-fullpage-base-js',
					),
				),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/SliderFullpageBackgroundsMask.js' ),
			),
			'SliderFullpageBackgroundsSlide' => array(
				'dependencies' => array( 'arts-fullpage-slider' ),
				'files'        => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/components/SliderFullpageBase.js' ),
						'id'   => 'slider-fullpage-base-js',
					),
				),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/SliderFullpageBackgroundsSlide.js' ),
			),
			'SliderFullpageWebGL'            => array(
				'dependencies' => array( 'curtains' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/SliderFullpageWebGL.js' ),
			),
			'SliderTestimonials'             => array(
				'dependencies' => array( 'arts-fullpage-slider' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/SliderTestimonials.js' ),
			),
			'InfiniteList'                   => array(
				'dependencies' => array( 'arts-infinite-list' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/InfiniteList.js' ),
			),
			'InfiniteListWebGL'              => array(
				'dependencies' => array( 'curtains' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/InfiniteListWebGL.js' ),
			),
			'CurtainsBase'                   => array(
				'dependencies' => array( 'curtains' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/CurtainsBase.js' ),
			),
			'SplitCounter'                   => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/SplitCounter.js' ),
			),
			'MarqueeHeader'                  => array(
				'dependencies' => array( 'arts-infinite-list' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/MarqueeHeader.js' ),
			),
			'MarqueeHeadingsHover'           => array(
				'dependencies' => array( 'arts-infinite-list' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/MarqueeHeadingsHover.js' ),
			),
			'MarqueeHeadingsHoverWebGL'      => array(
				'dependencies' => array( 'curtains' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/MarqueeHeadingsHoverWebGL.js' ),
			),
			'ScreensWall'                    => array(
				'dependencies' => array( 'arts-infinite-list' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/ScreensWall.js' ),
			),
			'RotatingButton'                 => array(
				'dependencies' => array( 'circle-type' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/RotatingButton.js' ),
			),
			'ArcImages'                      => array(
				'dependencies' => array( 'arts-infinite-list' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/ArcImages.js' ),
			),
			'Scroll'                         => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Scroll.js' ),
			),
			'AJAX'                           => array(
				'dependencies' => array( 'barba' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/AJAX.js' ),
			),
			'Masthead'                       => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Masthead.js' ),
			),
			'Content'                        => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Content.js' ),
			),
			'Parallax'                       => array(
				'dependencies' => array( 'arts-parallax' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Parallax.js' ),
			),
			'HorizontalScroll'               => array(
				'dependencies' => array( 'arts-horizontal-scroll' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/HorizontalScroll.js' ),
			),
			'CursorFollower'                 => array(
				'dependencies' => array( 'arts-cursor-follower' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/CursorFollower.js' ),
			),
			'PSWP'                           => array(
				'dependencies' => array( 'photoswipe' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/PSWP.js' ),
			),
			'GoogleMap'                      => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/GoogleMap.js' ),
			),
			'FormAJAXPassword'               => array(
				'dependencies' => array( 'pristine' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/FormAJAXPassword.js' ),
			),
			'Grid'                           => array(
				'dependencies' => array( 'isotope' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Grid.js' ),
			),
			'AutoScrollNext'                 => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/AutoScrollNext.js' ),
			),
			'AutoScrollNextWebGL'            => array(
				'dependencies' => array( 'curtains' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/AutoScrollNextWebGL.js' ),
			),
			'FixedHeader'                    => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/FixedHeader.js' ),
			),
			'FixedWall'                      => array(
				'dependencies' => array( 'arts-infinite-list' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/FixedWall.js' ),
			),
			'CounterUp'                      => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/CounterUp.js' ),
			),
			'SliderImages'                   => array(
				'dependencies' => array( 'arts-infinite-list' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/SliderImages.js' ),
			),
			'ClickAndHold'                   => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/ClickAndHold.js' ),
			),
			'Mask'                           => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Mask.js' ),
			),
			'Comments'                       => array(
				'dependencies' => array( 'pristine' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/Comments.js' ),
			),
			'ResponsiveRectangle'            => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/ResponsiveRectangle.js' ),
			),
			'ProductQuantity'                => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/ProductQuantity.js' ),
			),
			'FormAJAXCart'                   => array(
				'dependencies' => array( 'pristine' ),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/FormAJAXCart.js' ),
			),
			'MiniCart'                       => array(
				'dependencies' => array(),
				'file'         => esc_url( ARTS_THEME_URL . '/js/components/MiniCart.js' ),
			),
		);
		$components         = apply_filters( 'arts/frontend/components', $default_components );

		wp_localize_script(
			'asli-app',
			'asli_theme_components',
			$components
		);
	}
}
