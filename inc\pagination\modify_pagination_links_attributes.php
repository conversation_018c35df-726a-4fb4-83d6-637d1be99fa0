<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_pagination_links_attributes' ) ) {
	/**
	 * Get custom attributes for pagination links.
	 *
	 * @param string $type The type of pagination link ('prev', 'next', 'num').
	 * @return string The custom attributes for the pagination link.
	 */
	function arts_get_pagination_links_attributes( $type = 'prev' ) {
		$attributes              = array(
			'class' => array(),
		);
		$cursor_follower_enabled = Utilities::get_kit_settings( 'cursor_follower_enabled', false );

		if ( $type === 'num' ) {
			if ( $cursor_follower_enabled ) {
				$attributes['data-arts-cursor-follower-target'] = wp_json_encode(
					array(
						'highlight'  => false,
						'magnetic'   => 0.33,
						'scale'      => 'current',
						'hideNative' => false,
					)
				);
			}
		} else {
			$attributes['class'][] = 'material-icons';

			if ( $cursor_follower_enabled ) {
				$attributes['data-arts-cursor-follower-target'] = wp_json_encode(
					array(
						'highlight'  => false,
						'magnetic'   => 0.33,
						'scale'      => 1.2,
						'hideNative' => false,
					)
				);
			}
		}

		return Utilities::get_pagination_link_attributes( $attributes, $type );
	}
}

add_filter( 'next_posts_link_attributes', 'arts_add_next_posts_link_attributes' );
if ( ! function_exists( 'arts_add_next_posts_link_attributes' ) ) {
	/**
	 * Custom attributes for pagination "Next" link.
	 *
	 * @return string The custom attributes for the "Next" pagination link.
	 */
	function arts_add_next_posts_link_attributes() {
		return arts_get_pagination_links_attributes( 'next' );
	}
}

add_filter( 'previous_posts_link_attributes', 'arts_add_previous_posts_link_attributes' );
if ( ! function_exists( 'arts_add_previous_posts_link_attributes' ) ) {
	/**
	 * Custom attributes for pagination "Prev" link.
	 *
	 * @return string The custom attributes for the "Prev" pagination link.
	 */
	function arts_add_previous_posts_link_attributes() {
		return arts_get_pagination_links_attributes( 'prev' );
	}
}

add_filter( 'paginate_links_output', 'arts_add_pagination_links_attributes' );
if ( ! function_exists( 'arts_add_pagination_links_attributes' ) ) {
	/**
	 * Custom attributes for pagination number links.
	 *
	 * @param string $r The pagination links output.
	 * @return string The modified pagination links output with custom attributes.
	 */
	function arts_add_pagination_links_attributes( $r ) {
		return str_replace( 'page-numbers"', 'page-numbers" ' . arts_get_pagination_links_attributes( 'num' ), $r );
	}
}
