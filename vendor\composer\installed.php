<?php return array(
    'root' => array(
        'name' => 'arts/asli-theme',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '610ab64055ce4b69b948fe24e46f5f493aa68d40',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'arts/asli-theme' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '610ab64055ce4b69b948fe24e46f5f493aa68d40',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arts/license-manager' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '194e1ce0bbdcabfeaa789e2a44bda20b3ac16587',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/license-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arts/merlin-wp' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '76520dec621425f9389e14d3391c14957e54859d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/merlin-wp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arts/notice-manager' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '835a17419ec6a511bbb7d22b27c06172c3d50c77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/notice-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arts/tgm-plugin-activation' => array(
            'pretty_version' => '2.6.1',
            'version' => '2.6.1.0',
            'reference' => '59034845106cb7e48aefc9f8903b08520e75bb56',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/tgm-plugin-activation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arts/utilities' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'f6d72338fb43caf82f90dbdeabd1b423c66d5c99',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/utilities',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'arts/wizard-setup' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '161b9b8de152b71781a3ba311e28b02dc0cb9833',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/wizard-setup',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arts/wp-content-importer-v2' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '5e6d8611ace26951c3de2c124dda92e848716534',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/wp-content-importer-v2',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.x-dev',
            'version' => '2.9999999.9999999.9999999-dev',
            'reference' => 'cd82b5069148dd811ef54b4b92ce1b3aad84209b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
            ),
        ),
    ),
);
