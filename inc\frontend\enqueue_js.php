<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_action( 'wp_enqueue_scripts', 'arts_enqueue_js', 50 );
if ( ! function_exists( 'arts_enqueue_js' ) ) {
	/**
	 * Enqueue the theme scripts
	 *
	 * @return void
	 */
	function arts_enqueue_js() {
		$ajax_enabled              = Utilities::get_kit_settings( 'ajax_enabled', false );
		$outdated_browsers_enabled = Utilities::get_kit_settings( 'outdated_browsers_enabled', false );

		/**
		 * Force load Elementor assets
		 * on non-Elementor pages with AJAX turned on
		 */
		if ( class_exists( '\Elementor\Frontend' ) && ! Utilities::is_built_with_elementor() && $ajax_enabled ) {
			/**
			 * @var \Elementor\Core\Base\Module::instance
			 */
			$instance = \Elementor\Frontend::instance();
			$instance->enqueue_scripts();
		}

		if ( is_singular() && comments_open() ) {
			wp_enqueue_script( 'comment-reply' );
		}

		// GSAP libraries
		$gsap_libraries_version = '3.12.5';
		$vendor_scripts_path    = ARTS_THEME_URL . '/js/vendor/';

		wp_enqueue_script(
			'gsap',
			esc_url( $vendor_scripts_path . 'gsap.min.js' ),
			array(),
			$gsap_libraries_version,
			true
		);
		wp_enqueue_script(
			'drawsvg-plugin',
			esc_url( $vendor_scripts_path . 'DrawSVGPlugin.min.js' ),
			array( 'gsap' ),
			$gsap_libraries_version,
			true
		);
		wp_enqueue_script(
			'morphsvg-plugin',
			esc_url( $vendor_scripts_path . 'MorphSVGPlugin.min.js' ),
			array( 'gsap' ),
			$gsap_libraries_version,
			true
		);
		wp_enqueue_script(
			'scroll-to-plugin',
			esc_url( $vendor_scripts_path . 'ScrollToPlugin.min.js' ),
			array( 'gsap' ),
			$gsap_libraries_version,
			true
		);
		wp_enqueue_script(
			'split-text',
			esc_url( $vendor_scripts_path . 'SplitText.min.js' ),
			array( 'gsap' ),
			$gsap_libraries_version,
			true
		);
		wp_enqueue_script(
			'scrolltrigger',
			esc_url( $vendor_scripts_path . 'ScrollTrigger.min.js' ),
			array( 'gsap' ),
			$gsap_libraries_version,
			true
		);

		wp_enqueue_script(
			'lazy-load',
			esc_url( $vendor_scripts_path . 'lazyload.min.js' ),
			array(),
			'17.8.5',
			true
		);

		if ( $outdated_browsers_enabled ) {
			wp_enqueue_script(
				'outdated-browser-rework',
				esc_url( $vendor_scripts_path . 'outdated-browser-rework.min.js' ),
				array(),
				'1.1.0',
				false,
				array( 'strategy' => 'async' )
			);
		}

		wp_enqueue_script(
			'asli-framework',
			esc_url( ARTS_THEME_URL . '/js/framework.js' ),
			array(
				'gsap',
				'drawsvg-plugin',
				'morphsvg-plugin',
				'scroll-to-plugin',
				'split-text',
				'scrolltrigger',
				'lazy-load',
			),
			ARTS_THEME_VERSION,
			true
		);

		wp_enqueue_script(
			'asli-app',
			esc_url( ARTS_THEME_URL . '/js/app.js' ),
			array( 'asli-framework' ),
			ARTS_THEME_VERSION,
			true
		);

		if ( Utilities::is_elementor_editor_active() ) {
			wp_enqueue_script(
				'asli-elementor-preview-widgets',
				esc_url( $vendor_scripts_path . 'elementor-preview-widgets.js' ),
				array( 'asli-app' ),
				ARTS_THEME_VERSION,
				true
			);
		}

		$google_maps_api_key = get_option( 'elementor_google_maps_api_key' );
		if ( $google_maps_api_key ) {
			wp_register_script(
				'google-maps',
				"https://maps.googleapis.com/maps/api/js?loading=async&callback=app.setGoogleMapLoaded&key={$google_maps_api_key}",
				array(),
				null,
				array( 'strategy' => 'async' )
			);
		}

		if ( ! wp_script_is( 'wc-cart-fragments' ) && wp_script_is( 'wc-cart-fragments', 'registered' ) ) {
			// Enqueue the 'wc-cart-fragments' script
			wp_enqueue_script( 'wc-cart-fragments' );
		}
	}
}

add_action( 'wp_enqueue_scripts', 'arts_enqueue_jquery_ready', -999 );
if ( ! function_exists( 'arts_enqueue_jquery_ready' ) ) {
	/**
	 * Enqueue the jQuery Ready script which emulates
	 * jQuery's ready event after AJAX page transitions.
	 *
	 * @return void
	 */
	function arts_enqueue_jquery_ready() {
		$ajax_enabled                      = Utilities::get_kit_settings( 'ajax_enabled', false );
		$ajax_emulate_jquery_ready_enabled = Utilities::get_kit_settings( 'ajax_emulate_jquery_ready_enabled', false );

		if ( $ajax_enabled && $ajax_emulate_jquery_ready_enabled ) {
			wp_enqueue_script(
				'jquery-ready',
				esc_url( ARTS_THEME_URL . '/js/vendor/jquery.ready.js' ),
				array( 'jquery' ),
				ARTS_THEME_VERSION,
				false
			);
		}
	}
}
