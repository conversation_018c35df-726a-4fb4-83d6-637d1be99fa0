<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

$defaults = array();
$args     = wp_parse_args( $args, $defaults );

?>

<?php if ( ! empty( $args['beforeLoadingText'] ) || ! empty( $args['afterLoadingText'] ) ) : ?>
	<!-- Bottom content -->
	<div class="preloader__footer p-gutters js-preloader__content" data-arts-split-text-preset="animatedLines">
		<?php if ( ! empty( $args['beforeLoadingText'] ) ) : ?>
			<!-- Content [before load] -->
			<div class="preloader__loading js-preloader__content-loading">
				<div class="preloader__loading-text preloader__loading-text_before"><?php echo esc_html( $args['beforeLoadingText'] ); ?></div>
			</div>
			<!-- - Content [before load] -->
		<?php endif; ?>
		<?php if ( ! empty( $args['afterLoadingText'] ) ) : ?>
			<!-- Content [after load] -->
			<div class="js-preloader__content-loaded">
				<div class="preloader__loading-text preloader__loading-text_after"><?php echo esc_html( $args['afterLoadingText'] ); ?></div>
			</div>
			<!-- - Content [after load] -->
		<?php endif; ?>
	</div>
	<!-- - Bottom content -->
<?php endif; ?>
