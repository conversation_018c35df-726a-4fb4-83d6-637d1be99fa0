<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_is_preloader_enabled' ) ) {
	/**
	 * Check if the theme preloader is enabled and should be rendered on the page
	 * based on various conditions.
	 *
	 * @return bool True if the preloader is enabled, false otherwise.
	 */
	function arts_is_preloader_enabled() {
		$preloader_enabled                 = Utilities::get_kit_settings( 'preloader_enabled', false );
		$preloader_show_once_enabled       = Utilities::get_kit_settings( 'preloader_show_once_enabled', true );
		$preloader_logged_in_users_enabled = Utilities::get_kit_settings( 'preloader_logged_in_users_enabled', true );

		if ( isset( $_GET['preloader'] ) ) {
			$force_preloader_enabled = preg_replace( '/[^-a-zA-Z0-9_]/', '', $_GET['preloader'] );

			if ( $force_preloader_enabled === 'yes' ) {
				return true;
			} elseif ( $force_preloader_enabled === 'no' ) {
				return false;
			}
		}

		// Filter out 404 page and search page
		if ( is_404() || is_search() ) {
			return false;
		}

		// Filter out WooCommerce pages
		if ( function_exists( 'is_woocommerce' ) && is_woocommerce() ) {
			return false;
		}

		// Filter out WooCommerce related pages
		if ( Utilities::is_checkout() || Utilities::is_cart() || Utilities::is_account_page() || Utilities::is_order_received_page() ) {
			return false;
		}

		// Don't render preloader inside
		// Elementor editor
		if ( $preloader_enabled && ! Utilities::is_elementor_editor_active() ) {
			if ( ! $preloader_logged_in_users_enabled && is_user_logged_in() ) {
				return false;
			}

			if ( $preloader_show_once_enabled && ! is_customize_preview() ) {
				return ! Utilities::is_referer_from_same_domain();
			}

			return true;
		}
	}
}
