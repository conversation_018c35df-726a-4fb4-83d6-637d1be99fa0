<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_filter( 'autoptimize_filter_css_aggregate', 'arts_ao_disable_css_aggregation_ajax' );
if ( ! function_exists( 'arts_ao_disable_css_aggregation_ajax' ) ) {
	/**
	 * Force disable CSS aggregation for Autoptimize plugin
	 * if AJAX transitions are enabled to avoid incorrect
	 * page rendering.
	 *
	 * @param bool $value The current value of CSS aggregation setting.
	 * @return bool The modified value of CSS aggregation setting.
	 */
	function arts_ao_disable_css_aggregation_ajax( $value ) {
		$ajax_enabled = Utilities::get_kit_settings( 'ajax_enabled', false );

		if ( $ajax_enabled ) {
			$value = false;
		}

		return $value;
	}
}


