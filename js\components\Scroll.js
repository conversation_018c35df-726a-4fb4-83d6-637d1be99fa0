function _0x1e32(_0x453c09,_0x4bebaa){const _0x32f708=_0x32f7();return _0x1e32=function(_0x1e3205,_0x553e05){_0x1e3205=_0x1e3205-0x1a8;let _0x508730=_0x32f708[_0x1e3205];return _0x508730;},_0x1e32(_0x453c09,_0x4bebaa);}function _0x32f7(){const _0x4f3e6f=['remove','finally','smoothScroll','offsetWidth','has-smooth-scroll','_attachAJAXListeners','instance','string','adminBar','isEnabledOption','_raf','5245065iWnQJu','_onAnimationFrame','getAttribute','_onAnimationFrameGSAP','getById','_initSmoothScroll','destroyed','_runSmoothScroll','includes','href','documentElement','container','body','bind','scroll','7163056ijledT','getElementById','addEventListener','_attachEvents','_detachEvents','#!scroll-up','removeEventListener','_handlers','destroy','options','3176805xzTAYw','14353630ZpxhbT','init','undefined','_isPowerSaving','_getAnchorTarget','no-smooth-scroll','customize','duration','update','6482052oMaJbt','offsetTop','.wc-tabs','targetScroll','6WaQEbY','start','querySelector','offsetHeight','velocityWatcher','closest','isSmooth','all','element','add','transitionEnd','isHTMLElement','138XWElxv','raf','_onClick','animationFrame','push','click','html','.has-horizontal-scroll','utilities','reset','postTask','22088wDslye','target','useGSAPRaf','animationFrameGSAP','lenis','elementor-action','_getPageElementAnchor','preventDefault','_setSmoothScroll','lagSmoothing','scrollTo','_createVelocityWatcher','isScrolling','innerWidth','_getHorizontalScrollDownContainer','.js-horizontal-scroll__section','.horizontal-scroll','innerHeight','4735970ECYbCt','maxScroll','dataReady','isStopped','create','lastIndexOf','classList','easing','ticker','9myEmTv'];_0x32f7=function(){return _0x4f3e6f;};return _0x32f7();}const _0x42ea64=_0x1e32;(function(_0x7fa852,_0x4ff4d4){const _0x35afde=_0x1e32,_0x3cb4e1=_0x7fa852();while(!![]){try{const _0x3874c9=-parseInt(_0x35afde(0x1e9))/0x1*(parseInt(_0x35afde(0x1f4))/0x2)+parseInt(_0x35afde(0x1cf))/0x3+parseInt(_0x35afde(0x1d9))/0x4+-parseInt(_0x35afde(0x206))/0x5*(-parseInt(_0x35afde(0x1dd))/0x6)+-parseInt(_0x35afde(0x1b6))/0x7+parseInt(_0x35afde(0x1c5))/0x8+-parseInt(_0x35afde(0x1aa))/0x9*(parseInt(_0x35afde(0x1d0))/0xa);if(_0x3874c9===_0x4ff4d4)break;else _0x3cb4e1['push'](_0x3cb4e1['shift']());}catch(_0x3999fe){_0x3cb4e1['push'](_0x3cb4e1['shift']());}}}(_0x32f7,0xc68ee));export default class Scroll extends BaseComponent{constructor({name:_0x1dde76,loadInnerComponents:_0x3f5604,loadAfterSyncStyles:_0xe9e75f,parent:_0x52a42c,element:_0x2b1d72,options:_0x1b81b2}){const _0x29a89a=_0x1e32;super({'name':_0x1dde76,'loadInnerComponents':_0x3f5604,'loadAfterSyncStyles':_0xe9e75f,'parent':_0x52a42c,'element':_0x2b1d72,'defaults':_0x1b81b2}),this[_0x29a89a(0x1cc)]={'click':this[_0x29a89a(0x1eb)][_0x29a89a(0x1c3)](this),'animationFrame':this[_0x29a89a(0x1b7)][_0x29a89a(0x1c3)](this),'animationFrameGSAP':this[_0x29a89a(0x1b9)][_0x29a89a(0x1c3)](this),'transitionEnd':this[_0x29a89a(0x1f2)][_0x29a89a(0x1c3)](this)},this[_0x29a89a(0x208)][_0x29a89a(0x1ac)](()=>{const _0x58dd4c=_0x29a89a;this[_0x58dd4c(0x1b5)]=0x0,this[_0x58dd4c(0x1d3)]=undefined,this[_0x58dd4c(0x1bc)]=![],this[_0x58dd4c(0x1b1)]=null,this['setup']();});}[_0x42ea64(0x1d1)](){return new Promise(_0x33c2b6=>{const _0x2703cb=_0x1e32;this['_createVelocityWatcher'](),this[_0x2703cb(0x1bb)](),this[_0x2703cb(0x1c8)](),this[_0x2703cb(0x1b0)](),_0x33c2b6(!![]);});}['reset'](){return new Promise(_0x186451=>{const _0x4ef66e=_0x1e32;this['instance']&&(this[_0x4ef66e(0x1b1)][_0x4ef66e(0x1dc)]=0x0,this[_0x4ef66e(0x1b1)][_0x4ef66e(0x1de)]()),this[_0x4ef66e(0x1c9)](),this['element']=app['elements'][_0x4ef66e(0x1c1)],this[_0x4ef66e(0x1c8)](),_0x186451(!![]);});}[_0x42ea64(0x1cd)](){return new Promise(_0x23472c=>{const _0x1512d7=_0x1e32,_0x518b7f=[];this[_0x1512d7(0x1c9)]();if(this[_0x1512d7(0x1b1)]){const _0x2fcaeb=scheduler[_0x1512d7(0x1f3)](()=>{const _0x232b17=_0x1512d7;this[_0x232b17(0x1b1)][_0x232b17(0x1cd)](),this[_0x232b17(0x1b1)][_0x232b17(0x1e3)]=![],this[_0x232b17(0x1b1)][_0x232b17(0x200)]=![],this[_0x232b17(0x1b1)][_0x232b17(0x209)]=![],document[_0x232b17(0x1c0)]['classList'][_0x232b17(0x1ab)](_0x232b17(0x1f8));});_0x518b7f[_0x1512d7(0x1ed)](_0x2fcaeb);const _0x2c7fb5=scheduler[_0x1512d7(0x1f3)](()=>{const _0xc1fa5c=_0x1512d7;!!this['options']['useGSAPRaf']?gsap['ticker'][_0xc1fa5c(0x1ab)](this[_0xc1fa5c(0x1cc)][_0xc1fa5c(0x1f7)]):(cancelAnimationFrame(this[_0xc1fa5c(0x1b5)]),this[_0xc1fa5c(0x1b5)]=0x0),this[_0xc1fa5c(0x1b1)]=undefined;});_0x518b7f[_0x1512d7(0x1ed)](_0x2c7fb5);}if(typeof wp!==_0x1512d7(0x1d2)&&typeof wp[_0x1512d7(0x1d6)]!==_0x1512d7(0x1d2)){const _0x75510e=scheduler[_0x1512d7(0x1f3)](()=>{const _0x45de8d=_0x1512d7;this[_0x45de8d(0x1bc)]=!![];});_0x518b7f['push'](_0x75510e);}Promise[_0x1512d7(0x1e4)](_0x518b7f)['finally'](()=>_0x23472c(!![]));});}['_initSmoothScroll'](){const _0x4d2318=_0x42ea64,_0x147405=typeof Lenis!==_0x4d2318(0x1d2)&&app[_0x4d2318(0x1f1)][_0x4d2318(0x1b4)](app['options'][_0x4d2318(0x1ad)]);_0x147405&&!this['instance']&&this[_0x4d2318(0x1bd)](),this[_0x4d2318(0x1fc)](_0x147405);}['_runSmoothScroll'](){const _0x1f8430=_0x42ea64;this[_0x1f8430(0x1b1)]=new Lenis({'wrapper':document[_0x1f8430(0x1c2)],'duration':this[_0x1f8430(0x1ce)][_0x1f8430(0x1d7)],'easing':typeof this['options']['easing']===_0x1f8430(0x1b2)?gsap['parseEase'](this[_0x1f8430(0x1ce)][_0x1f8430(0x1a8)]):this[_0x1f8430(0x1ce)][_0x1f8430(0x1a8)]}),!!this[_0x1f8430(0x1ce)][_0x1f8430(0x1f6)]?(gsap[_0x1f8430(0x1a9)][_0x1f8430(0x1e6)](this[_0x1f8430(0x1cc)][_0x1f8430(0x1f7)]),gsap[_0x1f8430(0x1a9)][_0x1f8430(0x1fd)](0x0)):this[_0x1f8430(0x1b5)]=requestAnimationFrame(this[_0x1f8430(0x1cc)][_0x1f8430(0x1ec)]),this['instance']['on'](_0x1f8430(0x1c4),ScrollTrigger[_0x1f8430(0x1d8)]);}['_setSmoothScroll'](_0x5bd767=![]){const _0x95230=_0x42ea64;_0x5bd767?(document[_0x95230(0x1c0)][_0x95230(0x20c)]['add'](_0x95230(0x1af)),document[_0x95230(0x1c0)]['classList']['remove'](_0x95230(0x1d5))):(document['documentElement'][_0x95230(0x20c)][_0x95230(0x1ab)]('has-smooth-scroll'),document[_0x95230(0x1c0)][_0x95230(0x20c)][_0x95230(0x1e6)](_0x95230(0x1d5)));}['_onAnimationFrame'](_0x422e4a){const _0x22c7df=_0x42ea64;this['instance']&&this[_0x22c7df(0x1b1)][_0x22c7df(0x1ea)](_0x422e4a),requestAnimationFrame(this['_handlers']['animationFrame']);}[_0x42ea64(0x1b9)](_0x539739,_0x475645,_0x494692){const _0x1ccbf9=_0x42ea64;this[_0x1ccbf9(0x1b1)]&&this[_0x1ccbf9(0x1b1)][_0x1ccbf9(0x1ea)](_0x539739*0x3e8);}[_0x42ea64(0x1b0)](){const _0x1aaef7=_0x42ea64;document[_0x1aaef7(0x1c7)]('arts/barba/transition/end/before',this[_0x1aaef7(0x1cc)][_0x1aaef7(0x1e7)]);}[_0x42ea64(0x1c8)](){const _0x492894=_0x42ea64;this[_0x492894(0x1e5)][_0x492894(0x1c7)](_0x492894(0x1ee),this['_handlers'][_0x492894(0x1ee)]);}['_detachEvents'](){const _0x51a3da=_0x42ea64;this[_0x51a3da(0x1e5)][_0x51a3da(0x1cb)](_0x51a3da(0x1ee),this['_handlers'][_0x51a3da(0x1ee)]);}[_0x42ea64(0x1eb)](_0x1fd373){const _0x4fbdbc=_0x42ea64,_0x316042=_0x1fd373[_0x4fbdbc(0x1f5)];if(app[_0x4fbdbc(0x1f1)][_0x4fbdbc(0x1e8)](_0x316042)){const _0x45a70a=_0x316042[_0x4fbdbc(0x1e2)]('a'),_0x446326=this[_0x4fbdbc(0x1fa)](_0x45a70a);if(_0x446326)_0x1fd373['preventDefault'](),app[_0x4fbdbc(0x1f1)][_0x4fbdbc(0x1fe)]({'target':_0x446326});else{if(_0x45a70a&&(_0x45a70a[_0x4fbdbc(0x1b8)](_0x4fbdbc(0x1bf))==='#!scroll-down'||_0x45a70a['getAttribute'](_0x4fbdbc(0x1bf))==='#asli-scroll-down')){_0x1fd373[_0x4fbdbc(0x1fb)]();let _0xf72539;const _0x3e2099=this['_getHorizontalScrollDownContainer'](_0x45a70a);if(_0x3e2099){const _0x17d6eb=_0x3e2099[_0x4fbdbc(0x1df)](_0x4fbdbc(0x203));_0x17d6eb?_0x17d6eb['closest'](_0x4fbdbc(0x1f0))?(_0xf72539=_0x3e2099[_0x4fbdbc(0x1da)]+_0x17d6eb[_0x4fbdbc(0x1ae)],_0xf72539+=app[_0x4fbdbc(0x1f1)][_0x4fbdbc(0x1b3)][_0x4fbdbc(0x1e0)]*window['devicePixelRatio']):_0xf72539=_0x3e2099[_0x4fbdbc(0x1da)]+_0x17d6eb['offsetHeight']:_0xf72539=_0x3e2099[_0x4fbdbc(0x1da)]+window[_0x4fbdbc(0x201)];}else _0xf72539=window[_0x4fbdbc(0x205)];app['utilities']['scrollTo']({'target':_0xf72539,'lockReveal':![]});}else _0x45a70a&&(_0x45a70a[_0x4fbdbc(0x1b8)](_0x4fbdbc(0x1bf))===_0x4fbdbc(0x1ca)||_0x45a70a[_0x4fbdbc(0x1b8)](_0x4fbdbc(0x1bf))==='#asli-scroll-up')&&(_0x1fd373['preventDefault'](),app[_0x4fbdbc(0x1f1)]['scrollTo']({'target':0x0,'lockReveal':![]}));}}}[_0x42ea64(0x202)](_0x2301e5){const _0x2bfb31=_0x42ea64,_0xb8a712=_0x2301e5[_0x2bfb31(0x1e2)]('.e-parent[data-element_type=\x22container\x22]'),_0x512bde=_0x2301e5[_0x2bfb31(0x1e2)](_0x2bfb31(0x204));if(_0x512bde)return _0xb8a712?_0xb8a712:_0x512bde;return null;}[_0x42ea64(0x1fa)](_0x4472a5){const _0x2a53f8=_0x42ea64;if(!_0x4472a5)return null;if(_0x4472a5[_0x2a53f8(0x1e2)](_0x2a53f8(0x1db)))return null;const _0x5e177a=_0x4472a5['getAttribute'](_0x2a53f8(0x1bf));if(this['_isValidAnchor'](_0x5e177a))return this[_0x2a53f8(0x1d4)](_0x5e177a);return null;}['_isValidAnchor'](_0x3597d3){const _0x454ba6=_0x42ea64;return _0x3597d3&&_0x3597d3!=='#'&&!_0x3597d3[_0x454ba6(0x1be)](_0x454ba6(0x1f9));}[_0x42ea64(0x1d4)](_0x30aa2b){const _0x256e27=_0x42ea64,_0x59cc66=_0x30aa2b[_0x256e27(0x20b)]('#');let _0x6e3dab;return _0x59cc66!==-0x1&&(_0x6e3dab=_0x30aa2b['slice'](_0x59cc66+0x1)),_0x6e3dab?document[_0x256e27(0x1c6)](_0x6e3dab):null;}[_0x42ea64(0x1ff)](){const _0x24e164=_0x42ea64,_0x51dc29='velocityWatcher';!ScrollTrigger[_0x24e164(0x1ba)](_0x51dc29)&&ScrollTrigger[_0x24e164(0x20a)]({'id':_0x24e164(0x1e1),'start':0x0,'end':()=>ScrollTrigger[_0x24e164(0x207)](_0x24e164(0x1ef))});}}