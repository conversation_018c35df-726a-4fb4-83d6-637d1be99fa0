<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'tgmpa_register', 'arts_register_required_plugins' );
if ( ! function_exists( 'arts_register_required_plugins' ) ) {
	/**
	 * Registers the required plugins for the theme.
	 *
	 * Defines an array of plugins to be installed and activated,
	 * along with their configuration settings. It uses the TGMPA library to
	 * handle the plugin installation and activation process.
	 *
	 * @return void
	 */
	function arts_register_required_plugins() {
		$theme_slug             = ARTS_THEME_SLUG;

		/*
		 * Array of plugin arrays. Required keys are name and slug.
		 * If the source is NOT from the .org repo, then source is also required.
		 */
		$plugins = array(
			array(
				'name'     => esc_html__( 'Advanced Custom Fields PRO', 'asli' ),
				'slug'     => 'advanced-custom-fields-pro',
				'source'   => esc_url( 'https://artemsemkin.com/wp-json/edd/v1/file/acf-pro/plugin' ),
				'required' => true,
			),

			array(
				'name'     => esc_html__( 'Asli Core', 'asli' ),
				'slug'     => 'asli-core',
				'source'   => esc_url( "https://artemsemkin.com/wp-json/edd/v1/file/{$theme_slug}/core-plugin" ),
				'required' => true,
				'version'  => '1.8.4',
			),

			array(
				'name'     => esc_html__( 'Contact Form 7', 'asli' ),
				'slug'     => 'contact-form-7',
				'required' => false,
			),

			array(
				'name'     => esc_html__( 'Elementor', 'asli' ),
				'slug'     => 'elementor',
				'required' => true,
			),

			array(
				'name'     => esc_html__( 'Intuitive Custom Post Order', 'asli' ),
				'slug'     => 'intuitive-custom-post-order',
				'required' => false,
			),

			array(
				'name'     => esc_html__( 'Safe SVG', 'asli' ),
				'slug'     => 'safe-svg',
				'required' => false,
			),

			array(
				'name'     => esc_html__( 'WooCommerce', 'asli' ),
				'slug'     => 'woocommerce',
				'required' => false,
			),
		);

		if ( file_exists( WP_PLUGIN_DIR . '/elementor-pro/elementor-pro.php' ) && ! defined( 'IS_PRO_ELEMENTS' ) ) {
			$plugins[] = array(
				'name'     => esc_html__( 'Elementor Pro', 'asli' ),
				'slug'     => 'elementor-pro',
				'source'   => ' ', // Prevent "Something went wrong with the plugin API" error in admin panel
				'required' => true,
			);
		} elseif ( ! defined( 'IS_PRO_ELEMENTS' ) && ! defined( 'ELEMENTOR_PRO_VERSION' ) ) {
			$plugins[] = array(
				'name'     => esc_html__( 'PRO Elements', 'asli' ),
				'slug'     => 'pro-elements',
				'source'   => esc_url( 'https://github.com/proelements/proelements/archive/refs/heads/master.zip' ),
				'required' => true,
			);
		}

		/*
		 * Array of configuration settings. Amend each line as needed.
		 */
		$config = array(
			'id'           => $theme_slug,                 // Unique ID for hashing notices for multiple instances of TGMPA.
			'default_path' => '',                      // Default absolute path to bundled plugins.
			'menu'         => 'tgmpa-install-plugins', // Menu slug.
			'has_notices'  => true,                    // Show admin notices or not.
			'dismissable'  => true,                    // If false, a user cannot dismiss the nag message.
			'dismiss_msg'  => '',                      // If 'dismissable' is false, this message will be output at top of nag.
			'is_automatic' => false,                   // Automatically activate plugins after installation or not.
			'message'      => '',                      // Message to output right before the plugins table.
		);

		tgmpa( $plugins, $config );
	}
}
