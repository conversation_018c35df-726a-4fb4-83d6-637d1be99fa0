<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_kit_settings' ) ) {
	/**
	 * Retrieve kit settings from Elementor.
	 *
	 * @param string|null $option_name    The name of the option to retrieve.
	 * @param mixed       $fallback_value The value to return if the option is not found.
	 * @param bool        $return_size    Whether to return the 'size' or 'url' if the value is an array.
	 *
	 * @return mixed The kit setting value or the fallback value.
	 * @deprecated 2.0.0 Use \Arts\Utilities\Utilities::get_kit_settings() method instead.
	 */
	function arts_get_kit_settings( $option_name = null, $fallback_value = null, $return_size = true ) {
		return Utilities::get_kit_settings( $option_name, $fallback_value, $return_size );
	}
}
