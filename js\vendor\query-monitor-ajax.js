document.addEventListener('arts/barba/transition/init/after', () => {
	try {
		var toolbarHeight = jQuery('#wpadminbar').length ? jQuery('#wpadminbar').outerHeight() : 0;
		var minheight = 100;
		var maxheight = (jQuery(window).height() - toolbarHeight);
		var minwidth = 300;
		var maxwidth = jQuery(window).width();
		var container = jQuery('#query-monitor-main');
		var body = jQuery('body');
		var body_margin = body.css('margin-bottom');
		var container_height_key = 'qm-container-height';
		var container_pinned_key = 'qm-' + (jQuery('body').hasClass('wp-admin') ? 'admin' : 'front') + '-container-pinned';
		var container_position_key = 'qm-container-position';
		var container_width_key = 'qm-container-width';

		if (container.hasClass('qm-peek')) {
			minheight = 27;
		}

		container.removeClass('qm-no-js').addClass('qm-js');

		var theme = localStorage.getItem('qm-theme');
		if (theme) {
			container.attr('data-theme', theme);
			jQuery('.qm-theme-toggle[value="' + theme + '"]').prop('checked', true);
		}

		if (jQuery('#qm-fatal').length) {
			console.error(qm_l10n.fatal_error + ': ' + jQuery('#qm-fatal').attr('data-qm-message'));

			if (jQuery('#wp-admin-bar-query-monitor').length) {
				jQuery('#wp-admin-bar-query-monitor')
					.addClass('qm-error')
					.find('a').eq(0)
					.text(qm_l10n.fatal_error);

				var fatal_container = document.createDocumentFragment();

				var fatal_message_menu = jQuery('#wp-admin-bar-query-monitor-placeholder')
					.clone()
					.attr('id', 'wp-admin-bar-qm-fatal-message');

				fatal_message_menu
					.find('a').eq(0)
					.text(jQuery('#qm-fatal').attr('data-qm-message'))
					.attr('href', '#qm-fatal');

				fatal_container.appendChild(fatal_message_menu.get(0));

				var fatal_file_menu = jQuery('#wp-admin-bar-query-monitor-placeholder')
					.clone()
					.attr('id', 'wp-admin-bar-qm-fatal-file');

				fatal_file_menu
					.find('a').eq(0)
					.text(jQuery('#qm-fatal').attr('data-qm-file') + ':' + jQuery('#qm-fatal').attr('data-qm-line'))
					.attr('href', '#qm-fatal');

				fatal_container.appendChild(fatal_file_menu.get(0));

				jQuery('#wp-admin-bar-query-monitor ul').append(fatal_container);
			}
		}

		var link_click = function (e) {
			var href = jQuery(this).attr('href') || jQuery(this).data('qm-href');

			if ('#qm-fatal' === href) {
				return;
			}

			show_panel(href);
			jQuery(href).focus();
			jQuery('#wp-admin-bar-query-monitor').removeClass('hover');
			e.preventDefault();
		};

		var stripes = function (table) {
			table.each(function () {
				jQuery(this).find('tbody tr').removeClass('qm-odd').not('[class*="qm-hide-"]').filter(':even').addClass('qm-odd');
			});
		};

		var show_panel = function (panel) {
			container.addClass('qm-show').removeClass('qm-hide');
			jQuery('.qm').removeClass('qm-panel-show');
			jQuery('#qm-panels').scrollTop(0);
			jQuery(panel).addClass('qm-panel-show');

			if (container.height() < minheight) {
				container.height(minheight);
			}

			if (container.hasClass('qm-show-right')) {
				body.css('margin-bottom', '');
			} else {
				body.css('margin-bottom', 'calc( ' + body_margin + ' + ' + container.height() + 'px )');
			}

			jQuery('#qm-panel-menu').find('button').removeAttr('aria-selected');
			jQuery('#qm-panel-menu').find('li').removeClass('qm-current-menu');
			var selected_menu = jQuery('#qm-panel-menu').find('[data-qm-href="' + panel + '"]').attr('aria-selected', true);

			if (selected_menu.length) {
				var selected_menu_top = selected_menu.position().top - 27;
				var menu_height = jQuery('#qm-panel-menu').height();
				var menu_scroll = jQuery('#qm-panel-menu').scrollTop();
				selected_menu.closest('#qm-panel-menu > ul > li').addClass('qm-current-menu');

				var selected_menu_off_bottom = (selected_menu_top > (menu_height));
				var selected_menu_off_top = (selected_menu_top < 0);

				if (selected_menu_off_bottom || selected_menu_off_top) {
					jQuery('#qm-panel-menu').scrollTop(selected_menu_top + menu_scroll - (menu_height / 2) + (selected_menu.outerHeight() / 2));
				}
			}

			jQuery('.qm-title-heading select').val(panel);

			localStorage.setItem(container_pinned_key, panel);

			var filters = jQuery(panel).find('.qm-filter');

			if (filters.length) {
				filters.trigger('change');
			} else {
				stripes(jQuery(panel).find('table'));
			}

		};

		if (jQuery('#wp-admin-bar-query-monitor').length) {

			var admin_bar_menu_container = document.createDocumentFragment();

			if (window.qm && window.qm.menu) {
				jQuery('#wp-admin-bar-query-monitor')
					.addClass(qm.menu.top.classname)
					.attr('dir', 'ltr')
					.find('a').eq(0)
					.html(qm.menu.top.title);

				jQuery.each(qm.menu.sub, function (i, el) {

					var new_menu = jQuery('#wp-admin-bar-query-monitor-placeholder')
						.clone()
						.attr('id', 'wp-admin-bar-' + el.id);
					new_menu
						.find('a').eq(0)
						.html(el.title)
						.attr('href', el.href);

					if ((typeof el.meta != 'undefined') && (typeof el.meta.classname != 'undefined')) {
						new_menu.addClass(el.meta.classname);
					}

					admin_bar_menu_container.appendChild(new_menu.get(0));

				});

				jQuery('#wp-admin-bar-query-monitor ul').append(admin_bar_menu_container);
			}

			jQuery('#wp-admin-bar-query-monitor').find('a').on('click', link_click);

			jQuery('#wp-admin-bar-query-monitor,#wp-admin-bar-query-monitor-default').show();

		} else {
			container.addClass('qm-peek').removeClass('qm-hide');
			jQuery('#qm-overview').addClass('qm-panel-show');
		}

		jQuery('#qm-panel-menu').find('button').on('click', link_click);

		container.find('.qm-filter').on('change', function (e) {

			var filter = jQuery(this).attr('data-filter'),
				table = jQuery(this).closest('table'),
				tr = table.find('tbody tr[data-qm-' + filter + ']'),
				// Escape the following chars with a backslash before passing into jQ selectors: [ ] ( ) ' " \
				val = jQuery(this).val().replace(/[[\]()'"\\]/g, "\\$&"),
				total = tr.removeClass('qm-hide-' + filter).length,
				hilite = jQuery(this).attr('data-highlight'),
				time = 0;

			key = jQuery(this).attr('id');
			if (val) {
				sessionStorage.setItem(key, jQuery(this).val());
			} else {
				sessionStorage.removeItem(key);
			}

			if (hilite) {
				table.find('tr').removeClass('qm-highlight');
			}

			if (jQuery(this).val() !== '') {
				if (hilite) {
					tr.filter('[data-qm-' + hilite + '*="' + val + '"]').addClass('qm-highlight');
				}
				tr.not('[data-qm-' + filter + '*="' + val + '"]').addClass('qm-hide-' + filter);
				jQuery(this).closest('th').addClass('qm-filtered');
			} else {
				jQuery(this).closest('th').removeClass('qm-filtered');
			}

			var matches = tr.filter(':visible');
			var filtered_count = 0;
			var total_count = 0;
			matches.each(function (i) {
				var row_time = jQuery(this).attr('data-qm-time');
				if (row_time) {
					time += parseFloat(row_time);
				}

				var row_count = jQuery(this).attr('data-qm-count');
				if (row_count) {
					filtered_count += parseFloat(row_count);
				} else {
					filtered_count++;
				}
			});
			if (time) {
				time = QM_i18n.number_format(time, 4);
			}

			tr.each(function (i) {
				var row_count = jQuery(this).attr('data-qm-count');
				if (row_count) {
					total_count += parseFloat(row_count);
				} else {
					total_count++;
				}
			});

			if (table.find('.qm-filtered').length) {
				var count = filtered_count + ' / ' + total_count;
			} else {
				var count = filtered_count;
			}

			table.find('.qm-items-number').text(count);
			table.find('.qm-items-time').text(time);

			stripes(table);
		});

		container.find('.qm-filter').each(function () {
			var key = jQuery(this).attr('id');
			var value = sessionStorage.getItem(key);
			if (value !== null) {
				// Escape the following chars with a backslash before passing into jQ selectors: [ ] ( ) ' " \
				var val = value.replace(/[[\]()'"\\]/g, "\\$&");
				if (!jQuery(this).find('option[value="' + val + '"]').length) {
					jQuery('<option>').attr('value', value).text(value).appendTo(this);
				}
				jQuery(this).val(value).trigger('change');
			}
		});

		container.find('.qm-filter-trigger').on('click', function (e) {
			var filter = jQuery(this).data('qm-filter'),
				value = jQuery(this).data('qm-value'),
				target = jQuery(this).data('qm-target');
			jQuery('#qm-' + target).find('.qm-filter').not('[data-filter="' + filter + '"]').val('').removeClass('qm-highlight').trigger('change');
			jQuery('#qm-' + target).find('[data-filter="' + filter + '"]').val(value).addClass('qm-highlight').trigger('change');
			show_panel('#qm-' + target);
			jQuery('#qm-' + target).focus();
			e.preventDefault();
		});

		container.find('.qm-toggle').on('click', function (e) {
			var el = jQuery(this);
			var currentState = el.attr('aria-expanded');
			var newState = 'true';
			if (currentState === 'true') {
				newState = 'false';
			}
			el.attr('aria-expanded', newState);
			var toggle = jQuery(this).closest('td').find('.qm-toggled');
			if (currentState === 'true') {
				if (toggle.length) {
					toggle.slideToggle(150, function () {
						el.closest('td').removeClass('qm-toggled-on');
						el.text(el.attr('data-on'));
					});
				} else {
					el.closest('td').removeClass('qm-toggled-on');
					el.text(el.attr('data-on'));
				}
			} else {
				el.closest('td').addClass('qm-toggled-on');
				el.text(el.attr('data-off'));
				toggle.slideToggle(150);
			}
			e.preventDefault();
		});

		container.find('.qm-highlighter').on('mouseenter', function (e) {

			var subject = jQuery(this).data('qm-highlight');
			var table = jQuery(this).closest('table');

			if (!subject) {
				return;
			}

			jQuery(this).addClass('qm-highlight');

			jQuery.each(subject.split(' '), function (i, el) {
				table.find('tr[data-qm-subject="' + el + '"]').addClass('qm-highlight');
			});

		}).on('mouseleave', function (e) {

			jQuery(this).removeClass('qm-highlight');
			jQuery(this).closest('table').find('tr').removeClass('qm-highlight');

		});

		jQuery('.qm').find('tbody a,tbody button').on('focus', function (e) {
			jQuery(this).closest('tr').addClass('qm-hovered');
		}).on('blur', function (e) {
			jQuery(this).closest('tr').removeClass('qm-hovered');
		});

		container.find('.qm table').on('sorted.qm', function () {
			stripes(jQuery(this));
		});

		jQuery(document).ajaxSuccess(function (event, response, options) {

			var errors = response.getResponseHeader('X-QM-php_errors-error-count');

			if (!errors) {
				return event;
			}

			errors = parseInt(errors, 10);

			if (window.console) {
				console.group(qm_l10n.ajax_error);
			}

			for (var key = 1; key <= errors; key++) {

				error = JSON.parse(response.getResponseHeader('X-QM-php_errors-error-' + key));

				if (window.console) {
					switch (error.type) {
						case 'warning':
							console.error(error);
							break;
						default:
							console.warn(error);
							break;
					}
				}

				if (jQuery('#qm-php_errors').find('[data-qm-key="' + error.key + '"]').length) {
					continue;
				}

				if (jQuery('#wp-admin-bar-query-monitor').length) {
					if (!qm.ajax_errors[error.type]) {
						jQuery('#wp-admin-bar-query-monitor')
							.addClass('qm-' + error.type)
							.find('a').first().append('<span class="ab-label qm-ajax-' + error.type + '"> &nbsp; Ajax: ' + error.type + '</span>');
					}
				}

				qm.ajax_errors[error.type] = true;

			}

			if (window.console) {
				console.groupEnd();
			}

			jQuery('#qm-ajax-errors').show();

			return event;

		});

		jQuery('.qm-auth').on('click', function (e) {
			var state = jQuery('#qm-settings').data('qm-state');
			var action = ('off' === state ? 'on' : 'off');

			jQuery.ajax(qm_l10n.ajaxurl, {
				type: 'POST',
				context: this,
				data: {
					action: 'qm_auth_' + action,
					nonce: qm_l10n.auth_nonce[action]
				},
				success: function (response) {
					jQuery(this).text(jQuery(this).data('qm-text-' + action));
					jQuery('#qm-settings').attr('data-qm-state', action).data('qm-state', action);
				},
				dataType: 'json',
				xhrFields: {
					withCredentials: true
				}
			});

			e.preventDefault();
		});

		var editorSuccessIndicator = jQuery('#qm-editor-save-status');
		editorSuccessIndicator.hide();

		jQuery('.qm-editor-button').on('click', function (e) {
			var state = jQuery('#qm-settings').data('qm-state');
			var editor = jQuery('#qm-editor-select').val();

			jQuery.ajax(qm_l10n.ajaxurl, {
				type: 'POST',
				context: this,
				data: {
					action: 'qm_editor_set',
					nonce: qm_l10n.auth_nonce['editor-set'],
					editor: editor
				},
				success: function (response) {
					if (response.success) {
						editorSuccessIndicator.show();
					}
				},
				dataType: 'json',
				xhrFields: {
					withCredentials: true
				}
			});

			e.preventDefault();
		});

		jQuery('.qm-theme-toggle').on('click', function (e) {
			container.attr('data-theme', jQuery(this).val());
			localStorage.setItem('qm-theme', jQuery(this).val());
		});

		jQuery.qm.tableSort({ target: jQuery('.qm-sortable') });

		var startY, startX, resizerHeight;

		jQuery(document).on('mousedown touchstart', '.qm-resizer', function (event) {
			event.stopPropagation();

			resizerHeight = jQuery(this).outerHeight() - 1;
			startY = container.outerHeight() + (event.clientY || event.originalEvent.targetTouches[0].pageY);
			startX = container.outerWidth() + (event.clientX || event.originalEvent.targetTouches[0].pageX);

			if (!container.hasClass('qm-show-right')) {
				jQuery(document).on('mousemove touchmove', qm_do_resizer_drag_vertical);
			} else {
				jQuery(document).on('mousemove touchmove', qm_do_resizer_drag_horizontal);
			}

			jQuery(document).on('mouseup touchend', qm_stop_resizer_drag);
		});

		function qm_do_resizer_drag_vertical(event) {
			var h = (startY - (event.clientY || event.originalEvent.targetTouches[0].pageY));
			if (h >= resizerHeight && h <= maxheight) {
				container.height(h);
				body.css('margin-bottom', 'calc( ' + body_margin + ' + ' + h + 'px )');
			}
		}

		function qm_do_resizer_drag_horizontal(event) {
			var w = (startX - event.clientX);
			if (w >= minwidth && w <= maxwidth) {
				container.width(w);
			}
			body.css('margin-bottom', '');
		}

		function qm_stop_resizer_drag(event) {
			jQuery(document).off('mousemove touchmove', qm_do_resizer_drag_vertical);
			jQuery(document).off('mousemove touchmove', qm_do_resizer_drag_horizontal);
			jQuery(document).off('mouseup touchend', qm_stop_resizer_drag);

			if (!container.hasClass('qm-show-right')) {
				localStorage.removeItem(container_position_key);
				localStorage.setItem(container_height_key, container.height());
			} else {
				localStorage.setItem(container_position_key, 'right');
				localStorage.setItem(container_width_key, container.width());
			}
		}

		var p = localStorage.getItem(container_position_key);
		var h = localStorage.getItem(container_height_key);
		var w = localStorage.getItem(container_width_key);
		if (!container.hasClass('qm-peek')) {
			if (p === 'right') {
				if (w !== null) {
					if (w < minwidth) {
						w = minwidth;
					}
					if (w > maxwidth) {
						w = maxwidth;
					}
					container.width(w);
				}
				container.addClass('qm-show-right');
			} else if (p !== 'right' && h !== null) {
				if (h < minheight) {
					h = minheight;
				}
				if (h > maxheight) {
					h = maxheight;
				}
				container.height(h);
			}
		}

		jQuery(window).on('resize', function () {
			var h = container.height();
			var w = container.width();

			maxheight = (jQuery(window).height() - toolbarHeight);
			maxwidth = jQuery(window).width();

			if (h < minheight) {
				container.height(minheight);
			}
			if (h > maxheight) {
				container.height(maxheight);
			}
			localStorage.setItem(container_height_key, container.height());

			if (w > jQuery(window).width()) {
				container.width(minwidth);
				localStorage.setItem(container_width_key, container.width());
			}
			if (jQuery(window).width() < 960) {
				container.removeClass('qm-show-right');
				localStorage.removeItem(container_position_key);
			}
		});

		jQuery('.qm-button-container-close').on('click', function () {
			container.removeClass('qm-show').height('').width('');
			body.css('margin-bottom', '');
			localStorage.removeItem(container_pinned_key);
		});

		jQuery('.qm-button-container-settings,a[href="#qm-settings"]').on('click', function () {
			show_panel('#qm-settings');
			jQuery('#qm-settings').focus();
		});

		jQuery('.qm-button-container-position').on('click', function () {
			container.toggleClass('qm-show-right');

			if (container.hasClass('qm-show-right')) {
				var w = localStorage.getItem(container_width_key);

				if (w !== null && w < jQuery(window).width()) {
					container.width(w);
				}

				body.css('margin-bottom', '');

				localStorage.setItem(container_position_key, 'right');
			} else {
				body.css('margin-bottom', 'calc( ' + body_margin + ' + ' + container.height() + 'px )');

				localStorage.removeItem(container_position_key);
			}
		});

		var pinned = localStorage.getItem(container_pinned_key);
		if (pinned && jQuery(pinned).length) {
			show_panel(pinned);
		}

		jQuery('.qm-title-heading select').on('change', function () {
			show_panel(jQuery(this).val());
			jQuery(jQuery(this).val()).focus();
		});

	} catch (error) {
		console.log(error);
	}
});
