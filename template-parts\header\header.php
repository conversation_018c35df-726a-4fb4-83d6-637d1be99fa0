<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$header_attributes = array(
	'id'    => 'page-header',
	'class' => array( 'header_classic-menu-right' ),
);

$header_attributes = Utilities::get_component_attributes(
	$header_attributes,
	array(
		'name'         => 'Header',
		'options'      => array(),
		'hasAnimation' => false,
	)
);

$has_menu = has_nav_menu( 'main_menu' );

do_action( 'asli/header/before_render' ); // BC
do_action( 'asli/page_header/before_render_start' );
?>

<header <?php Utilities::print_attributes( $header_attributes ); ?>>
	<?php do_action( 'asli/page_header/after_render_start' ); ?>
	<!-- Top bar -->
	<div class="header__bar header__bar_fixed borders-auto-opacity-solid d-flex js-header__bar" data-arts-header-logo="primary" data-arts-header-sticky-logo="primary" data-arts-header-sticky-class="bg-white">
		<!-- Site logo -->
		<?php get_template_part( 'template-parts/header/top-bar/partials/logo' ); ?>
		<!-- - Site logo -->
		<?php if ( $has_menu ) : ?>
			<!-- Menu [desktop only] -->
			<div class="header__col header__col header__col_fluid-paddings d-none d-lg-block ms-auto">
				<?php get_template_part( 'template-parts/menu/menu-classic' ); ?>
			</div>
			<!-- - Menu [desktop only] -->
			<!-- Burger [mobile menu only] -->
			<?php get_template_part( 'template-parts/header/top-bar/partials/burger' ); ?>
			<!-- - Burger [mobile menu only] -->
		<?php endif; ?>
		<?php if ( Utilities::is_woocommerce() ) : ?>
			<!-- Mini cart -->
			<?php get_template_part( 'template-parts/woocommerce/cart', 'mini' ); ?>
			<!-- - Mini cart -->
		<?php endif; ?>
		<!-- Bar border bottom -->
		<div class="header__border-horizontal header__border-container bg-bb"></div>
	</div>
	<!-- - Top bar -->
	<?php if ( $has_menu ) : ?>
		<!-- Overlay menu -->
		<?php get_template_part( 'template-parts/header/fullscreen-overlay-container/fullscreen-overlay-container' ); ?>
		<!-- - Overlay menu -->
	<?php endif; ?>
	<?php do_action( 'asli/page_header/before_render_end' ); ?>
</header>
<?php do_action( 'asli/header/after_render' ); // BC ?>
<?php do_action( 'asli/page_header/after_render_end' ); ?>
