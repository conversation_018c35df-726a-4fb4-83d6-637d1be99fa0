{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "5ddd922c518dde75081d58436ed08cae", "packages": [{"name": "arts/notice-manager", "version": "dev-master", "dist": {"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsNoticeManager", "reference": "f1d4d2817ee1cfd64bc4b53e1c28185cadb758ae"}, "require": {"php": ">=7.4"}, "type": "library", "autoload": {"psr-4": {"Arts\\NoticeManager\\": "src/php/"}}, "description": "Manages the notices appearance in the WordPress admin panel", "transport-options": {"symlink": true, "relative": false}}, {"name": "arts/utilities", "version": "dev-master", "dist": {"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsUtilities", "reference": "282313d41a292caa3ae623a9c5ff5fb6485f3669"}, "require": {"php": ">=7.2"}, "require-dev": {"antecedent/patchwork": "^2.1", "brain/monkey": "^2.6", "phpunit/phpunit": "^9.6", "yoast/phpunit-polyfills": "^4.0"}, "type": "library", "autoload": {"psr-4": {"Arts\\Utilities\\": "src/php/"}}, "autoload-dev": {"psr-4": {"Tests\\": "__tests__/php/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A comprehensive collection of utility methods for WordPress theme and plugin development, with a focus on Elementor integration.", "transport-options": {"symlink": true, "relative": false}}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"arts/notice-manager": 20, "arts/utilities": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}