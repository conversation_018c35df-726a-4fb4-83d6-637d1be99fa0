<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use Arts\Utilities\Utilities;

$defaults                      = array(
	'customLoaderImage' => null,
);
$args                          = wp_parse_args( $args, $defaults );
$preloader_loader_custom_image = Utilities::get_kit_settings( 'preloader_loader_custom_image', null, false );

$attr = array(
	'class'   => 'preloader__custom-loader js-preloader__custom-loader',
	'loading' => 'eager',
);

$container_attributes = array(
	'class' => array(
		'preloader__wrapper-counter',
		'js-preloader__wrapper-counter',
	),
);

$circle_args = array(
	'size'   => 600,
	'stroke' => 3,
	'inner'  => array(
		'class' => array( 'js-preloader__circle' ),
	),
);

if ( ! $args['customLoaderImage'] && is_array( $preloader_loader_custom_image ) && isset( $preloader_loader_custom_image['id'] ) ) {
	$args['customLoaderImage'] = $preloader_loader_custom_image['id'];
}

?>

<?php if ( $args['customLoaderEnabled'] ) : ?>
	<!-- Custom Loader -->
	<div <?php Utilities::print_attributes( $container_attributes ); ?>>
		<?php if ( $args['customLoaderImage'] ) : ?>
			<?php echo wp_get_attachment_image( $args['customLoaderImage'], 'full', false, $attr ); ?>
		<?php endif; ?>
		<?php get_template_part( 'template-parts/svg/circle', '', $circle_args ); ?>
	</div>
	<!-- - Custom Loader -->
<?php endif; ?>
