<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( post_password_required() ) {
	return;
}

use \Arts\Utilities\Utilities;

$defaults = array(
	'ajax_enabled' => false,
);

$args = apply_filters( 'arts_comments_options', $defaults );

$ajax_enabled    = Utilities::get_kit_settings( 'ajax_enabled', false );
$comments_closed = ! comments_open() && get_comments_number() && post_type_supports( get_post_type(), 'comments' );
$have_comments   = have_comments();

if ( ! $have_comments && $comments_closed ) {
	return;
}

$comments_attributes = array(
	'id'    => 'comments',
	'class' => array( 'comments-area' ),
);

if ( ! $comments_closed ) {
	if ( $ajax_enabled ) {
		$comments_attributes['data-barba-prevent'] = 'all';
	}

	if ( $args['ajax_enabled'] ) {
		$comments_attributes = Utilities::get_component_attributes(
			$comments_attributes,
			array(
				'name' => 'Comments',
			)
		);
	}
}

?>

<div <?php Utilities::print_attributes( $comments_attributes ); ?>>
	<?php if ( $have_comments ) : ?>
		<?php
			$comments_number  = get_comments_number();
			$comments_content = '';
		if ( $comments_number === '1' ) {
			$comments_content = sprintf( '%1$s %2$s', $comments_number, esc_html__( 'Comment', 'asli' ) );
		} else {
			$comments_content = sprintf( '%1$s %2$s', $comments_number, esc_html__( 'Comments', 'asli' ) );
		}
		?>
		<h4 class="comments-title"><?php echo esc_html( $comments_content ); ?></h4>
		<ol class="comment-list">
			<?php
				wp_list_comments(
					array(
						'avatar_size' => 80,
						'style'       => 'ol',
						'short_ping'  => true,
						'walker'      => new Arts_Walker_Comment(),
					)
				);
			?>
		</ol>
		<?php the_comments_pagination(); ?>
	<?php endif; ?>
	<?php if ( $comments_closed ) : ?>
		<div class="post-password-form-wrapper">
			<p class="no-comments post-password-form-error"><i class="post-password-form-message-icon material-icons">lock</i><?php echo esc_html__( 'Comments are closed.', 'asli' ); ?></p>
		</div>
	<?php else : ?>
		<?php
			comment_form(
				array(
					'title_reply_before' => '<h4 id="reply-title" class="comment-reply-title">',
					'title_reply_after'  => '</h4>',
				)
			);
		?>
	<?php endif; ?>
</div>
