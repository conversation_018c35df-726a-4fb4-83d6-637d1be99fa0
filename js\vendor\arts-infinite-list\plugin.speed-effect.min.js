"use strict";(self.webpackChunkArtsInfiniteList=self.webpackChunkArtsInfiniteList||[]).push([[934],{133:(e,t,r)=>{r.r(t),r.d(t,{default:()=>i});var n=r(199);const s={skew:.25,multiLane:!1};class i extends n.v{constructor({autoLoad:e=!1,container:t,options:r,controller:n,config:i}){super({autoLoad:e,container:t,options:r,controller:n,config:i,defaults:s}),this.init()}init(){if(!this.enabled)if(this.config.multiLane)for(let e=0;e<this.controller.lanes.length;e++)this._registerTransformHook(this.config.multiLane[e],e);else this._registerTransformHook(this.config)}destroy(){this.enabled&&this.controller.removeTransformHooks()}_registerTransformHook(e,t){"skew"in e&&this.controller.addTransformHook(this._transformerSkew.bind(this),t),"scale"in e&&this.controller.addTransformHook(this._transformerScale.bind(this),t)}_transformerSkew({indexLane:e,indexItem:t,progressItem:r,translateItem:n,laneGeometry:s,laneDirection:i,laneVelocity:o}){const a=n,l=this.getConfigOption("skew",e),c={x:0,y:0,z:0};if("horizontal"===this.options.direction)switch(i){case"forward":c.x=o*l*100;break;case"backward":c.x=-o*l*100;break;default:c.x=0}if("vertical"===this.options.direction)switch(i){case"forward":c.y=o*l*100;break;case"backward":c.y=-o*l*100;break;default:c.y=0}return{translate:a,skew:c}}_transformerScale({indexLane:e,indexItem:t,progressItem:r,translateItem:n,laneGeometry:s,laneDirection:i,laneVelocity:o}){return{translate:n,scale:1+o*this.getConfigOption("scale",e)*2}}}}}]);