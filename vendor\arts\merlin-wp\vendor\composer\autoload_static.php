<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit5dadfcb385406cb39e89f505d4015db1
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'ProteusThemes\\WPContentImporter2\\' => 33,
        ),
        'M' => 
        array (
            'Monolog\\' => 8,
        ),
        'A' => 
        array (
            'Arts\\Utilities\\' => 15,
            'Arts\\Merlin\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/Psr/Log',
        ),
        'ProteusThemes\\WPContentImporter2\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/wp-content-importer-v2/src',
        ),
        'Monolog\\' => 
        array (
            0 => __DIR__ . '/..' . '/monolog/monolog/src/Monolog',
        ),
        'Arts\\Utilities\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/utilities/src/php',
        ),
        'Arts\\Merlin\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit5dadfcb385406cb39e89f505d4015db1::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit5dadfcb385406cb39e89f505d4015db1::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit5dadfcb385406cb39e89f505d4015db1::$classMap;

        }, null, ClassLoader::class);
    }
}
