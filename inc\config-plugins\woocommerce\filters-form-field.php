<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter(
	'woocommerce_form_field_args',
	function( $args, $key, $value ) {
		$filterable_types = array(
			'text',
			'email',
			'tel',
			'state',
		);

		if ( in_array( $args['type'], $filterable_types ) ) {
			$args['class'][]       = 'input-float';
			$args['class'][]       = 'woo-form-control-wrap';
			$args['input_class'][] = 'input-float__input';
			$args['label_class'][] = 'input-float__label';
		}

		return $args;
	},
	10,
	3
);
