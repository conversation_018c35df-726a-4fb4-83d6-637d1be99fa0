function _0x479c(_0x2b25d8,_0x2ca81c){const _0x525c83=_0x525c();return _0x479c=function(_0x479c4e,_0x57561d){_0x479c4e=_0x479c4e-0x6c;let _0x4b8960=_0x525c83[_0x479c4e];return _0x4b8960;},_0x479c(_0x2b25d8,_0x2ca81c);}const _0x55a19b=_0x479c;(function(_0x2f7015,_0x21b9d4){const _0xc9c437=_0x479c,_0x404cab=_0x2f7015();while(!![]){try{const _0x47fac2=-parseInt(_0xc9c437(0x10b))/0x1+parseInt(_0xc9c437(0x119))/0x2*(-parseInt(_0xc9c437(0xd5))/0x3)+parseInt(_0xc9c437(0xcf))/0x4+parseInt(_0xc9c437(0xbe))/0x5*(-parseInt(_0xc9c437(0x87))/0x6)+-parseInt(_0xc9c437(0x10c))/0x7*(-parseInt(_0xc9c437(0xfe))/0x8)+-parseInt(_0xc9c437(0xc4))/0x9+parseInt(_0xc9c437(0xeb))/0xa*(parseInt(_0xc9c437(0x11d))/0xb);if(_0x47fac2===_0x21b9d4)break;else _0x404cab['push'](_0x404cab['shift']());}catch(_0x612914){_0x404cab['push'](_0x404cab['shift']());}}}(_0x525c,0x613a9));function _0x525c(){const _0x414f63=['marquee','toggleClass','_webGLEnabled','_attachEvents','_unpinElements','_detachDragListeners','dragPressed','images','timeline','_handlers','hoverEffect','setup','toggleScrollingClass','data-arts-infinite-list-options','object','center','add','fixedCanvasWrapper','fixedWrapper','3787185tYuRsz','instance','create','.marquee-headings-hover__wrapper-img','enabled','pluginsReady','5273181sKYADX','clear','.js-marquee-headings-hover__label','hoverIn','_updateElements','.js-marquee-headings-hover__item','label','_toggleInteraction','postTask','finally','animation','2315060ulIPcA','_createInfiniteListHeadings','isElementorEditor','_hasAnimationScene','snapOnRelease','_isWebGLEnabled','48417uWyTYD','_detachEvents','togglePressedClass','_highlightActiveElements','prepareAnimation','addMarqueeDelimiter','default','init','filter','wheel,touch,pointer','opacity,visibility','timeScale','Drag','dataReady','animatePlane','_setWebGLReady','autoCenterFirstItem','_onMouseEnter','length','.canvas-wrapper_sticky','drag','string','2630FNHuVv','color','animateScale','pointer-events-inner-none','addAJAXStartEventListener','bottom\x20bottom','.js-marquee-headings-hover__fixed-wrapper','all','100%','power3.out','getLinkTarget','querySelectorAll','push','.js-marquee-headings-hover__link','arrowsDistance','classList','currentClass','hideNative','scrollUpdate','560ZdHnMX','animationFade','innerSelectors','type','setLoading','items','kill','cursorRef','zIndex','then','rotatingButtons','scroll','utilities','131274aMmPfn','16345zGeNbe','top\x20top','delimiter','options','_webGLLoader','elements','.js-marquee-headings-hover__lane','toggle','_onMouseLeave','_setRefs','center\x20bottom','marquee-headings-hover_pressed','marquee-headings-hover_scrolling','38SlNQSg','_pinElements','clamp','onScrollRotatingButtonSpeed','60115cGFNln','lanes','forEach','triggerStickyCanvas','enable','curtains','var(--color-accent-dark-theme)','function','current','_onScrollUpdate','power3.inOut','MarqueeHeadingsHoverWebGL','ready','loop','_initMarquee','preventScroll','assign','animationReveal','getAttribute','RotatingButton','destroy','infiniteListHeadings','bind','getRevealAnimation','scale','_setImages','opacityEffect','virtualScroll','_onDragPressed','.canvas-wrapper','6DNehsQ','_animateImage','updateRef','horizontal','controller','speedEffect','stScrub','reset','disable','.js-marquee-headings-hover__animation-scale','.js-marquee-headings-hover__animation-fade','components','_toggleMarqueeAnimation','triggerStickyWrapper','.js-marquee-headings-hover__animation-reveal','img,\x20video','load','_attachDragListeners','animationScale','attachEvents','start','_getWebGLLoader','_clamp','set','element','marquee-headings-hover_dragging','className','active','_hideImages','itemIdAttribute','direction','labels','keyboard','utils','plugins','data-post-id'];_0x525c=function(){return _0x414f63;};return _0x525c();}export default class MarqueeHeadingsHover extends BaseComponent{constructor({name:_0x427db5,loadInnerComponents:_0x475fe0,loadAfterSyncStyles:_0x3471b6,parent:_0x383c9a,element:_0x5bb229}){const _0xd104cb=_0x479c;super({'name':_0x427db5,'loadInnerComponents':_0x475fe0,'loadAfterSyncStyles':_0x3471b6,'parent':_0x383c9a,'element':_0x5bb229,'defaults':{'webGL':{'enabled':![],'vertices':0x10},'preventScroll':![],'drag':{'label':_0xd104cb(0xe1),'arrowsDistance':0x32,'scale':1.5,'hideNative':!![],'toggleClass':'marquee-headings-hover_mouse-drag','color':'var(--ui-element-color-light-theme)','background':_0xd104cb(0x6f)},'delimiter':'&nbsp;&nbsp;/&nbsp;&nbsp;','direction':_0xd104cb(0x8a),'matchMedia':![],'onScrollRotatingButtonSpeed':0x2,'loop':!![],'autoCenterFirstItem':!![],'marquee':{'speed':0.6},'scroll':app[_0xd104cb(0x10f)][_0xd104cb(0x84)],'type':_0xd104cb(0xde),'toggleScrollingClass':_0xd104cb(0x118),'toggleDraggingClass':_0xd104cb(0xa0),'togglePressedClass':_0xd104cb(0x117),'snapOnRelease':{'keyboard':!![],'toggleActiveItemClass':_0xd104cb(0xa2),'removeActiveClassOnInteraction':![]},'wheelSpeed':-0x1,'speedEffect':![],'opacityEffect':![],'currentClass':_0xd104cb(0x71),'itemIdAttribute':_0xd104cb(0xaa)},'innerElements':{'canvasWrapper':_0xd104cb(0x86),'fixedCanvasWrapper':_0xd104cb(0xe8),'lanes':_0xd104cb(0x112),'items':_0xd104cb(0xc9),'labels':_0xd104cb(0xc6),'wrappers':'.js-marquee-headings-hover__wrapper','fixedWrapper':_0xd104cb(0xf1),'links':_0xd104cb(0xf8),'images':_0xd104cb(0xc1),'animationFade':_0xd104cb(0x91),'animationScale':_0xd104cb(0x90),'animationReveal':_0xd104cb(0x95)}}),this['_handlers']={'hoverIn':this[_0xd104cb(0xe6)][_0xd104cb(0x7f)](this),'hoverOut':this['_onMouseLeave'][_0xd104cb(0x7f)](this),'dragPressed':this[_0xd104cb(0x85)]['bind'](this),'scrollUpdate':this['_onScrollUpdate'][_0xd104cb(0x7f)](this)},this[_0xd104cb(0xe2)]['finally'](()=>{const _0x5d4e4a=_0xd104cb;this['tl']=gsap[_0x5d4e4a(0xb3)](),this[_0x5d4e4a(0x9d)]=gsap[_0x5d4e4a(0xa8)][_0x5d4e4a(0x11b)](0x1,0xa),this[_0x5d4e4a(0xad)]=this[_0x5d4e4a(0xd4)](),this[_0x5d4e4a(0x110)]=this[_0x5d4e4a(0xad)]?this['_getWebGLLoader']():null,this[_0x5d4e4a(0xb6)]();});}['init'](){return new Promise(_0x37fcd5=>{const _0x3a9992=_0x479c,_0x3b0e4e=[app[_0x3a9992(0x10a)][_0x3a9992(0xda)](this[_0x3a9992(0x10f)][_0x3a9992(0x10e)],this['elements'][_0x3a9992(0xa6)]),this[_0x3a9992(0x82)]()];this[_0x3a9992(0x115)](),Promise[_0x3a9992(0xf2)](_0x3b0e4e)[_0x3a9992(0xcd)](()=>{const _0x30c89f=_0x3a9992;this[_0x30c89f(0xd0)](),this[_0x30c89f(0x7e)][_0x30c89f(0xc3)]['finally'](()=>{const _0x1b0b88=_0x30c89f;this[_0x1b0b88(0xd2)]()&&this[_0x1b0b88(0xcb)](![]),this[_0x1b0b88(0x110)]?(this[_0x1b0b88(0x102)](!![]),this[_0x1b0b88(0x110)][_0x1b0b88(0x107)](_0x3025ef=>{const _0xc1b16e=_0x1b0b88;this[_0xc1b16e(0x6e)]=new _0x3025ef[(_0xc1b16e(0xdb))]({'element':this[_0xc1b16e(0x9f)],'elements':this[_0xc1b16e(0x111)],'options':this[_0xc1b16e(0x10f)],'infiniteListHeadings':this[_0xc1b16e(0x7e)]}),this['curtains'][_0xc1b16e(0xdc)]()[_0xc1b16e(0xcd)](()=>{const _0xbb63dc=_0xc1b16e;this[_0xbb63dc(0xe4)](),this[_0xbb63dc(0x11a)](),this[_0xbb63dc(0xae)](),this['infiniteListHeadings']['update'](),this[_0xbb63dc(0x102)](![]);}),_0x37fcd5(!![]);})['catch'](()=>_0x37fcd5(!![]))):(this['_pinElements'](),this[_0x1b0b88(0xae)](),_0x37fcd5(!![]));});});});}['destroy'](){return new Promise(_0x1da0e9=>{const _0x283f71=_0x479c,_0x486a0c=[];this[_0x283f71(0xd6)]();if(this['infiniteListHeadings']&&typeof this[_0x283f71(0x7e)][_0x283f71(0x7d)]===_0x283f71(0x70)){const _0x16fe04=scheduler[_0x283f71(0xcc)](()=>{const _0x506c99=_0x283f71;this[_0x506c99(0x7e)]['destroy']();});_0x486a0c[_0x283f71(0xf7)](_0x16fe04);}if(this[_0x283f71(0x6e)]){const _0x4c0ace=scheduler[_0x283f71(0xcc)](()=>{const _0x5bfd96=_0x283f71;this[_0x5bfd96(0x6e)][_0x5bfd96(0x7d)]();});_0x486a0c[_0x283f71(0xf7)](_0x4c0ace);}Promise[_0x283f71(0xf2)](_0x486a0c)[_0x283f71(0xcd)](()=>_0x1da0e9(!![]));});}[_0x55a19b(0x9c)](){const _0x4795d8=_0x55a19b;return app['componentsManager'][_0x4795d8(0x97)]({'properties':app[_0x4795d8(0x92)][_0x4795d8(0x74)]});}[_0x55a19b(0xd9)](){return new Promise(_0x38bd36=>{const _0x2cb99e=_0x479c;this[_0x2cb99e(0xc8)]({'container':this[_0x2cb99e(0x9f)],'elements':this[_0x2cb99e(0x100)]})[_0x2cb99e(0xcd)](()=>{const _0xd6efa4=_0x2cb99e;this[_0xd6efa4(0x7e)]?this[_0xd6efa4(0x7e)][_0xd6efa4(0x75)][_0xd6efa4(0xcd)](()=>{const _0x3742f6=_0xd6efa4,_0x247cd2=gsap[_0x3742f6(0xb3)]({'onComplete':()=>_0x38bd36(!![])});this[_0x3742f6(0x111)][_0x3742f6(0x7a)][_0x3742f6(0xe7)]&&_0x247cd2[_0x3742f6(0x9e)](this['elements']['animationReveal'],{'y':_0x3742f6(0xf3)}),this[_0x3742f6(0x111)][_0x3742f6(0xff)]['length']&&_0x247cd2[_0x3742f6(0x9e)](this[_0x3742f6(0x111)][_0x3742f6(0xff)],{'autoAlpha':0x0}),this[_0x3742f6(0x111)][_0x3742f6(0x99)][_0x3742f6(0xe7)]&&_0x247cd2[_0x3742f6(0x9e)](this[_0x3742f6(0x111)][_0x3742f6(0x99)],{'scale':0x0,'transformOrigin':'center\x20center'});}):_0x38bd36(!![]);});});}[_0x55a19b(0x80)](){const _0xf08343=_0x55a19b,_0xb46c0e=gsap[_0xf08343(0xb3)]({'paused':!![],'onComplete':()=>{const _0x1927d7=_0xf08343;this[_0x1927d7(0xcb)](!![]),this[_0x1927d7(0x77)]();}});return this[_0xf08343(0x111)][_0xf08343(0x7a)][_0xf08343(0xe7)]&&_0xb46c0e['to'](this[_0xf08343(0x111)]['animationReveal'],{'y':'0%','duration':1.2,'ease':'power3.out','stagger':distributeByPosition({'from':_0xf08343(0x9b),'axis':'y','amount':0.6})},_0xf08343(0x9b)),this[_0xf08343(0x111)]['animationFade']['length']&&_0xb46c0e['to'](this[_0xf08343(0x111)][_0xf08343(0xff)],{'autoAlpha':0x1,'duration':1.2,'stagger':0.05,'clearProps':_0xf08343(0xdf),'ease':_0xf08343(0xf4)},'start'),this[_0xf08343(0x111)][_0xf08343(0x99)][_0xf08343(0xe7)]&&_0xb46c0e[_0xf08343(0xed)](this[_0xf08343(0x111)]['animationScale'],{'ease':_0xf08343(0xf4),'duration':1.2,'animateFrom':_0xf08343(0xba)},_0xf08343(0x9b)),_0xb46c0e;}['_toggleInteraction'](_0x8e35a8=!![]){const _0x3859d3=_0x55a19b;this[_0x3859d3(0x9f)][_0x3859d3(0xfa)][_0x3859d3(0x113)](_0x3859d3(0xee),!_0x8e35a8),this['infiniteListHeadings']&&this['infiniteListHeadings']['pluginsReady']['finally'](()=>{const _0x32cbf3=_0x3859d3;this[_0x32cbf3(0x7e)]&&'scroll'in this[_0x32cbf3(0x7e)]['plugins']&&(this[_0x32cbf3(0x7e)][_0x32cbf3(0xa9)][_0x32cbf3(0x109)]['ignore']=!_0x8e35a8);});}[_0x55a19b(0x82)](){return new Promise(_0x37c0e0=>{const _0x2d4299=_0x479c,_0x3a0ae5=[];this[_0x2d4299(0x111)][_0x2d4299(0xb2)][_0x2d4299(0x11f)](_0x2946e3=>{const _0x3da1e1=_0x2d4299,_0x59b691=new Promise(_0x5881e9=>{const _0x4cabcc=_0x479c,_0x2f5fac=_0x2946e3['querySelectorAll'](_0x4cabcc(0x96));gsap[_0x4cabcc(0x9e)](_0x2f5fac,{'scale':1.05,'onComplete':()=>_0x5881e9(!![])});});_0x3a0ae5[_0x3da1e1(0xf7)](_0x59b691);}),Promise['all'](_0x3a0ae5)[_0x2d4299(0xcd)](()=>_0x37c0e0(!![]));});}[_0x55a19b(0xd0)](){const _0xa9080c=_0x55a19b;typeof this[_0xa9080c(0x10f)][_0xa9080c(0xd3)]===_0xa9080c(0xb9)&&!!this[_0xa9080c(0x10f)][_0xa9080c(0xd3)][_0xa9080c(0xa7)]&&!!app[_0xa9080c(0x10f)][_0xa9080c(0xd1)]&&Object[_0xa9080c(0x79)](this[_0xa9080c(0x10f)][_0xa9080c(0xd3)],{'keyboard':![]}),this['infiniteListHeadings']=new ArtsInfiniteList(this['element'],{'direction':this[_0xa9080c(0x10f)][_0xa9080c(0xa5)],'mapWheelEventYtoX':!![],'listElementsSelector':this[_0xa9080c(0x100)][_0xa9080c(0x103)],'autoCenterFirstItem':this['options'][_0xa9080c(0xe5)],'multiLane':{'laneSelector':this['innerSelectors'][_0xa9080c(0x11e)],'laneOptionsAttribute':_0xa9080c(0xb8)},'matchMedia':this['options']['matchMedia'],'loop':this[_0xa9080c(0x10f)][_0xa9080c(0x76)],'autoClone':this[_0xa9080c(0x10f)][_0xa9080c(0x76)],'scroll':this[_0xa9080c(0x10f)][_0xa9080c(0x109)],'plugins':{'marquee':typeof this['options'][_0xa9080c(0xab)]==='object'?{'autoInit':this['_hasAnimationScene']()?![]:!![],...this[_0xa9080c(0x10f)][_0xa9080c(0xab)]}:![],'scroll':this[_0xa9080c(0x10f)]['scroll']?{'type':this[_0xa9080c(0x10f)][_0xa9080c(0x101)],'toggleScrollingClass':this[_0xa9080c(0x10f)][_0xa9080c(0xb7)],'toggleDraggingClass':this[_0xa9080c(0x10f)]['toggleDraggingClass'],'togglePressedClass':this[_0xa9080c(0x10f)][_0xa9080c(0xd7)],'snapOnRelease':this[_0xa9080c(0x10f)]['snapOnRelease'],'preventDefault':this[_0xa9080c(0x10f)][_0xa9080c(0x78)]}:![],'speedEffect':this['options'][_0xa9080c(0x8c)],'opacityEffect':this[_0xa9080c(0x10f)][_0xa9080c(0x83)]}});}[_0x55a19b(0xe6)](_0x2acce2){return new Promise(_0x188b1b=>{const _0x362b31=_0x479c,_0x15668f=app['utilities']['getLinkTarget'](_0x2acce2);_0x15668f&&this['_toggleMarqueeAnimation'](![])[_0x362b31(0x107)](()=>this[_0x362b31(0xd8)](_0x15668f,!![]))[_0x362b31(0x107)](()=>this[_0x362b31(0x88)](_0x15668f))['finally'](()=>_0x188b1b(!![]));});}[_0x55a19b(0x114)](_0x3c55d8){return new Promise(_0x565eb0=>{const _0x3460cb=_0x479c,_0x5600d6=app[_0x3460cb(0x10a)][_0x3460cb(0xf5)](_0x3c55d8);_0x5600d6&&this['_toggleMarqueeAnimation'](!![])[_0x3460cb(0x107)](()=>this[_0x3460cb(0xd8)](_0x5600d6,![]))[_0x3460cb(0x107)](()=>this[_0x3460cb(0xa3)]())[_0x3460cb(0xcd)](()=>_0x565eb0(!![]));});}[_0x55a19b(0xae)](){const _0x39fd1b=_0x55a19b;app[_0x39fd1b(0xb5)][_0x39fd1b(0x9a)](this[_0x39fd1b(0x9f)],this[_0x39fd1b(0xb4)]['hoverIn'],this[_0x39fd1b(0xb4)]['hoverOut']),!!this[_0x39fd1b(0x10f)][_0x39fd1b(0x11c)]&&this['rotatingButtons'][_0x39fd1b(0xe7)]&&this[_0x39fd1b(0x7e)][_0x39fd1b(0x8b)]['on'](_0x39fd1b(0xfd),this['_handlers'][_0x39fd1b(0xfd)]),!!this[_0x39fd1b(0x10f)][_0x39fd1b(0xe9)]&&(typeof this[_0x39fd1b(0x10f)][_0x39fd1b(0xe9)][_0x39fd1b(0xac)]===_0x39fd1b(0xea)&&this[_0x39fd1b(0x9f)]['classList'][_0x39fd1b(0xbb)](this['options']['drag'][_0x39fd1b(0xac)]),this[_0x39fd1b(0x98)]()),app['utilities'][_0x39fd1b(0xef)](this[_0x39fd1b(0xd6)][_0x39fd1b(0x7f)](this)),app[_0x39fd1b(0x10a)][_0x39fd1b(0xef)](this[_0x39fd1b(0xaf)]['bind'](this));}[_0x55a19b(0xd6)](){const _0x482b6d=_0x55a19b;app[_0x482b6d(0xb5)]['detachEvents'](this[_0x482b6d(0x9f)],this[_0x482b6d(0xb4)][_0x482b6d(0xc7)],this[_0x482b6d(0xb4)]['hoverOut']),!!this[_0x482b6d(0x10f)]['onScrollRotatingButtonSpeed']&&this[_0x482b6d(0x108)]['length']&&this['infiniteListHeadings'][_0x482b6d(0x8b)]['off'](_0x482b6d(0xfd),this[_0x482b6d(0xb4)]['scrollUpdate']),!!this['options'][_0x482b6d(0xe9)]&&this[_0x482b6d(0xb0)]();}[_0x55a19b(0x98)](){const _0xfd664d=_0x55a19b;this[_0xfd664d(0x7e)][_0xfd664d(0x8b)]['on'](_0xfd664d(0xb1),this[_0xfd664d(0xb4)][_0xfd664d(0xb1)]);}[_0x55a19b(0xb0)](){const _0x88a877=_0x55a19b;this[_0x88a877(0x7e)]['controller']['off']('dragPressed',this[_0x88a877(0xb4)][_0x88a877(0xb1)]);}[_0x55a19b(0x85)](_0x36e8fc){const _0x24c9f4=_0x55a19b;this[_0x24c9f4(0x89)](_0x24c9f4(0x105),'CursorFollower'),this[_0x24c9f4(0x105)]&&(_0x36e8fc?(this[_0x24c9f4(0x105)][_0x24c9f4(0xbf)]['reset'](),this[_0x24c9f4(0x105)][_0x24c9f4(0xbf)][_0x24c9f4(0x9e)]({'autoReset':![],'arrows':this[_0x24c9f4(0x10f)]['direction'],'arrowsDistance':this[_0x24c9f4(0x10f)][_0x24c9f4(0xe9)][_0x24c9f4(0xf9)],'scale':this[_0x24c9f4(0x10f)][_0x24c9f4(0xe9)][_0x24c9f4(0x81)],'label':this[_0x24c9f4(0x10f)]['drag'][_0x24c9f4(0xca)]||'','className':this[_0x24c9f4(0x10f)][_0x24c9f4(0xe9)][_0x24c9f4(0xa1)]||'','hideNative':this['options'][_0x24c9f4(0xe9)][_0x24c9f4(0xfc)],'color':this['options'][_0x24c9f4(0xe9)][_0x24c9f4(0xec)],'background':this[_0x24c9f4(0x10f)][_0x24c9f4(0xe9)]['background']})):(this[_0x24c9f4(0x105)][_0x24c9f4(0xbf)]['set']({'autoReset':!![]}),this[_0x24c9f4(0x105)][_0x24c9f4(0xbf)][_0x24c9f4(0x8e)]()));}[_0x55a19b(0x72)](_0x11410){const _0x4b6c05=_0x55a19b,_0x4e1007=this[_0x4b6c05(0x9d)](_0x11410*0x64)*this[_0x4b6c05(0x10f)]['onScrollRotatingButtonSpeed'];this['rotatingButtons'][_0x4b6c05(0x11f)](_0x1a0d78=>{const _0x3eb35b=_0x4b6c05;_0x1a0d78[_0x3eb35b(0x8d)]&&_0x1a0d78['stScrub'][_0x3eb35b(0xce)][_0x3eb35b(0xe0)](_0x4e1007);});}[_0x55a19b(0x93)](_0x254834=!![]){return new Promise(_0x1a86ca=>{const _0x230a93=_0x479c;if(!this[_0x230a93(0x7e)]||!this['infiniteListHeadings'][_0x230a93(0xc2)]){_0x1a86ca(!![]);return;}!!this[_0x230a93(0x10f)][_0x230a93(0xab)]&&_0x230a93(0xab)in this[_0x230a93(0x7e)][_0x230a93(0xa9)]&&(_0x254834?this[_0x230a93(0x7e)][_0x230a93(0xa9)][_0x230a93(0xab)][_0x230a93(0x6d)]():this['infiniteListHeadings'][_0x230a93(0xa9)]['marquee'][_0x230a93(0x8f)]()),_0x1a86ca(!![]);});}[_0x55a19b(0xd8)](_0x5a1786,_0x4c43ea=!![]){return new Promise(_0xfd3b05=>{const _0x4069ec=_0x479c,_0x5bf4be=[],_0x4b9dd8=parseInt(_0x5a1786[_0x4069ec(0x7b)](''+this[_0x4069ec(0x10f)][_0x4069ec(0xa4)])),_0x1f8f08=[...this[_0x4069ec(0x9f)][_0x4069ec(0xf6)]('['+this[_0x4069ec(0x10f)]['itemIdAttribute']+'=\x22'+_0x4b9dd8+'\x22]')];_0x1f8f08[_0x4069ec(0xe7)]&&_0x1f8f08[_0x4069ec(0x11f)](_0x181582=>{const _0x3516d1=_0x4069ec,_0x22f5b8=new Promise(_0x13a43a=>{const _0x788366=_0x479c;_0x181582[_0x788366(0xfa)]['toggle'](''+this['options'][_0x788366(0xfb)],_0x4c43ea),_0x13a43a(!![]);});_0x5bf4be[_0x3516d1(0xf7)](_0x22f5b8);}),Promise[_0x4069ec(0xf2)](_0x5bf4be)[_0x4069ec(0xcd)](()=>_0xfd3b05(!![]));});}[_0x55a19b(0x88)](_0x60a605){return new Promise(_0x3ca81b=>{const _0x2dbf89=_0x479c,_0x5f2057=[],_0x41d8e4=_0x60a605[_0x2dbf89(0x7b)](''+this[_0x2dbf89(0x10f)]['itemIdAttribute']);this['tl'][_0x2dbf89(0xc5)](),this[_0x2dbf89(0x6e)]?this['tl'][_0x2dbf89(0xbb)](this['curtains'][_0x2dbf89(0xe3)](_0x41d8e4)):this['elements'][_0x2dbf89(0xb2)][_0x2dbf89(0x11f)](_0x8ca0af=>{const _0x321388=_0x2dbf89,_0x59c3d3=_0x8ca0af[_0x321388(0xf6)](_0x321388(0x96)),_0x778324=new Promise(_0x2d8ca4=>{const _0x2769a1=_0x321388;_0x8ca0af[_0x2769a1(0x7b)](''+this[_0x2769a1(0x10f)][_0x2769a1(0xa4)])===_0x41d8e4?this['tl'][_0x2769a1(0x9e)](_0x8ca0af,{'zIndex':0x32},'start')['to'](_0x59c3d3,{'scale':0x1,'transformOrigin':_0x2769a1(0x116),'duration':0.6,'ease':_0x2769a1(0x73)},_0x2769a1(0x9b))['to'](_0x8ca0af,{'--shape-size':0x64,'duration':0.6,'ease':_0x2769a1(0x73)},_0x2769a1(0x9b)):this['tl'][_0x2769a1(0x9e)](_0x8ca0af,{'clearProps':'zIndex'},_0x2769a1(0x9b))['to'](_0x59c3d3,{'scale':1.05,'transformOrigin':'center\x20bottom','duration':0.6,'ease':_0x2769a1(0x73)},_0x2769a1(0x9b))['to'](_0x8ca0af,{'--shape-size':0x0,'duration':0.6,'ease':_0x2769a1(0x73),'clearProps':'zIndex'},_0x2769a1(0x9b)),_0x2d8ca4(!![]);});_0x5f2057['push'](_0x778324);}),Promise['all'](_0x5f2057)['finally'](()=>_0x3ca81b(!![]));});}[_0x55a19b(0xa3)](){return new Promise(_0x406cb5=>{const _0x41979f=_0x479c,_0x115eb4=[];this['tl'][_0x41979f(0xc5)](),this['curtains']?this['tl'][_0x41979f(0xbb)](this[_0x41979f(0x6e)]['hidePlanes']()):this[_0x41979f(0x111)][_0x41979f(0xb2)][_0x41979f(0x11f)](_0x4386b7=>{const _0xe92f88=_0x41979f,_0x44c23f=_0x4386b7[_0xe92f88(0xf6)](_0xe92f88(0x96)),_0x44686c=new Promise(_0x951a55=>{const _0x255cfd=_0xe92f88;this['tl'][_0x255cfd(0x9e)](_0x4386b7,{'clearProps':_0x255cfd(0x106)})['to'](_0x44c23f,{'scale':1.05,'transformOrigin':_0x255cfd(0x116),'duration':0.6,'ease':'power3.inOut'},_0x255cfd(0x9b))['to'](_0x4386b7,{'--shape-size':0x0,'duration':0.6,'ease':_0x255cfd(0x73)},_0x255cfd(0x9b)),_0x951a55(!![]);});_0x115eb4[_0xe92f88(0xf7)](_0x44686c);}),Promise[_0x41979f(0xf2)](_0x115eb4)[_0x41979f(0xcd)](()=>_0x406cb5(!![]));});}[_0x55a19b(0x11a)](){const _0x3644b6=_0x55a19b;this[_0x3644b6(0x111)]['fixedCanvasWrapper']&&(this[_0x3644b6(0x6c)]=ScrollTrigger[_0x3644b6(0xc0)]({'trigger':this[_0x3644b6(0x9f)],'pin':this[_0x3644b6(0x111)][_0x3644b6(0xbc)],'pinSpacing':![],'start':'top\x20top','end':_0x3644b6(0xf0)})),this[_0x3644b6(0x111)]['fixedWrapper']&&(this[_0x3644b6(0x94)]=ScrollTrigger[_0x3644b6(0xc0)]({'trigger':this[_0x3644b6(0x9f)],'pin':this[_0x3644b6(0x111)][_0x3644b6(0xbd)],'start':_0x3644b6(0x10d),'end':_0x3644b6(0xf0)}));}[_0x55a19b(0xaf)](){const _0x42fafd=_0x55a19b;this[_0x42fafd(0x6c)]&&(this[_0x42fafd(0x6c)][_0x42fafd(0x104)](![]),this['triggerStickyCanvas']=null),this[_0x42fafd(0x94)]&&(this[_0x42fafd(0x94)][_0x42fafd(0x104)](![]),this[_0x42fafd(0x94)]=null);}[_0x55a19b(0x115)](){const _0x495dfc=_0x55a19b;this[_0x495dfc(0x108)]=this[_0x495dfc(0x92)][_0x495dfc(0xdd)]((_0x4be858,_0x502655)=>_0x4be858['name']===_0x495dfc(0x7c)),this[_0x495dfc(0x89)](_0x495dfc(0x105),'CursorFollower');}[_0x55a19b(0x77)](){const _0x487c18=_0x55a19b;this[_0x487c18(0x7e)]&&this['infiniteListHeadings'][_0x487c18(0xc3)][_0x487c18(0xcd)](()=>{const _0x55dddb=_0x487c18;this[_0x55dddb(0x7e)][_0x55dddb(0xa9)][_0x55dddb(0xab)]&&this['infiniteListHeadings'][_0x55dddb(0xa9)][_0x55dddb(0xab)][_0x55dddb(0xdc)]();});}}