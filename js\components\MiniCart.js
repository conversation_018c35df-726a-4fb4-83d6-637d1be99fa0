function _0xf985(){const _0x5f4a2a=['_attachEvents','76102gYAcrP','_handlers','adding_to_cart','18kItNSE','off','now','jQuery','addingToCart','39Rektpw','10376630AmRzqT','init','_getFetchTime','9154929OsUXNQ','loadingClass','_onAddedToCart','3462020exrqcr','617219CsUuzO','added_to_cart','876738ORoQnx','body','bind','8MnypHx','addedToCart','2426512AiQcTO','addEventListener','fetchTimer','finally','classList','fetchAfterDelay','_startFetchTimer','options','loading','_detachEvents','dataReady','removeEventListener'];_0xf985=function(){return _0x5f4a2a;};return _0xf985();}const _0x4b6007=_0x18f5;(function(_0x52b8b0,_0x1d9c91){const _0x267d2a=_0x18f5,_0x2d0304=_0x52b8b0();while(!![]){try{const _0x495f54=parseInt(_0x267d2a(0x118))/0x1+parseInt(_0x267d2a(0x108))/0x2*(-parseInt(_0x267d2a(0x110))/0x3)+-parseInt(_0x267d2a(0x11f))/0x4+parseInt(_0x267d2a(0x117))/0x5+-parseInt(_0x267d2a(0x11a))/0x6+-parseInt(_0x267d2a(0x114))/0x7*(parseInt(_0x267d2a(0x11d))/0x8)+-parseInt(_0x267d2a(0x10b))/0x9*(-parseInt(_0x267d2a(0x111))/0xa);if(_0x495f54===_0x1d9c91)break;else _0x2d0304['push'](_0x2d0304['shift']());}catch(_0xe5fc5a){_0x2d0304['push'](_0x2d0304['shift']());}}}(_0xf985,0xca8f8));function _0x18f5(_0x31d601,_0x23ffcc){const _0xf98502=_0xf985();return _0x18f5=function(_0x18f559,_0x40d6f9){_0x18f559=_0x18f559-0x100;let _0x1cbab7=_0xf98502[_0x18f559];return _0x1cbab7;},_0x18f5(_0x31d601,_0x23ffcc);}export default class MiniCart extends BaseComponent{constructor({name:_0x59d4c4,loadInnerComponents:_0x2ab35c,loadAfterSyncStyles:_0x207abe,parent:_0x5f5b8f,element:_0x27f50d}){const _0x3460f1=_0x18f5;super({'name':_0x59d4c4,'loadInnerComponents':_0x2ab35c,'loadAfterSyncStyles':_0x207abe,'parent':_0x5f5b8f,'element':_0x27f50d,'defaults':{'loadingClass':_0x3460f1(0x103),'fetchAfterDelay':0x190},'innerElements':{}}),this[_0x3460f1(0x109)]={'addingToCart':this['_onAddingToCart'][_0x3460f1(0x11c)](this),'addedToCart':this[_0x3460f1(0x116)][_0x3460f1(0x11c)](this)},this[_0x3460f1(0x105)][_0x3460f1(0x122)](()=>{this['fetchTimer']=0x0,this['setup']();});}[_0x4b6007(0x112)](){return new Promise(_0x32d1b6=>{this['_attachEvents'](),_0x32d1b6(!![]);});}['destroy'](){return new Promise(_0x410870=>{const _0x3594bc=_0x18f5;this[_0x3594bc(0x104)](),_0x410870(!![]);});}[_0x4b6007(0x101)](){const _0x26d254=_0x4b6007;this[_0x26d254(0x121)]=Date[_0x26d254(0x10d)]();}[_0x4b6007(0x113)](){const _0x5f1f8e=_0x4b6007;return Date[_0x5f1f8e(0x10d)]()-this['fetchTimer'];}[_0x4b6007(0x107)](){const _0x2fe2ed=_0x4b6007;window[_0x2fe2ed(0x10e)]?(jQuery(document['body'])['on'](_0x2fe2ed(0x10a),this[_0x2fe2ed(0x109)][_0x2fe2ed(0x10f)]),jQuery(document[_0x2fe2ed(0x11b)])['on'](_0x2fe2ed(0x119),this['_handlers'][_0x2fe2ed(0x11e)])):(document[_0x2fe2ed(0x11b)][_0x2fe2ed(0x120)](_0x2fe2ed(0x10a),this[_0x2fe2ed(0x109)][_0x2fe2ed(0x10f)]),document[_0x2fe2ed(0x11b)]['addEventListener'](_0x2fe2ed(0x119),this[_0x2fe2ed(0x109)][_0x2fe2ed(0x11e)]));}['_detachEvents'](){const _0x4af07a=_0x4b6007;window[_0x4af07a(0x10e)]?(jQuery(document[_0x4af07a(0x11b)])[_0x4af07a(0x10c)](_0x4af07a(0x10a),this[_0x4af07a(0x109)][_0x4af07a(0x10f)]),jQuery(document[_0x4af07a(0x11b)])[_0x4af07a(0x10c)](_0x4af07a(0x119),this[_0x4af07a(0x109)][_0x4af07a(0x11e)])):(document[_0x4af07a(0x11b)]['removeEventListener'](_0x4af07a(0x10a),this[_0x4af07a(0x109)][_0x4af07a(0x10f)]),document['body'][_0x4af07a(0x106)](_0x4af07a(0x119),this[_0x4af07a(0x109)][_0x4af07a(0x11e)]));}['_onAddingToCart'](){const _0x367c4e=_0x4b6007;this['_startFetchTimer'](),this['element'][_0x367c4e(0x123)]['add'](this[_0x367c4e(0x102)][_0x367c4e(0x115)]);}[_0x4b6007(0x116)](){const _0x454c5c=_0x4b6007,_0x332005=this[_0x454c5c(0x113)](),_0x3ace60=_0x332005<this['options']['fetchAfterDelay']?this[_0x454c5c(0x102)][_0x454c5c(0x100)]-_0x332005:0x0;setTimeout(()=>{const _0x3809c3=_0x454c5c;this['element'][_0x3809c3(0x123)]['remove'](this['options'][_0x3809c3(0x115)]);},_0x3ace60);}}