const _0x5a6ddf=_0x5c83;function _0x5c83(_0x3cb19a,_0xd01533){const _0x4818c0=_0x4818();return _0x5c83=function(_0x5c8383,_0x5e2a99){_0x5c8383=_0x5c8383-0x10d;let _0x45c030=_0x4818c0[_0x5c8383];return _0x45c030;},_0x5c83(_0x3cb19a,_0xd01533);}function _0x4818(){const _0x2929a1=['mqChange','arts/header/overlay/beforeOpen','_onTransitionStart','animateLines','.js-header__bar','100%','infiniteList','8AQqcRX','assign','MenuOverlay','push','topLevelMenu','timeScaleOpen','fromTo','2238186FFAJmf','enable','reverse','disable','progress','options','_onOverlayClose','lockReveal','innerSelectors','getAttribute','.js-header__overlay-widget','toggleStickyClass','addAJAXStartEventListener','disableScroll','1114026IzGfdz','matches','lockedSticky','set','overlay','_createHeader','hideLines','overlaySwitcher','enableScroll','overlayContainer','opened','removeMediaQueryListener','_onOverlayOpen','top','transitionStart','hideCurtain','--header-height','scrollLock','container','25ZKrIkl','isEnabledOption','bind','arts/header/overlay/afterOpen','_getStickyOptions','element','dataReady','1642830wNXLUh','1962496TTqgZV','timeScaleClose','MenuClassic','end','scrollTo','elements','toggle','1458881MpKWjV','menuOverlayRef','dispatchEvent','.js-header__overlay-switcher','instance','matchMediaAutoCloseOverlay','addMediaQueryListener','-103%','arts/container/visibility','add','timeline','utilities','_attachEvents','all','currentSubmenuRef','current','postTask','4086880CmJdPf','setup','observeHeight','remove','490494SrXijJ','_onMQChange','_setSubmenuLabelNext','widgets','overlayWasOpened','.menu-overlay','destroy','_shouldReopenMenu','animating','_scrollContainerInnerTop',':scope\x20>\x20li\x20>\x20a','start','moveSubmenuToHolder','_onMenuBeforeOpen','<50%','.js-header__overlay-container-inner','forEach','header_hidden-not-opened','_handlers','_onMenuClose','classList','visible','_getInnerComponentByName','.menu-overlay\x20.sub-menu','contains','menuClassicRef','header__bar_scrolling-down','restoreSubmenuOriginalPlacement','.js-header__overlay-submenu-back','menu','.js-header__overlay-container','(min-width:\x20992px)','arts/header/overlay/afterClose','visibility,height','submenuLabelCurrent','arts/header/overlay/beforeClose','-100%','opacity,visibility','timeScale','hidden','power4.out','update','_detachEvents','header__bar_locked-reveal','bar','submenuLabelNext','_onHeaderAnchorsClick','header__bar_sticky','sticky','_setSubmenuLabelCurrent','currentMenuIsTopLevel','scrollListToCurrentTopLevelItem','9KoCvue','content','aria-label','app','toggleOverlay','overlayContainerInner','isOverlayOpening','opened-submenu','data-arts-header-sticky-class','data-arts-header-overlay-color-theme','enabled','innerHTML','toggleScrollingDownClass','init','finally'];_0x4818=function(){return _0x2929a1;};return _0x4818();}(function(_0x433aff,_0x5af14c){const _0x316c39=_0x5c83,_0x524fb8=_0x433aff();while(!![]){try{const _0x5beb4f=parseInt(_0x316c39(0x177))/0x1+-parseInt(_0x316c39(0x147))/0x2+-parseInt(_0x316c39(0x18c))/0x3*(parseInt(_0x316c39(0x140))/0x4)+-parseInt(_0x316c39(0x168))/0x5*(-parseInt(_0x316c39(0x155))/0x6)+-parseInt(_0x316c39(0x188))/0x7+parseInt(_0x316c39(0x170))/0x8+parseInt(_0x316c39(0x12a))/0x9*(parseInt(_0x316c39(0x16f))/0xa);if(_0x5beb4f===_0x5af14c)break;else _0x524fb8['push'](_0x524fb8['shift']());}catch(_0x26a993){_0x524fb8['push'](_0x524fb8['shift']());}}}(_0x4818,0xbb3b6));export default class Header extends BaseComponent{constructor({name:_0x1d34f2,loadInnerComponents:_0x52a7cd,loadAfterSyncStyles:_0x3e6c23,parent:_0x3a6016,element:_0x1a87ea}){const _0x8db011=_0x5c83;super({'name':_0x1d34f2,'loadInnerComponents':_0x52a7cd,'loadAfterSyncStyles':_0x3e6c23,'parent':_0x3a6016,'element':_0x1a87ea,'defaults':{'sticky':{'toggleAttributes':{'data-arts-header-logo':'data-arts-header-sticky-logo','data-arts-color-theme':'data-arts-header-sticky-color-theme','class':_0x8db011(0x132)},'toggleReveal':!![],'toggleStickyClass':_0x8db011(0x125),'toggleRevealingClass':![],'toggleScrollingDownClass':_0x8db011(0x110)},'observeHeight':!![],'matchMediaAutoCloseOverlay':_0x8db011(0x115),'timeScaleOpen':0x1,'timeScaleClose':0x1},'innerElements':{'bar':_0x8db011(0x13d),'overlayContainer':_0x8db011(0x114),'overlayContainerInner':_0x8db011(0x19b),'overlaySwitcher':_0x8db011(0x17a),'topLevelMenu':_0x8db011(0x191),'allSubMenus':_0x8db011(0x10d),'submenuBackButton':_0x8db011(0x112),'submenuLabelCurrent':'.js-header__overlay-label-opened-current','submenuLabelNext':'.js-header__overlay-label-opened-next','widgets':_0x8db011(0x151)}}),this[_0x8db011(0x19e)]={'mqChange':this[_0x8db011(0x18d)][_0x8db011(0x16a)](this),'transitionStart':this[_0x8db011(0x13b)][_0x8db011(0x16a)](this)},this[_0x8db011(0x16e)]['finally'](()=>{const _0x3e7792=_0x8db011;this[_0x3e7792(0x157)]=![],this[_0x3e7792(0x130)]=![],this['mq']=window['matchMedia'](this['options'][_0x3e7792(0x17c)]),this[_0x3e7792(0x189)]();});}[_0x5a6ddf(0x137)](){return new Promise(_0x455418=>{const _0x2dfbd1=_0x5c83;this['menuOverlayRef']=this['_getInnerComponentByName'](_0x2dfbd1(0x142)),this[_0x2dfbd1(0x10f)]=this[_0x2dfbd1(0x1a2)](_0x2dfbd1(0x172)),this[_0x2dfbd1(0x15a)](),this[_0x2dfbd1(0x17b)]?this[_0x2dfbd1(0x17b)]['ready'][_0x2dfbd1(0x138)](()=>{const _0x11c690=_0x2dfbd1;this[_0x11c690(0x183)](),(!!this[_0x11c690(0x14c)]['forceOpen']||this[_0x11c690(0x193)]())&&(this[_0x11c690(0x17b)][_0x11c690(0x12e)](!![],!![]),this['instance'][_0x11c690(0x113)]&&this[_0x11c690(0x17b)][_0x11c690(0x113)][_0x11c690(0x181)][_0x11c690(0x14b)](0.98),this[_0x11c690(0x17b)]['overlay']&&this[_0x11c690(0x17b)][_0x11c690(0x159)]['timeline'][_0x11c690(0x14b)](0.98),this[_0x11c690(0x178)]&&this[_0x11c690(0x178)][_0x11c690(0x129)]()),_0x455418(!![]);}):_0x455418(!![]);});}[_0x5a6ddf(0x193)](){const _0x316b83=_0x5a6ddf;if(!!window[_0x316b83(0x12d)][_0x316b83(0x190)]&&this[_0x316b83(0x17b)])return this[_0x316b83(0x178)]&&this[_0x316b83(0x10f)]?!this['mq']['matches']:!![];return![];}[_0x5a6ddf(0x192)](){return new Promise(_0x3ae68e=>{const _0x57b740=_0x5c83,_0x25e005=[];this[_0x57b740(0x120)]();if(this['instance']){Object[_0x57b740(0x141)](app,{'overlayWasOpened':this[_0x57b740(0x17b)]['opened']});const _0x168b25=new Promise(_0x48bd23=>{const _0x1ab483=_0x57b740;this[_0x1ab483(0x17b)][_0x1ab483(0x192)]()['finally'](()=>{const _0x582bc3=_0x1ab483;this['instance']&&(this[_0x582bc3(0x17b)]['overlay']&&this['instance']['overlay'][_0x582bc3(0x181)]['kill'](),this[_0x582bc3(0x17b)][_0x582bc3(0x113)]&&this[_0x582bc3(0x17b)][_0x582bc3(0x113)][_0x582bc3(0x181)]['kill']()),this[_0x582bc3(0x17b)]=undefined,this[_0x582bc3(0x157)]=![],this[_0x582bc3(0x130)]=![],_0x48bd23(!![]);});});_0x25e005[_0x57b740(0x143)](_0x168b25);}else this[_0x57b740(0x157)]=![],this['isOverlayOpening']=![];Promise['all'](_0x25e005)[_0x57b740(0x138)](()=>_0x3ae68e(!![]));});}[_0x5a6ddf(0x11f)](){}['toggleHidden'](_0x9baac6=![]){const _0x5d2630=_0x5a6ddf;this[_0x5d2630(0x16d)]['classList']['toggle'](_0x5d2630(0x19d),_0x9baac6);}['lockSticky'](_0x257d50=![]){const _0x17d687=_0x5a6ddf;if(!this[_0x17d687(0x17b)]||!this[_0x17d687(0x17b)][_0x17d687(0x126)]||!this[_0x17d687(0x175)][_0x17d687(0x122)][0x0])return;this[_0x17d687(0x157)]=_0x257d50;if(!!_0x257d50){const _0x4a12a7=this[_0x17d687(0x16d)]['getAttribute'](_0x17d687(0x132));_0x4a12a7&&this[_0x17d687(0x175)][_0x17d687(0x122)][0x0][_0x17d687(0x1a0)][_0x17d687(0x18b)](_0x4a12a7),this['instance']['sticky'][_0x17d687(0x14a)]();}else this[_0x17d687(0x17b)]['sticky']['enable'](),this[_0x17d687(0x17b)]['update']();this[_0x17d687(0x17b)]['sticky']['locked']=_0x257d50;}[_0x5a6ddf(0x14e)](_0x3260ff=![],{ease:_0x5cc789,duration:_0x2bf29a}={'ease':'expo.inOut','duration':0.8}){const _0x355a89=_0x5a6ddf;if(!this[_0x355a89(0x17b)]||this[_0x355a89(0x17b)][_0x355a89(0x15f)]||!this[_0x355a89(0x17b)][_0x355a89(0x126)]||!this[_0x355a89(0x175)][_0x355a89(0x122)][0x0])return;this['lockedSticky']=_0x3260ff;const _0x1a428c=this['elements'][_0x355a89(0x122)][0x0]['classList'][_0x355a89(0x10e)](this[_0x355a89(0x14c)]['sticky'][_0x355a89(0x136)]),_0x5ae432=this[_0x355a89(0x175)][_0x355a89(0x122)][0x0][_0x355a89(0x1a0)]['contains'](this[_0x355a89(0x14c)][_0x355a89(0x126)]['toggleStickyClass']);this['elements'][_0x355a89(0x122)][0x0][_0x355a89(0x1a0)][_0x355a89(0x176)](_0x355a89(0x121),_0x3260ff),!!_0x3260ff?(this[_0x355a89(0x17b)][_0x355a89(0x126)]['disable'](),_0x1a428c&&this['elements'][_0x355a89(0x122)][0x0][_0x355a89(0x1a0)][_0x355a89(0x180)](this[_0x355a89(0x14c)]['sticky']['toggleScrollingDownClass']),_0x5ae432&&this[_0x355a89(0x175)]['bar'][0x0][_0x355a89(0x1a0)][_0x355a89(0x180)](this[_0x355a89(0x14c)]['sticky'][_0x355a89(0x152)]),gsap['to'](this[_0x355a89(0x175)][_0x355a89(0x122)][0x0],{'--translateY':_0x355a89(0x11a),'ease':_0x5cc789,'duration':_0x2bf29a})):this[_0x355a89(0x17b)][_0x355a89(0x126)][_0x355a89(0x148)](),this[_0x355a89(0x17b)][_0x355a89(0x126)]['locked']=_0x3260ff;}[_0x5a6ddf(0x16c)](){const _0x5ac21f=_0x5a6ddf;return app['utilities'][_0x5ac21f(0x169)](this[_0x5ac21f(0x14c)][_0x5ac21f(0x126)])?{'containerSelector':this[_0x5ac21f(0x14f)][_0x5ac21f(0x122)],...this['options'][_0x5ac21f(0x126)]}:![];}[_0x5a6ddf(0x15a)](){const _0x24a9af=_0x5a6ddf;this[_0x24a9af(0x17b)]=new ArtsHeader(this[_0x24a9af(0x16d)],{'init':!![],'matchMedia':![],'sticky':this[_0x24a9af(0x16c)](),'overlay':{'containerSelector':this[_0x24a9af(0x14f)][_0x24a9af(0x15e)],'containerSelectorStickyFallback':this[_0x24a9af(0x14f)][_0x24a9af(0x122)],'toggleAttributes':{'data-arts-color-theme':_0x24a9af(0x133)},'toggleOpenedClass':'opened','toggleAnimatingClass':_0x24a9af(0x194),'onOpen':this[_0x24a9af(0x161)][_0x24a9af(0x16a)](this),'onClose':this[_0x24a9af(0x14d)][_0x24a9af(0x16a)](this)},'switcher':{'elementSelector':this[_0x24a9af(0x14f)][_0x24a9af(0x15c)],'toggleActiveClass':'header__burger_opened'},'menu':{'menuSelector':this[_0x24a9af(0x14f)][_0x24a9af(0x144)],'submenuBackButtonSelector':this['innerSelectors']['submenuBackButton'],'toggleCurrentMenuClass':_0x24a9af(0x186),'toggleSubmenuOpenedClass':_0x24a9af(0x131),'toggleOpeningClass':_0x24a9af(0x194),'beforeOpen':this[_0x24a9af(0x199)][_0x24a9af(0x16a)](this),'onOpen':this['_onMenuOpen'][_0x24a9af(0x16a)](this),'onClose':this[_0x24a9af(0x19f)][_0x24a9af(0x16a)](this)},'anchors':{'autoCloseOverlay':!![],'onClick':this[_0x24a9af(0x124)][_0x24a9af(0x16a)](this)},'heightObserver':{'containerSelector':this[_0x24a9af(0x14f)][_0x24a9af(0x122)],'updateCSSVar':_0x24a9af(0x165),'observe':this['options'][_0x24a9af(0x18a)],'cleanupOnDestroy':!!app[_0x24a9af(0x14c)]['isElementorEditor']?!![]:![]}});}[_0x5a6ddf(0x161)](){const _0x382f6e=_0x5a6ddf,_0x429b35=app['elements'][_0x382f6e(0x12b)]&&app[_0x382f6e(0x175)][_0x382f6e(0x12b)][_0x382f6e(0x10e)](this[_0x382f6e(0x16d)])?![]:!![],_0x114e43=this[_0x382f6e(0x14c)]['timeScaleOpen'],_0x532053=gsap[_0x382f6e(0x181)]({'onStart':()=>{const _0x4fc2b6=_0x382f6e;this['menuOverlayRef']&&(this[_0x4fc2b6(0x178)][_0x4fc2b6(0x185)]=null,this['menuOverlayRef']['currentSubmenuParentRef']=null),this[_0x4fc2b6(0x130)]=!![];},'onComplete':()=>{this['isOverlayOpening']=![];}})[_0x382f6e(0x158)](this[_0x382f6e(0x175)]['allSubMenus'],{'visibility':_0x382f6e(0x11d)})[_0x382f6e(0x158)](this['elements'][_0x382f6e(0x15e)],{'autoAlpha':0x1,'zIndex':0x190})['animateCurtain'](this[_0x382f6e(0x175)][_0x382f6e(0x15e)],{'animateFrom':_0x382f6e(0x162),'duration':1.2,'onStart':()=>{const _0x5f1729=_0x382f6e;app[_0x5f1729(0x182)][_0x5f1729(0x179)](_0x5f1729(0x13a));},'onComplete':()=>{const _0xd0471a=_0x382f6e;app[_0xd0471a(0x182)][_0xd0471a(0x179)](_0xd0471a(0x16b));}})['to'](app[_0x382f6e(0x175)][_0x382f6e(0x12b)],{'autoAlpha':_0x429b35?0x0:0x1,'onStart':()=>{const _0x512df8=_0x382f6e;app[_0x512df8(0x182)][_0x512df8(0x179)](_0x512df8(0x17f),{'detail':{'container':app[_0x512df8(0x175)][_0x512df8(0x167)],'visible':_0x429b35?![]:!![]}}),app[_0x512df8(0x182)][_0x512df8(0x166)](!![]);},'duration':0.3},_0x382f6e(0x19a))[_0x382f6e(0x146)](this[_0x382f6e(0x175)][_0x382f6e(0x18f)],{'autoAlpha':0x0,'y':_0x382f6e(0x13e),'immediateRender':!![]},{'autoAlpha':0x1,'y':'0%','duration':1.2,'ease':'power4.out','stagger':{'amount':0.1,'from':_0x382f6e(0x197),'axis':'y'}},'<25%');return _0x532053['timeScale'](_0x114e43),_0x532053;}[_0x5a6ddf(0x14d)](){const _0x1c4026=_0x5a6ddf,_0x3edff0=this['options']['timeScaleClose'],_0x2802e2=gsap['timeline']({'onComplete':()=>{const _0x5daef6=_0x5c83;this[_0x5daef6(0x178)]&&(this['menuOverlayRef'][_0x5daef6(0x154)](),this[_0x5daef6(0x178)][_0x5daef6(0x111)]());}})['to'](this['elements'][_0x1c4026(0x18f)],{'autoAlpha':0x0,'y':_0x1c4026(0x13e),'duration':1.2,'ease':_0x1c4026(0x11e),'stagger':{'amount':0.1,'from':_0x1c4026(0x173),'axis':'y'}})[_0x1c4026(0x164)](this[_0x1c4026(0x175)][_0x1c4026(0x15e)],{'animateTo':_0x1c4026(0x162),'duration':1.2,'clearProps':_0x1c4026(0x184),'onStart':()=>{const _0x12e155=_0x1c4026;app[_0x12e155(0x182)][_0x12e155(0x179)](_0x12e155(0x119)),app[_0x12e155(0x182)][_0x12e155(0x166)](![]);},'onComplete':()=>{const _0x42fa63=_0x1c4026;app['utilities'][_0x42fa63(0x179)](_0x42fa63(0x116));}},'<')['to'](app[_0x1c4026(0x175)][_0x1c4026(0x12b)],{'autoAlpha':0x1,'duration':0.3,'onComplete':()=>{const _0x55ea51=_0x1c4026;gsap[_0x55ea51(0x158)](app[_0x55ea51(0x175)][_0x55ea51(0x12b)],{'clearProps':_0x55ea51(0x11b)}),app['utilities'][_0x55ea51(0x179)](_0x55ea51(0x17f),{'detail':{'container':app[_0x55ea51(0x175)][_0x55ea51(0x167)],'visible':!![]}});}},'<');return _0x2802e2[_0x1c4026(0x11c)](_0x3edff0),_0x2802e2;}['_onMenuBeforeOpen'](){this['_scrollContainerInnerTop']({'duration':0x0});}[_0x5a6ddf(0x195)]({target:target=0x0,duration:duration=0.5,container:container=this['elements'][_0x5a6ddf(0x12f)][0x0]}={}){const _0x26f5c5=_0x5a6ddf;if(this['elements'][_0x26f5c5(0x12f)][0x0]&&this[_0x26f5c5(0x175)][_0x26f5c5(0x12f)][0x0]['scrollTop']>0x0)return app[_0x26f5c5(0x182)][_0x26f5c5(0x174)]({'target':target,'duration':duration,'container':container});}['_onMenuOpen'](_0x238218,_0x35168e){const _0x50a956=_0x5a6ddf;if(!_0x238218)return;this[_0x50a956(0x130)]=!![];const _0x5587b1=_0x238218===_0x35168e,_0x576cbd=this[_0x50a956(0x14c)][_0x50a956(0x145)],_0x25a79e=_0x238218[_0x50a956(0x150)](_0x50a956(0x12c)),_0x1f9d88=this['instance'][_0x50a956(0x128)],_0x435f7d=[..._0x238218['querySelectorAll'](_0x50a956(0x196))],_0x4ad00b=gsap[_0x50a956(0x181)]({'onStart':()=>{const _0xd690b2=_0x50a956;this[_0xd690b2(0x178)]&&this[_0xd690b2(0x178)][_0xd690b2(0x13f)]&&this[_0xd690b2(0x178)][_0xd690b2(0x13f)][_0xd690b2(0x134)]&&(_0x1f9d88?(this[_0xd690b2(0x178)]['infiniteList'][_0xd690b2(0x11f)](),this[_0xd690b2(0x178)][_0xd690b2(0x15d)]()):this['menuOverlayRef']['disableScroll']()),_0x25a79e&&this[_0xd690b2(0x18e)](_0x25a79e);}});return _0x435f7d[_0x50a956(0x19c)]((_0x1bf783,_0x39b858)=>{const _0x2e84d6=_0x50a956;_0x4ad00b[_0x2e84d6(0x15b)](_0x1bf783,{'duration':0x0,'y':_0x2e84d6(0x17e)},_0x2e84d6(0x197));}),_0x4ad00b[_0x50a956(0x158)](_0x238218,{'visibility':_0x50a956(0x1a1),'onComplete':()=>{this['_scrollContainerInnerTop']();}}),!_0x1f9d88&&_0x435f7d[_0x50a956(0x149)](),_0x435f7d['forEach']((_0x326f21,_0x2cd40f)=>{const _0x42b711=_0x50a956;let _0x361c53='<';_0x2cd40f===0x0&&_0x5587b1&&(_0x361c53=0.6),_0x4ad00b[_0x42b711(0x13c)](_0x326f21,{'duration':1.2/_0x576cbd,'ease':_0x42b711(0x11e)},_0x361c53),_0x2cd40f===0x0&&this[_0x42b711(0x178)]&&this[_0x42b711(0x130)]&&_0x4ad00b['set']({},{'onComplete':()=>{const _0x684dbe=_0x42b711;this[_0x684dbe(0x178)][_0x684dbe(0x129)]();}},'<');}),_0x4ad00b[_0x50a956(0x146)](this[_0x50a956(0x175)]['submenuLabelCurrent'],{'y':0x0,'autoAlpha':0x1},{'y':-0x1e,'autoAlpha':0x0,'duration':0.3,'onComplete':()=>{const _0x12450e=_0x50a956;_0x1f9d88?this['_setSubmenuLabelCurrent']():this[_0x12450e(0x127)](_0x25a79e);}},'<')[_0x50a956(0x146)](this[_0x50a956(0x175)][_0x50a956(0x123)],{'y':0x1e,'autoAlpha':0x0},{'y':0x0,'autoAlpha':0x1,'duration':0.3,'onStart':()=>{_0x25a79e&&this['_setSubmenuLabelNext'](_0x25a79e);}},'<'),_0x4ad00b[_0x50a956(0x11c)](_0x576cbd),_0x4ad00b;}[_0x5a6ddf(0x127)](_0x53392f=''){return new Promise(_0x17ba0e=>{const _0x4c4c07=_0x5c83;this['elements'][_0x4c4c07(0x118)][0x0]?scheduler['postTask'](()=>{const _0x4e2530=_0x4c4c07;this['elements']['submenuLabelCurrent'][0x0][_0x4e2530(0x135)]=_0x53392f;})[_0x4c4c07(0x138)](()=>_0x17ba0e(!![])):_0x17ba0e(!![]);});}[_0x5a6ddf(0x18e)](_0x1efe0a=''){return new Promise(_0x17d7c8=>{const _0xd01966=_0x5c83;this['elements'][_0xd01966(0x123)][0x0]?scheduler['postTask'](()=>{const _0x4a0cdb=_0xd01966;this[_0x4a0cdb(0x175)]['submenuLabelNext'][0x0][_0x4a0cdb(0x135)]=_0x1efe0a;})[_0xd01966(0x138)](()=>_0x17d7c8(!![])):_0x17d7c8(!![]);});}[_0x5a6ddf(0x19f)](_0x2e6ef8,_0x1321cd){const _0x41ecc4=_0x5a6ddf;if(!_0x1321cd)return;const _0xf965c4=this[_0x41ecc4(0x14c)][_0x41ecc4(0x171)],_0x2d8031=gsap[_0x41ecc4(0x181)]({'onStart':()=>{const _0x5a607e=_0x41ecc4;_0x1321cd===this['elements'][_0x5a607e(0x144)][0x0]&&_0x2e6ef8!==this[_0x5a607e(0x175)][_0x5a607e(0x144)][0x0]&&(this[_0x5a607e(0x178)]&&this[_0x5a607e(0x178)][_0x5a607e(0x198)](_0x2e6ef8));},'onComplete':()=>{const _0x47c008=_0x41ecc4;this[_0x47c008(0x17b)]['opened']&&_0xfb2c12&&(this[_0x47c008(0x178)]&&this[_0x47c008(0x178)][_0x47c008(0x111)]());}}),_0xfb2c12=this[_0x41ecc4(0x17b)][_0x41ecc4(0x128)],_0xd3041b=[..._0x1321cd['querySelectorAll'](_0x41ecc4(0x196))];return _0xd3041b[_0x41ecc4(0x19c)]((_0x233790,_0x7b063a)=>{const _0x4b6fcd=_0x41ecc4;_0x2d8031['hideLines'](_0x233790,{'duration':1.2/_0xf965c4,'ease':_0x4b6fcd(0x11e)},_0x4b6fcd(0x197));}),_0x2d8031[_0x41ecc4(0x158)](_0x1321cd,{'clearProps':_0x41ecc4(0x117)}),_0x2d8031[_0x41ecc4(0x11c)](_0xf965c4),_0x2d8031;}[_0x5a6ddf(0x124)](_0x443409){const _0x3fd1de=_0x5a6ddf;app[_0x3fd1de(0x182)][_0x3fd1de(0x174)]({'target':_0x443409});}[_0x5a6ddf(0x183)](){const _0x22c83c=_0x5a6ddf;app['utilities'][_0x22c83c(0x17d)](this['mq'],this[_0x22c83c(0x19e)][_0x22c83c(0x139)]),app[_0x22c83c(0x182)][_0x22c83c(0x153)](this['_handlers'][_0x22c83c(0x163)]);}[_0x5a6ddf(0x120)](){const _0x3075f1=_0x5a6ddf;app[_0x3075f1(0x182)][_0x3075f1(0x160)](this['mq'],this[_0x3075f1(0x19e)][_0x3075f1(0x139)]);}['_onMQChange'](_0x1a76fa){const _0x3037f5=_0x5a6ddf;_0x1a76fa[_0x3037f5(0x156)]&&this['menuOverlayRef']&&this[_0x3037f5(0x10f)]&&this[_0x3037f5(0x17b)][_0x3037f5(0x15f)]&&this['instance'][_0x3037f5(0x12e)](![],!![]);}[_0x5a6ddf(0x13b)](){return new Promise(_0x6bf1d5=>{const _0xf8da87=_0x5c83;if(this['instance']&&this[_0xf8da87(0x17b)][_0xf8da87(0x126)]&&this[_0xf8da87(0x175)][_0xf8da87(0x122)][0x0]){const _0x1cb4f0=this[_0xf8da87(0x16d)][_0xf8da87(0x150)]('data-arts-header-sticky-class');_0x1cb4f0?scheduler['postTask'](()=>{const _0x20c5f2=_0xf8da87;this[_0x20c5f2(0x175)]['bar'][0x0][_0x20c5f2(0x1a0)]['remove'](_0x1cb4f0);})['finally'](()=>{const _0x5bf3c4=_0xf8da87;this[_0x5bf3c4(0x17b)][_0x5bf3c4(0x126)][_0x5bf3c4(0x14a)](),_0x6bf1d5(!![]);}):scheduler[_0xf8da87(0x187)](()=>{const _0x3c96ac=_0xf8da87;this[_0x3c96ac(0x17b)][_0x3c96ac(0x126)][_0x3c96ac(0x14a)](),_0x6bf1d5(!![]);});}else _0x6bf1d5(!![]);});}}