function distributeByPosition(e){var t,n=e.ease&&gsap.parseEase(e.ease),s=e.from||0,i=e.base||0,a=e.axis,r={center:.5,end:1,edges:.5}[s]||0;return function(o,l,c){var p,h,u,d,m,f,g,y,b,_,v,w=c.length;if(!t){for(t=[],y=_=-(g=b=1/0),v=[],f=0;f<w;f++)(u=((m=c[f].getBoundingClientRect()).left+m.right)/2)<g&&(g=u),u>y&&(y=u),(d=(m.top+m.bottom)/2)<b&&(b=d),d>_&&(_=d),v[f]={x:u,y:d};for(p=isNaN(s)?g+(y-g)*r:v[s].x||0,h=isNaN(s)?b+(_-b)*r:v[s].y||0,y=0,g=1/0,f=0;f<w;f++)u=v[f].x-p,d=h-v[f].y,t[f]=m=a?Math.abs("y"===a?d:u):Math.sqrt(u*u+d*d),m>y&&(y=m),m<g&&(g=m);t.max=y-g,t.min=g,t.v=w=(e.amount||e.each*w||0)*("edges"===s?-1:1),t.b=w<0?i-w:i}return w=(t[o]-t.min)/t.max,t.b+(n?n(w):w)*t.v}}!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).deepmerge=t()}(this,(function(){"use strict";var e=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var n=Object.prototype.toString.call(e);return"[object RegExp]"===n||"[object Date]"===n||function(e){return e.$$typeof===t}(e)}(e)},t="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?o((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function s(e,t,s){return e.concat(t).map((function(e){return n(e,s)}))}function i(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return e.propertyIsEnumerable(t)})):[]}(e))}function a(e,t){try{return t in e}catch(e){return!1}}function r(e,t,s){var r={};return s.isMergeableObject(e)&&i(e).forEach((function(t){r[t]=n(e[t],s)})),i(t).forEach((function(i){(function(e,t){return a(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,i)||(a(e,i)&&s.isMergeableObject(t[i])?r[i]=function(e,t){if(!t.customMerge)return o;var n=t.customMerge(e);return"function"==typeof n?n:o}(i,s)(e[i],t[i],s):r[i]=n(t[i],s))})),r}function o(t,i,a){(a=a||{}).arrayMerge=a.arrayMerge||s,a.isMergeableObject=a.isMergeableObject||e,a.cloneUnlessOtherwiseSpecified=n;var o=Array.isArray(i);return o===Array.isArray(t)?o?a.arrayMerge(t,i,a):r(t,i,a):n(i,a)}return o.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return o(e,n,t)}),{})},o}));class Animations{constructor({duration:e}={duration:1.2}){this.defaults={duration:e},this.splitTextPresets={sliderLines:{init:!0,type:"lines",wrap:"lines",set:!1},sliderChars:{type:"lines,chars",wrap:"chars",set:!1},counterChars:{init:!0,type:"chars",set:!1,wrap:!1},animatedLines:{init:!0,type:"lines",set:{type:"lines",direction:"y",distance:"103%",opacity:!1},wrap:"lines",wrapClass:"overflow-hidden"},overlayMenuItem:{init:!0,type:"lines",set:{type:"lines",direction:"y",distance:"-103%",opacity:!1},wrap:"lines",wrapClass:"overflow-hidden"},animatedChars:{init:!0,type:"lines,words,chars",set:{type:"chars",direction:"y",distance:"103%",opacity:!1},wrap:"lines",wrapClass:"overflow-hidden"},animatedCharsRandom:{init:!0,type:"lines,words,chars",set:{type:"chars",direction:"y",distance:"103%",opacity:1},wrap:"lines",wrapClass:"overflow-hidden"},animatedCounterChars:{init:!0,type:"chars",set:{type:"chars",direction:"y",distance:"40%",opacity:0},wrap:!1}},this._animateTranslate(),this._animateScale(),this._animateMask(),this._hideMask(),this._animateCurtain(),this._hideCurtain()}_animateTranslate(){gsap.registerEffect({name:"animateTranslate",effect:(e,t)=>{const n=gsap.timeline();if(e&&e[0]){let s={},i={xPercent:0,yPercent:0,duration:t.duration,ease:t.ease,stagger:t.stagger};switch(t.animateFrom){case"top":s.yPercent=-100;break;case"right":s.xPercent=100;break;case"left":s.xPercent=-100;break;case"bottom":s.yPercent=100}"number"==typeof t.xPercent&&(s.xPercent=t.xPercent),"number"==typeof t.yPercent&&(s.yPercent=t.yPercent),t.animateOpacity&&(s.opacity=0,i.opacity=1),gsap.set(e,s),n.to(e,i),t.clearProps&&t.clearProps.length&&n.set(e,{clearProps:t.clearProps})}return n},defaults:{animateFrom:"bottom",animateOpacity:!1,animateSkew:!1,xPercent:!1,yPercent:!1,duration:1.2,ease:"power4.out",stagger:0,transformOrigin:"center center",clearProps:"transform,transformOrigin,opacity"},extendTimeline:!0})}_animateScale(){gsap.registerEffect({name:"animateScale",effect:(e,t)=>{const n=gsap.timeline();if(e&&e[0]){let s={duration:t.duration,ease:t.ease,stagger:t.stagger};switch(t.animateFrom){case"top":s.transformOrigin="center top",s.scaleY=1;break;case"right":s.transformOrigin="right center",s.scaleX=1;break;case"left":s.transformOrigin="left center",s.scaleX=1;break;case"bottom":s.transformOrigin="center bottom",s.scaleY=1;break;case"center":s.transformOrigin="center center",s.scale=1}"number"==typeof t.scaleX&&(s.scaleX=1),"number"==typeof t.scaleY&&(s.scaleY=1),"number"==typeof t.scale&&(s.scale=1),n.to(e,s),t.clearProps&&t.clearProps.length&&n.set(e,{clearProps:t.clearProps})}return n},defaults:{scaleX:!1,scaleY:!1,scale:!1,animateFrom:"center",duration:.6,ease:"power4.out",clearProps:"transform"},extendTimeline:!0})}_animateMask(){gsap.registerEffect({name:"animateMask",effect:(e,t)=>{const n=gsap.timeline({onStart:t.onStart,onComplete:t.onComplete});if(e&&e[0]){let s,i;if("circle"===t.shape)s="circle(0% at 50% 50%)";else if("ellipse"===t.shape)s="ellipse(50% 0% at 50% 50%)";else switch(t.animateFrom){case"top":s="polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)";break;case"right":s="polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)";break;case"left":s="polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)";break;case"center":s="inset(50%)";break;default:s="polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)"}t.clipPathFrom&&gsap.set(e,{clipPath:s,overwrite:t.overwrite}),"string"==typeof t.scaleInner&&(i=e[0].querySelector(t.scaleInner)),i&&t.scale&&gsap.set(i,{transformOrigin:"center center",scale:t.scale});let a="polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)";"center"===t.animateFrom&&(a="inset(0%)"),"circle"===t.shape?a="circle(100% at 50% 50%)":"ellipse"===t.shape&&(a="ellipse(100% 100% at 50% 50%)"),n.to(e,{x:t.x,y:t.y,clipPath:a,duration:t.duration,ease:t.ease,stagger:t.stagger}),i&&t.scale&&n.to(i,{scale:1,duration:1.25*t.duration,ease:t.ease},"<"),t.clearProps&&t.clearProps.length&&(n.set(e,{clearProps:t.clearProps}),i&&t.scale&&n.set(i,{clearProps:"transform"}))}return n},defaults:{x:void 0,y:void 0,shape:"rectangle",duration:this.defaults.duration,scale:1.05,scaleInner:"img,video",ease:"expo.inOut",stagger:0,animateFrom:"bottom",clearProps:"clipPath",clipPathFrom:!0,overwrite:!0},extendTimeline:!0})}_hideMask(){gsap.registerEffect({name:"hideMask",effect:(e,t)=>{const n=gsap.timeline({onStart:t.onStart,onComplete:t.onComplete});if(e&&e[0]){switch(t.animateTo){case"top":t.clipPath="polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)";break;case"right":t.clipPath="polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)";break;case"left":t.clipPath="polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)";break;case"center":t.clipPath="inset(50%)";break;default:t.clipPath="polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)"}let s;t.clipPathFrom&&gsap.set(e,{clipPath:t.clipPathFrom,overwrite:t.overwrite}),"string"==typeof t.scaleInner&&(s=e[0].querySelector(t.scaleInner)),"number"==typeof t.duration&&t.duration>0?(n.to(e,{x:t.x,y:t.y,clipPath:t.clipPath,duration:t.duration,ease:t.ease,stagger:t.stagger}),s&&t.scale&&n.to(s,{scale:t.scale,duration:t.duration,ease:t.ease},"<")):(n.set(e,{x:t.x,y:t.y,clipPath:t.clipPath}),s&&t.scale&&n.set(s,{scale:t.scale},"<")),t.clearProps&&t.clearProps.length&&(n.set(e,{clearProps:t.clearProps}),s&&t.scale&&n.set(s,{clearProps:"transform"}))}return n},defaults:{x:void 0,y:void 0,duration:this.defaults.duration,scale:1.1,scaleInner:"img,video",ease:"expo.inOut",animateTo:"bottom",clearProps:"clipPath",clipPathFrom:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",overwrite:!0},extendTimeline:!0})}_animateCurtain(){gsap.registerEffect({name:"animateCurtain",effect:(e,t)=>{if(!app.options.animations.curvedMasks)return Object.assign(t,{overwrite:!1}),gsap.effects.animateMask(e,t);const n=gsap.timeline({onStart:t.onStart,onComplete:t.onComplete});let s=!1;if(s="function"==typeof app.options.animations.curvedMasksForceRepaint?app.options.animations.curvedMasksForceRepaint():"auto"===app.options.animations.curvedMasksForceRepaint?void 0!==window.safari:!!app.options.animations.curvedMasksForceRepaint,e&&e[0]){const i="url('#curtain-clip')",a=document.querySelector("#curtain-clip").querySelector("#curtain-clip__path");let r="center bottom",o="M0,0.5 C0.167,0.167,0.333,0,0.5,0 C0.667,0,0.833,0.167,1,0.5 L1,1 L0,1 L0,0.5",l="M0,0 C0.167,0,0.333,0,0.5,0 C0.667,0,0.833,0,1,0 L1,1 L0,1 L0,0";"top"===t.animateFrom&&(r="center top",o="M0,0 L1,0 L1,0.5 C0.833,0.833,0.667,1,0.5,1 C0.333,1,0.167,0.833,0,0.5 L0,0",l="M0,0 L1,0 L1,1 C0.833,1,0.667,1,0.5,1 C0.333,1,0.167,1,0,1 L0,0"),n.set(e[0],{clipPath:i,inset:"0px"}).set(a,{clearProps:"all"}).fromTo(a,{morphSVG:o,scaleY:0,transformOrigin:r,immediateRender:!0},{morphSVG:l,scaleY:1.01,transformOrigin:r,ease:t.ease,duration:t.duration,onUpdate:()=>{s&&(e[0].style.clipPath="none",e[0].offsetWidth,e[0].style.clipPath="url('#curtain-clip')")}}).set(e[0],{clearProps:t.clearProps}).set(a,{clearProps:"all"})}return n},defaults:{duration:this.defaults.duration,ease:"expo.inOut",animateFrom:"bottom",clearProps:"clipPath,inset"},extendTimeline:!0})}_hideCurtain(){gsap.registerEffect({name:"hideCurtain",effect:(e,t)=>{if(!app.options.animations.curvedMasks)return Object.assign(t,{overwrite:!1}),gsap.effects.hideMask(e,t);const n=gsap.timeline({onStart:t.onStart,onComplete:t.onComplete});let s=!1;if(s="function"==typeof app.options.animations.curvedMasksForceRepaint?app.options.animations.curvedMasksForceRepaint():"auto"===app.options.animations.curvedMasksForceRepaint?void 0!==window.safari:!!app.options.animations.curvedMasksForceRepaint,e&&e[0]){const i="url('#curtain-clip')",a=document.querySelector("#curtain-clip").querySelector("#curtain-clip__path");let r="center bottom",o="M0,0.5 C0.167,0.167,0.333,0,0.5,0 C0.667,0,0.833,0.167,1,0.5 L1,1 L0,1 L0,0.5",l="M0,0 C0.167,0,0.333,0,0.5,0 C0.667,0,0.833,0,1,0 L1,1 L0,1 L0,0";"top"===t.animateTo&&(r="center top",o="M0,0 L1,0 L1,0.5 C0.833,0.833,0.667,1,0.5,1 C0.333,1,0.167,0.833,0,0.5 L0,0",l="M0,0 L1,0 L1,1 C0.833,1,0.667,1,0.5,1 C0.333,1,0.167,1,0,1 L0,0"),n.set(e[0],{clipPath:i,inset:"0px"}).set(a,{clearProps:"all"}).fromTo(a,{morphSVG:l,scaleY:1,transformOrigin:r,immediateRender:!0},{morphSVG:o,scaleY:0,transformOrigin:r,ease:t.ease,duration:t.duration,onUpdate:()=>{s&&(e[0].style.clipPath="none",e[0].offsetWidth,e[0].style.clipPath=i)}}).set(e[0],{clearProps:t.clearProps}).set(a,{clearProps:"all"})}return n},defaults:{duration:this.defaults.duration,ease:"expo.inOut",animateTo:"top",clearProps:"clipPath,inset",onComplete:void 0},extendTimeline:!0})}}class AssetsManager{constructor(){this.promises=[]}load({type:e,src:t=null,id:n=null,inline:s=null,preload:i=!1,refElement:a,version:r=null,timeout:o=3e4,cache:l=!0,cb:c=null}){return new Promise(((p,h)=>{if(l&&n in this.promises)this.promises[n].then(p,h).catch(p,h);else{if("style"===e){const e=this._loadStyle({src:t,id:n,inline:s,preload:i,refElement:a,timeout:o,version:r,cb:c});return this.promises[n]=e,e.then(p,h)}if("script"===e){const e=this._loadScript({src:t,id:n,inline:s,preload:i,refElement:a,timeout:o,version:r,cb:c});return this.promises[n]=e,e.then(p,h)}h(new TypeError('Resource type "style" or "script" is missing.'))}}))}_loadScript({src:e=null,id:t=null,inline:n=null,preload:s=!1,refElement:i=document.body,version:a=null,timeout:r=15e3,cb:o=null}){return new Promise(((l,c)=>{const p=document.querySelector(`script[src="${e}"]`),h=document.getElementsByTagName("head")[0];let u,d,m;if(null!=p||n)l(p);else{if(e&&a&&(e+=`?ver=${a}`),e&&s&&(m=document.createElement("link"),m.setAttribute("rel","preload"),m.setAttribute("href",e),m.setAttribute("as","script"),m.setAttribute("type","text/javascript"),h.prepend(m)),u=document.createElement("script"),u.setAttribute("type","text/javascript"),e&&(u.setAttribute("async","async"),u.setAttribute("src",e),u.setAttribute("crossorigin","anonymous")),!t){const e=Math.round((new Date).getTime()/1e3);t=`ajax-asset-${e}-js`}u.setAttribute("id",t),"string"==typeof n&&n&&n.length&&(u.innerHTML=n),i.append(u),e?(u.onerror=t=>{f(),i.removeChild(u),u=null,c(new Error(`A network error occured while trying to load resouce ${e}`))},void 0===u.onreadystatechange?u.onload=g:u.onreadystatechange=g,d=setTimeout(u.onerror,r)):l(u)}function f(){clearTimeout(d),d=null,u.onerror=u.onreadystatechange=u.onload=null}function g(){if(f(),!u.onreadystatechange||u.readyState&&"complete"===u.readyState)return"function"==typeof o&&o(),void l(u)}}))}_loadStyle({src:e=null,id:t=null,inline:n=null,preload:s=!1,refElement:i,version:a=null,timeout:r=15e3,cb:o=null}){return new Promise(((l,c)=>{const p="string"==typeof n&&n.length;t||c(new TypeError("Resource ID attribute is missing."));const h=document.getElementById(t);let u,d,m,f,g=p?document.createElement("style"):document.createElement("link"),y=!1,b="number"==typeof r&&r>0?r:10;e&&a&&(e+=`?ver=${a}`),e&&s&&(f=document.createElement("link"),f.setAttribute("rel","preload"),f.setAttribute("href",e),f.setAttribute("as","style"),f.setAttribute("type","text/css"),document.head.prepend(f)),p?(g.innerHTML=n,g.setAttribute("id",t),g.setAttribute("type","text/css")):(g.setAttribute("rel","stylesheet"),g.setAttribute("id",t),g.setAttribute("type","text/css"),g.setAttribute("href",e),s||g.setAttribute("crossorigin","anonymous")),null!=i?i.insertAdjacentElement("afterend",g):document.head.append(g),g.onerror=function(t){u&&clearInterval(u),u=null,c(new Error(`A network error occured while trying to load resouce ${e}`))},"sheet"in g?(d="sheet",m="cssRules"):(d="styleSheet",m="rules"),g&&"object"==typeof g.onload&&(g.onload=()=>{g[d]&&g[d][m].length?y=!0:setTimeout((()=>{y=!0}),200)}),u=setInterval((function(){try{if(g[d]&&(g[d][m].length||y))return clearInterval(u),u=null,"function"==typeof o&&o(),l(g),void(h&&h.remove())}catch(e){}b--<0&&(clearInterval(u),u=null,c(new Error(`A network error occured while trying to load resouce ${e}`)))}),20)}))}injectPreload({src:e=null,refElement:t=document.head.querySelector("meta[charset]"),rel:n="prefetch",crossorigin:s="anonymous",as:i="script",type:a="application/javascript"}={}){if(e&&!document.head.querySelector(`link[rel="${n}"][href="${e}"]`)&&!document.querySelector(`script[src="${e}"]`)){const r=document.createElement("link");r.setAttribute("href",e),r.setAttribute("rel",n),r.setAttribute("as",i),r.setAttribute("crossorigin",s),r.setAttribute("type",a),t?t.insertAdjacentElement("afterend",r):document.head.prepend(r)}}}class BaseAnimation{constructor(){this.animations={animatedScale:{animationName:"animateScale",initialVars:{scale:0},targetVars:{animateFrom:"center",ease:"power3.out",duration:.6}},animatedBorderHorizontal:{animationName:"animateScale",initialVars:{scaleX:0},targetVars:{animateFrom:"left",ease:"power4.out",duration:1.2}},animatedMask:{animationName:"animateMask",setAnimationName:"hideMask",initialVars:{animateTo:"center",duration:0,overwrite:!1,clearProps:!1,scaleInner:".js-animated-mask-inner"},targetVars:{animateFrom:"center",shape:"rectangle",ease:"power4.out",duration:1.2,scale:1.2,scaleInner:".js-animated-mask-inner"}},animatedMaskCircle:{animationName:"animateMask",setAnimationName:"hideMask",initialVars:{animateTo:"center",duration:0,overwrite:!1,clearProps:!1,scaleInner:".js-animated-mask-inner"},targetVars:{animateFrom:"center",shape:"circle",ease:"power2.inOut",duration:1.2,scale:1.2,scaleInner:".js-animated-mask-inner"}},animatedMaskEllipse:{animationName:"animateMask",setAnimationName:"hideMask",initialVars:{animateTo:"center",duration:0,overwrite:!1,clearProps:!1,scaleInner:".js-animated-mask-inner"},targetVars:{animateFrom:"center",shape:"ellipse",ease:"power2.inOut",duration:1.2,scale:1.2,scaleInner:".js-animated-mask-inner"}},animatedMaskFromTop:{animationName:"animateMask",setAnimationName:"hideMask",initialVars:{animateTo:"bottom",duration:0,overwrite:!1,clearProps:!1,scaleInner:".js-animated-mask-inner"},targetVars:{animateFrom:"top",shape:"rectangle",ease:"power3.inOut",duration:.9,scale:1.2,scaleInner:".js-animated-mask-inner"}},animatedMaskFromBottom:{animationName:"animateMask",setAnimationName:"hideMask",initialVars:{animateTo:"top",duration:0,overwrite:!1,clearProps:!1,scaleInner:".js-animated-mask-inner"},targetVars:{animateFrom:"bottom",shape:"rectangle",ease:"power3.inOut",duration:.9,scale:1.2,scaleInner:".js-animated-mask-inner"}},animatedMaskFromLeft:{animationName:"animateMask",setAnimationName:"hideMask",initialVars:{animateTo:"right",duration:0,overwrite:!1,clearProps:!1,scaleInner:".js-animated-mask-inner"},targetVars:{animateFrom:"left",shape:"rectangle",ease:"power3.inOut",duration:.9,scale:1.2,scaleInner:".js-animated-mask-inner"}},animatedMaskFromRight:{animationName:"animateMask",setAnimationName:"hideMask",initialVars:{animateTo:"left",duration:0,overwrite:!1,clearProps:!1,scaleInner:".js-animated-mask-inner"},targetVars:{animateFrom:"right",shape:"rectangle",ease:"power3.inOut",duration:.9,scale:1.2,scaleInner:".js-animated-mask-inner"}},animatedReveal:{initialVars:{yPercent:-100},targetVars:{yPercent:0,ease:"power3.out",duration:1.2}},animatedRevealBottom:{initialVars:{yPercent:100},targetVars:{yPercent:0,ease:"power3.out",duration:1.2}},animatedJump:{initialVars:{autoAlpha:0,y:50},targetVars:{autoAlpha:1,y:0,ease:"power3.out",duration:.6}},animatedJumpFromLeft:{initialVars:{autoAlpha:0,xPercent:-30},targetVars:{autoAlpha:1,xPercent:0,ease:"power3.out",duration:.6}},animatedJumpFromRight:{initialVars:{autoAlpha:0,xPercent:30},targetVars:{autoAlpha:1,xPercent:0,ease:"power3.out",duration:.6}},animatedJumpScale:{initialVars:{autoAlpha:0,scaleX:1,scaleY:1.5,x:0,y:300,xPercent:0,yPercent:0},targetVars:{autoAlpha:1,scaleX:1,scaleY:1,x:0,y:0,xPercent:0,yPercent:0,ease:"power3.out",duration:.6}},animatedFade:{initialVars:{autoAlpha:0},targetVars:{autoAlpha:1,ease:"power3.out",duration:.6}}}}prepareAnimation(){return new Promise((e=>{e(!0)}))}getScrubAnimation(){return gsap.timeline()}getRevealAnimation(){return gsap.timeline()}_registerAnimations(){const e=app.utilities.getTimeScaleValue();for(const[t,n]of Object.entries(this.animations)){const s=this._getScopedAnimationElements(`[data-arts-os-animation-name="${t}"]`);if(s.length){const t={elements:s};"initialVars"in n&&Object.assign(t,{initialVars:n.initialVars}),"setAnimationName"in n&&"function"==typeof gsap.effects[n.setAnimationName]&&Object.assign(t,{shouldRunSetTween:!1,setCb:(e,t)=>{gsap.effects[n.setAnimationName](e,t)}}),"animationName"in n&&"function"==typeof gsap.effects[n.animationName]&&Object.assign(t,{shouldRunTween:!1,cb:t=>{if("duration"in n.targetVars){const t=n.targetVars.duration;Object.assign(n.targetVars,{duration:t/e})}gsap.effects[n.animationName](t,n.targetVars)}}),this._createBatchSTReveal(t)}}}_getRevealTextAnimation(){const e=this._getScopedAnimationElements();this._createBatchSTReveal({elements:e,shouldRunTween:!1,clearVars:!1,cb:e=>{this._animateText(e)}})}_animateText(e){const t=gsap.timeline(),n=app.utilities.getTimeScaleValue();e.forEach(((e,n)=>{if(app.utilities.isHTMLElement(e)){switch(e.getAttribute("data-arts-split-text-preset")){case"animatedCounterChars":t.animateChars(e,{duration:1.2,y:"-40%",stagger:{from:"start",amount:.1}},"start");break;case"animatedChars":t.animateChars(e,{duration:1.2,stagger:{from:"start",amount:.3}},"start");break;case"animatedCharsRandom":t.animateChars(e,{duration:1.2,stagger:{from:"random",amount:.3}},"start");break;case"animatedWords":t.animateWords(e,{duration:1.2,stagger:{from:"start",amount:.1}},0===n?"start":"<20%");break;case"animatedLines":t.animateLines(e,{duration:1.2,stagger:.07},0===n?"start":"<20%")}}})),this._animateOutlines(t),t.timeScale(n)}_animateOutlines(e){e&&this.elements.outlines&&this.elements.outlines.length&&e.add((()=>{this.elements.outlines.forEach(((t,n)=>{e.to(t,{duration:1.8,drawSVG:"100% 0%",ease:"expo.inOut",delay:.1*n},"<");const s=this.elements.outlines[n].closest(".js-arts-split-text__wrapper-line");s&&(s.classList.remove("overflow-hidden"),s.style.overflow="initial"),this.elements.outlines[n].classList.add("color-outline")}))}),"<50%")}_initAnimations(){return new Promise((e=>{const t=[];this._hasAnimationScene()?(t.push(this.prepareAnimation()),this.infiniteListImages&&t.push(this.infiniteListImages.ready),this.infiniteListHeadings&&t.push(this.infiniteListHeadings.ready),Promise.all(t).finally((()=>{this._getRevealTextAnimation(),this._registerAnimations(),this._createSTReveal(),this._setAnimationReady(),this._createSTScrub(),e(!0)}))):(this._createSTScrub(),e(!0))}))}destroyAnimations(){return new Promise((e=>{const t=[];if(this._hasAnimationScene()){const e=scheduler.postTask((()=>{this.element.setAttribute("data-arts-os-animation","true")}));t.push(e)}const n=new Promise((e=>{gsap.set(this.element,{clearProps:"all",onComplete:()=>e(!0)})}));t.push(n),Promise.all(t).finally((()=>e(!0)))}))}_createSTReveal(){const e=this.getRevealAnimation();if(e){const t=[app.loaded],n=gsap.timeline({defaults:{duration:1.2,ease:"power3.out"},onStart:()=>{app.utilities.dispatchEvent("animation/start",{},this.element)},onUpdate:()=>{app.utilities.dispatchEvent("animation/update",{},this.element)},onComplete:()=>{e.kill(),app.utilities.dispatchEvent("animation/end",{},this.element)}}).add(e.play()),s=app.componentsManager.getComponentByName("Preloader"),i=app.utilities.getTriggerHookValue(),a=app.utilities.getTimeScaleValue();n.timeScale(a),this.stReveal=ScrollTrigger.create({start:this.containerAnimation?"top bottom":`top bottom-=${window.innerHeight*i}px`,animation:n,trigger:this.element,invalidateOnRefresh:!0,once:!0}),this.stReveal.disable(),s&&t.push(s.loaded),this._isWebGLEnabled()&&t.push(this.webGLReady),Promise.all(t).finally((()=>{app.refresher&&app.refresher.refreshComplete?app.refresher.refreshComplete.finally((()=>{this.stReveal.enable()})):this.stReveal.enable()}))}}_createSTScrub(){const e=this.getScrubAnimation();e&&(e.matchMedia?(this.mmSTScrub=gsap.matchMedia(),this.mmSTScrub.add(e.matchMedia,(()=>{this.stScrub=ScrollTrigger.create(e)}))):this.stScrub=ScrollTrigger.create(e),app.utilities.addAJAXStartEventListener(this._killAnimations.bind(this)))}_killAnimations(){this.stScrub&&this.stScrub.kill(),this.mmSTScrub&&"function"==typeof this.mmSTScrub.kill&&this.mmSTScrub.kill()}_createBatchSTReveal({initialVars:e={},targetVars:t={},clearVars:n={},elements:s,interval:i,batchMax:a,setCb:r,cb:o,shouldRunTween:l=!0,shouldRunSetTween:c=!0}={}){if(s){const p=[app.loaded],h=app.utilities.getTimeScaleValue(),u=app.utilities.getTriggerHookValue(),d={interval:i,batchMax:a,start:({trigger:e})=>{let t=0,n=0;const s=window.innerHeight,{top:i}=e.getBoundingClientRect(),a=i+document.documentElement.scrollTop,r=Math.max(0,s-s*u),o=ScrollTrigger.maxScroll(window);if(a>=s&&(n=s*u),o+r<=a){const e=10;t=o+s-a,t=Math.max(0,n-t+e)}return`top-=${t} bottom-=${n}`},once:!0},m={autoAlpha:1,x:0,y:0,xPercent:0,yPercent:0,scaleX:1,scaleY:1,duration:.6,stagger:.07,ease:"power3.out"},f={clearProps:"opacity,visibility,transform"},g=app.componentsManager.getComponentByName("Preloader");if("object"==typeof t&&(t=deepmerge(m,t)),"object"==typeof n&&(n=deepmerge(f,n)),"duration"in t){const e=t.duration;Object.assign(t,{duration:e/h})}Object.assign(d,{onEnter:e=>{Object.assign(t,{onStart:()=>{app.utilities.dispatchEvent("animation/start",{},this.element)},onUpdate:()=>{app.utilities.dispatchEvent("animation/update",{},this.element)},onComplete:()=>{"object"==typeof n&&gsap.set(e,n),app.utilities.dispatchEvent("animation/complete",{},this.element)}}),"function"==typeof o&&o(e,t),l&&gsap.to(e,t)}}),Object.assign(e,{onComplete:()=>{const e=ScrollTrigger.batch(s,d);e.forEach((e=>e.disable())),g&&p.push(g.loaded),this._isWebGLEnabled()&&p.push(this.webGLReady),Promise.all(p).finally((()=>{app.refresher&&app.refresher.refreshComplete?app.refresher.refreshComplete.finally((()=>{e.forEach((e=>e.enable()))})):e.forEach((e=>e.enable()))}))}}),"function"==typeof r&&r(s,e),c&&gsap.set(s,e);const y=s.filter((e=>e.hasAttribute("data-arts-os-animation-name")||e.closest("[data-arts-os-animation-name]")));y&&y.length&&app.loaded.finally((()=>{ScrollTrigger.addEventListener("refreshInit",this._onRefreshInit.bind(this,y)),app.utilities.addAJAXStartEventListener((()=>new Promise((e=>{ScrollTrigger.removeEventListener("refreshInit",this._onRefreshInit.bind(this,y)),e(!0)}))))}))}}_onRefreshInit(e){return gsap.set(e,{x:0,y:0,xPercent:0,yPercent:0})}_setAnimationReady(){return new Promise((e=>{scheduler.postTask((()=>{this.element.setAttribute("data-arts-os-animation","ready")})).finally((()=>e(!0)))}))}_hasAnimationScene(){const e=this.element.getAttribute("data-arts-os-animation");return!!e&&"false"!==e}_getScopedSelector(e){return`:scope ${e}:not(:scope [data-arts-component-name] ${e}):not(:scope [data-arts-component-name]${e})`}_getScopedAnimationElements(e="[data-arts-split-text-preset]"){const t=[...this.element.querySelectorAll(this._getScopedSelector(e))];return this.element.matches(e)&&t.push(this.element),t}}class BaseComponent extends BaseAnimation{constructor({name:e,element:t,loadInnerComponents:n,parent:s,defaults:i,innerElements:a}){super(),this.mounted=!1,this.containerAnimation=void 0,this.dataReady=new Promise((e=>{this._setDataReady=e})),this.ready=new Promise((e=>{this._setReady=e})),this.webGLReady=new Promise((e=>{this._setWebGLReady=e})),this.loadInnerComponents=n,this.name=e,this.element=t,this.parent=s,this.defaults="function"==typeof i?i():i,this.innerSelectors=a,this.components=[],this.elements={},this.options={},Promise.all([this._updateOptions(),this._updateElements({container:this.element,elements:this.innerSelectors})]).finally((()=>this._setDataReady()))}setup(){Promise.all([this.dataReady,document.fonts.ready]).then((()=>this.mount())).then((()=>this.init())).then((()=>this._initAnimations())).finally((()=>this._setReady()))}init(){return new Promise((e=>{e(!0)}))}destroy(){return new Promise((e=>{e(!0)}))}update(){return new Promise((e=>{this._updateOptions().finally((()=>e(!0)))}))}reload({options:e,container:t,reload:n=!0}={reload:!0,options:void 0}){return new Promise((s=>{const i=[];if(this.stReveal){const e=scheduler.postTask((()=>{this.stReveal.kill()}));i.push(e)}if(this.stScrub){const e=scheduler.postTask((()=>{this.stScrub.kill()}));i.push(e)}this.destroy().then((()=>this.destroyAnimations())).then((()=>this.destroySplitText())).finally((()=>{this.components=[],this.elements={},this.options={},this.mounted=!1,this.splitTextTriggered=!1,n?(t&&(this.element=t),Promise.all([this._updateOptions({attributeSelector:!e&&void 0,options:e,defaults:{}}),this._updateElements({container:this.element,elements:this.innerSelectors})]).finally((()=>{this._setDataReady(),this.setup(),s(!0)}))):s(!0)}))}))}mount(){return new Promise((e=>{this.parent&&this.parent.horizontalScroll&&(this.horizontalScroll=this.parent.horizontalScroll,this.containerAnimation=this.horizontalScroll.getContainerAnimation(this.element)),this.mounted||!this.loadInnerComponents?(this.mounted=!0,e(!0)):Promise.all(app.componentsManager.init({storage:this.components,scope:this.element,parent:this,nameAttribute:"data-arts-component-name",optionsAttribute:"data-arts-component-options"})).then((()=>this._initSplitText())).then((()=>this._initLazyMedia())).finally((()=>{this.mounted=!0,e(!0)}))}))}destroySplitText(){return new Promise((e=>{const t=[];this.splitObserver&&"function"==typeof this.splitObserver.disconnect&&this.splitObserver.disconnect(),this.splitTextInstance&&this.splitTextInstance.length&&this.splitTextInstance.forEach((e=>{const n=scheduler.postTask((()=>{e.destroy()}));t.push(n)})),Promise.all(t).finally((()=>e(!0)))}))}updateRef(e,t){if(e&&t)return Object.assign(this,{[e]:app.componentsManager.getComponentByName(t)}),this[e]}setLoading(e=!0){app.options.cursorLoading&&this.element.classList.toggle("cursor-progress",e)}_getWebGLLoader(){return app.componentsManager.load({properties:app.components.CurtainsBase})}_initLazyMedia(){return new Promise((e=>{const t=[...this.element.querySelectorAll(".lazy:not(:scope [data-arts-component-name] .lazy)")];t.length&&ScrollTrigger.create({trigger:this.element,start:()=>"top-=2000px bottom",scrub:!1,once:!0,onEnter:()=>{t.forEach((e=>{scheduler.postTask((()=>{"function"==typeof LazyLoad&&"function"==typeof LazyLoad.load&&LazyLoad.load(e)}))}))}}),setTimeout((()=>e(!0)),0)}))}_initSplitText(){return new Promise((e=>{const t=[],n=this._getScopedAnimationElements();let s={init:!0,type:"lines",set:{type:"lines",direction:"y",distance:"102%",opacity:!1},wrap:"lines",wrapClass:"overflow-hidden"};this.splitTextInstance=[],this.splitTextTriggered=!1,this.splitObserver=new ResizeObserver(app.utilities.debounce(this._onSplitTextResize.bind(this),2*app.utilities.getDebounceTime())),n.forEach(((e,n)=>{const i=scheduler.postTask((()=>{const t=e.getAttribute("data-arts-split-text-preset");this._createTextOutlines(e),t&&t in app.animations.splitTextPresets&&(s=app.animations.splitTextPresets[t]);try{this.splitTextInstance[n]=new ArtsSplitText(e,s),this.splitObserver.observe(e)}catch(e){console.error(`An error occured in component ${this.name}: ${e}`)}}));t.push(i)})),app.utilities.addAJAXStartEventListener(this._destroyResize.bind(this)),Promise.all(t).finally((()=>e(!0)))}))}_destroyResize(){return new Promise((e=>{scheduler.postTask((()=>{this.splitObserver&&"function"==typeof this.splitObserver.disconnect&&this.splitObserver.disconnect()})).finally((()=>e(!0)))}))}_createTextOutlines(e){const t=[...e.querySelectorAll("u")];t.length&&"string"==typeof app.options.circleTemplate&&(this.elements.outlines=[],t.forEach((e=>{e.insertAdjacentHTML("beforeend",app.options.circleTemplate);const t=e.querySelector("ellipse");this.elements.outlines.push(t),this._hasAnimationScene()&&gsap.set(t,{drawSVG:"0% 0%"})})))}_onSplitTextResize(e){const t=app.componentsManager.getComponentByName("Preloader"),n=app.componentsManager.getComponentByName("AJAX"),s=app.componentsManager.getComponentByName("Header");if(!(t&&t.running||n&&n.running))if(this.splitTextTriggered)for(const t of e)s&&s.element.contains(t.target)&&s.instance&&!s.instance.opened||(this.elements.outlines=[],this.splitTextInstance.forEach((e=>{scheduler.postTask((()=>{if(e.containerElement===t.target){e.destroy(),t.target.classList.contains("split-text-animation-revealed")&&[...t.target.querySelectorAll("u ellipse")].forEach((e=>{e.style=null}));[...t.target.querySelectorAll("u")].forEach((e=>{const t=e.querySelector("ellipse");this.elements.outlines.push(t)})),e.init(),t.target.classList.contains("split-text-animation-revealed")&&[...t.target.querySelectorAll("u ellipse")].forEach((e=>{const t=e.closest(".js-arts-split-text__wrapper-line");t&&scheduler.postTask((()=>{t.classList.remove("overflow-hidden"),t.style.overflow="initial"}))}))}}))})));else this.splitTextTriggered=!0}_setReady(){}_setDataReady(){}_setWebGLReady(){}_isWebGLEnabled(){return app.utilities.isEnabledOption(this.options.webGL)&&this.elements.canvasWrapper[0]}_updateOptions({container:e=this.element,target:t=this.options,defaults:n=this.defaults,attributeSelector:s="data-arts-component-options",options:i={}}={}){return new Promise((a=>{if(!e)return a(!0),{};const r=[];let o={};if(i&&n){const e=scheduler.postTask((()=>{o=deepmerge(n,i)}));r.push(e)}if(s){let t;const n=new Promise((n=>{t=app.utilities.parseOptionsStringObject(e.getAttribute(s)),t&&0!==Object.keys(t).length?scheduler.postTask((()=>{o=deepmerge(o,t)})).finally((()=>n(!0))):n(!0)}));r.push(n)}Promise.all(r).finally((()=>{Object.assign(t,o),a(!0)}))}))}_updateElements({container:e,elements:t}){return new Promise((n=>{if(e&&t&&"object"==typeof t){const s=[];this.element=e;for(const n in t){const i=scheduler.postTask((()=>{const s=`${t[n]}`;Object.assign(this.elements,{[n]:[...e.querySelectorAll(s)]})}));s.push(i)}Promise.all(s).finally((()=>n(!0)))}else n(!0)}))}_getInnerComponentByName(e){const t=this.components.findIndex((t=>t.name===e));return t>-1&&this.components[t]}_getInnerElementByName(e){if(this.elements[e]&&this.elements[e][0])return this.elements[e][0]}}class ComponentsManager{constructor(){this.instances={persistent:[],disposable:[]}}init({scope:e=document,scopeExclude:t=[],parent:n=null,loadInnerComponents:s=!0,storage:i=this.instances.disposable,selector:a=":scope [data-arts-component-name]:not(:scope [data-arts-component-name] [data-arts-component-name])",loadOnlyFirst:r=!1,nameAttribute:o="data-arts-component-name",optionsAttribute:l="data-arts-component-options"}){if(!e)return[];let c=r?[e.querySelector(a)]:[...e.querySelectorAll(a)],p=[];return n||(c=c.filter((e=>e&&!e.matches(":scope [data-arts-component-name] [data-arts-component-name]"))),r||t.length||(c[0]=null)),t.length&&(c=c.filter((e=>{let n=!0;return t.forEach((t=>{t.contains(e)&&(n=!1)})),n}))),c&&c.length&&c.forEach((e=>{const t=this.loadComponent({el:e,parent:n,storage:i,loadInnerComponents:s,nameAttribute:o,optionsAttribute:l});p.push(t)})),p}loadComponent({el:e,loadInnerComponents:t,parent:n,storage:s,name:i,nameAttribute:a="data-arts-component-name",optionsAttribute:r="data-arts-component-options",options:o}){if(!e)return new Promise((e=>{e(!0)}));const l=i||e.getAttribute(a),c=o||e.getAttribute(r);return new Promise(((i,a)=>{if(void 0!==window[l]){const a=new window[l]({name:l,loadInnerComponents:t,parent:n,element:e,options:c});s.push(a),a.ready.finally((()=>i(!0)))}else app.components[l]?this.load({properties:app.components[l]}).then((a=>{if("object"==typeof a&&"default"in a){const r=new a.default({name:l,loadInnerComponents:t,parent:n,element:e,options:c});s.push(r),r.ready.finally((()=>{app.refresher&&Object.assign(app.refresher,{requiresTriggersSorting:!0}),i(r)}))}else i(!0)})).catch((e=>{console.error(`Component "${l}" is not recognized`),console.error(e),i(!0)})):(console.error(`Component "${l}" is not recognized`),i(!0))}))}load({properties:e=[]}){const t=[],n=[];return new Promise((s=>{"dependencies"in e&&e.dependencies.forEach((e=>{e in app.assets&&app.assets[e].forEach((e=>{t.push(app.assetsManager.load(e))}))})),"files"in e&&e.files.forEach((e=>{n.push(app.assetsManager.load(e))})),Promise.all(t).then((()=>Promise.all(n))).then((()=>"string"==typeof e.file?import(e.file):{})).then(s).catch(s)}))}disposeAllComponents(e,t="disposable"){return new Promise((n=>{if(!e)return void n(!0);const s=[];this.instances[t].forEach(((n,i)=>{if(e.contains(n.element)){const e=new Promise((e=>{this.disposeInnerComponents(n).finally((()=>{this.instances[t][i]=null,delete this.instances[t][i],e(!0)}))}));s.push(e)}})),Promise.all(s).finally((()=>{this.resetInstancesArray(e,this.instances[t]),n(!0)}))}))}disposeComponent(e,t="disposable"){this.instances[t].forEach(((n,s)=>{e===n.element&&(this.disposeInnerComponents(n),n&&"function"==typeof n.destroy&&n.destroy(),this.instances[t][s]=null,delete this.instances[t][s])})),Object.assign(this.instances,{[t]:[...this.instances[t].filter((e=>e))]})}disposeInnerComponents(e){return new Promise((t=>{const n=[];e.components&&e.components.length?(e.components.forEach((e=>{const t=new Promise((t=>{this.disposeInnerComponents(e).finally((()=>t(!0)))}));n.push(t)})),"function"==typeof e.destroy&&n.push(new Promise((t=>{e.destroy().finally((()=>t(!0)))})))):"function"==typeof e.destroy&&n.push(new Promise((t=>{e.destroy().finally((()=>t(!0)))}))),Promise.all(n).finally((()=>t(!0)))}))}resetInstancesArray(e,t){t=t.filter((t=>t&&t.element&&e.contains(t.element)))}getComponentByName(e){return this.instances.persistent.filter((t=>t.name.toLowerCase()===e.toLowerCase()))[0]||void 0}updateRef(e="headerRef",t="Header",n=this.instances.disposable){e&&t&&n&&[...n].filter((t=>t&&e in t)).forEach((n=>{n[e]=this.getComponentByName(t),n.components&&n.components.length&&this.updateRef(e,t,n.components)}))}}class Forms{constructor(){this.forms="form",this.input="input-float__input",this.inputClassNotEmpty="input-float__input_not-empty",this.inputClassFocused="input-float__input_focused",this.inputClassChecked="input-float__input_checked",this.inputParentElements=".wpcf7-form-control-wrap, .password-input, .woo-form-control-wrap",this._handlers={focusIn:this._onFocusIn.bind(this),focusOut:this._onFocusOut.bind(this),reset:this._onReset.bind(this),change:this._onChange.bind(this),wpcf7submit:this._onWPCF7Submit.bind(this),dismiss:this._onDismiss.bind(this)},this.init()}init(){this._floatLabels(),this._attachEvents()}_floatLabels(){[...document.querySelectorAll(`.${this.input}`)].forEach((e=>{const t=e.closest(this.inputParentElements);e.value&&e.value.length?this._setNotEmptyValue(e,t):this._setEmptyValue(e,t),this._setChecked(e,t),!e.placeholder||!e.placeholder.length||e.value&&e.value.length||this._setNotEmptyValue(e,t)}))}_setNotEmptyValue(e,t){e&&e.classList.add(this.inputClassNotEmpty),t&&t.classList.add(this.inputClassNotEmpty)}_setEmptyValue(e,t){e&&e.classList.remove(this.inputClassFocused,this.inputClassNotEmpty),t&&t.classList.remove(this.inputClassFocused,this.inputClassNotEmpty)}_setFocus(e,t){e&&(e.classList.add(this.inputClassFocused),e.classList.remove(this.inputClassNotEmpty)),t&&(t.classList.add(this.inputClassFocused),t.classList.remove(this.inputClassNotEmpty))}_setChecked(e,t){e&&e.type&&"checkbox"===e.type&&(e.classList.toggle(this.inputClassChecked,!!e.checked),t&&t.classList.toggle(this.inputClassChecked,!!e.checked))}_removeFocus(e,t){e&&e.classList.remove(this.inputClassFocused),t&&t.classList.remove(this.inputClassFocused)}_isTargetInput(e){return e.classList&&e.classList.contains(this.input)}_isTargetForm(e){return"FORM"===e.tagName}_attachEvents(){window.addEventListener("focusin",this._handlers.focusIn),window.addEventListener("focusout",this._handlers.focusOut),window.addEventListener("reset",this._handlers.reset),window.addEventListener("change",this._handlers.change),window.addEventListener("wpcf7submit",this._handlers.wpcf7submit)}_detachEvents(){window.removeEventListener("focusin",this._handlers.focusIn),window.removeEventListener("focusout",this._handlers.focusOut),window.removeEventListener("change",this._handlers.change),window.removeEventListener("wpcf7submit",this._handlers.wpcf7submit)}_onFocusIn(e){const t=e.target;if(this._isTargetInput(t)){const e=t.closest(this.inputParentElements);this._setFocus(t,e)}}_onFocusOut(e){const t=e.target;if(this._isTargetInput(t)){const e=t.closest(this.inputParentElements);t.value&&t.value.length?this._setNotEmptyValue(t,e):(t.placeholder&&t.placeholder.length&&this._setNotEmptyValue(t,e),this._removeFocus(t,e))}}_onReset(e){const t=e.target;this._isTargetForm(t)&&[...t.querySelectorAll(`.${this.input}`)].forEach((e=>{const t=e.closest(this.inputParentElements);e.classList.remove(this.inputClassFocused,this.inputClassNotEmpty),t&&t.classList.remove(this.inputClassFocused,this.inputClassNotEmpty)}))}_onChange(e){const t=e.target;if(this._isTargetInput(t)){const e=t.closest(this.inputParentElements);this._setChecked(t,e)}}_onWPCF7Submit(e){const{message:t,status:n}=e.detail.apiResponse;"mail_sent"===n&&this._onSuccess(t),"acceptance_missing"!==n&&"mail_failed"!==n||this._onError(t)}_onSuccess(e){this._createModal({template:this._getModalTemplate({icon:"icon-success.svg",message:e}),onDismiss:this._handlers.dismiss})}_onError(e){this._createModal({template:this._getModalTemplate({icon:"icon-error.svg",message:e})})}_onDismiss(){}_getModalTemplate({icon:e,message:t}){return`\n\t\t\t<div class="modal-dialog modal-dialog-centered">\n\t\t\t\t<div class="modal-content radius-img">\n\t\t\t\t\t<div class="modal__close p-3" data-bs-dismiss="modal" data-arts-cursor-follower-target="{scale: 'current', magnetic: 0.33}">\n\t\t\t\t\t\t<img src="${app.options.themeURL}/img/modal/icon-close.svg" alt="">\n\t\t\t\t\t</div>\n\t\t\t\t\t<header class="text-center my-4">\n\t\t\t\t\t\t<img class="d-inline-block mb-4" src="${app.options.themeURL}/img/modal/${e}" width="80px" height="80px" alt=""/>\n\t\t\t\t\t\t<div class="modal__message h5">${t}</div>\n\t\t\t\t\t</header>\n\t\t\t\t\t<div class=">modal-content__wrapper-button">\n\t\t\t\t\t\t<button type="button" class="button button_icon button_fullwidth cursor-highlight elementor-button elementor-button-link" data-bs-dismiss="modal">\n\t\t\t\t\t\t\t<span class="button__label button__label-normal">\n\t\t\t\t\t\t\t\t<span class="button__title">OK</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span class="button__label button__label-hover">\n\t\t\t\t\t\t\t\t<span class="button__title">OK</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t`.trim()}_createModal({template:e,onDismiss:t}){const n=document.createElement("div");n.id="modalContactForm",n.className="modal",n.innerHTML+=e,document.body.appendChild(n);const s=new bootstrap.Modal(n);n.addEventListener("hidden.bs.modal",(()=>{"function"==typeof t&&t(),s.dispose(),n.remove(),app.utilities.scrollLock(!1)})),n.addEventListener("shown.bs.modal",(()=>{app.utilities.scrollLock(!0)})),s.show()}}class HoverEffect{constructor(){this._handlers={hoverIn:this._onMouseEnter.bind(this),hoverOut:this._onMouseLeave.bind(this),prevent:this.preventDefault.bind(this)},this.selectorHoverSelf="[data-hover-class]",this.attributeHoverSelf="data-hover-class",this.selectorHoverGroup="[data-hover-group-class]",this.selectorHoverGroupElements=`${this.selectorHoverGroup} a:not(.no-hover-group):not(.no-hover-group a), ${this.selectorHoverGroup} button`,this.attributeHoverGroup="data-hover-group-class",this.attachEvents(document,this._handlers.hoverIn,this._handlers.hoverOut)}_onMouseEnter(e){const t=e.target;app.utilities.isHTMLElement(t)&&(this._toggleHoverSelfClass({element:t,toggle:!0}),this._toggleHoverGroupClass({element:t,toggle:!0}))}_onMouseLeave(e){const t=e.target;app.utilities.isHTMLElement(t)&&(this._toggleHoverSelfClass({element:t,selector:this.selectorHoverSelf,attribute:this.attributeHoverSelf,toggle:!1}),this._toggleHoverGroupClass({element:t,toggle:!1}))}_toggleHoverSelfClass({element:e,toggle:t}={element:null,toggle:!1}){const n=e.closest(this.selectorHoverSelf);if(n){const e=n.getAttribute(this.attributeHoverSelf);e.length&&n.classList.toggle(e,t)}}_toggleHoverGroupClass({element:e,toggle:t}={element:null,toggle:!1}){if(e.closest(this.selectorHoverGroupElements)){const n=e.closest(this.selectorHoverGroup);if(n){const e=n.getAttribute(this.attributeHoverGroup);e.length&&n.classList.toggle(e,t)}}}attachEvents(e,t,n){e&&("function"==typeof t&&(e.addEventListener("mouseenter",t,!0),e.addEventListener("touchstart",t,!0),e.addEventListener("webkitmouseforcewillbegin",this._handlers.prevent),e.addEventListener("webkitmouseforcedown",this._handlers.prevent),e.addEventListener("webkitmouseforceup",this._handlers.prevent),e.addEventListener("webkitmouseforcechanged",this._handlers.prevent)),"function"==typeof n&&(e.addEventListener("mouseleave",n,!0),e.addEventListener("touchend",n,!0),e.addEventListener("touchcancel",n,!0)))}detachEvents(e,t,n){e&&("function"==typeof t&&(e.removeEventListener("mouseenter",t,!0),e.removeEventListener("touchstart",t,!0),e.removeEventListener("webkitmouseforcewillbegin",this._handlers.prevent),e.removeEventListener("webkitmouseforcedown",this._handlers.prevent),e.removeEventListener("webkitmouseforceup",this._handlers.prevent),e.removeEventListener("webkitmouseforcechanged",this._handlers.prevent)),"function"==typeof n&&(e.removeEventListener("mouseleave",n,!0),e.removeEventListener("touchend",n,!0),e.removeEventListener("touchcancel",n,!0)))}preventDefault(e){e.stopPropagation(),e.preventDefault()}}class Refresher{constructor(e=800){this.currentlyRefreshing=!1,this.requiresTriggersSorting=!0,this.requiresImmediateRefresh=!0,this.refreshComplete=new Promise((e=>{this.setRefreshComplete=e})),this.debounceTime="number"==typeof e?e:800,this._timer=0,this._attachEvents()}run(e=!0,t=!1){if(ScrollTrigger.isRefreshing)return;t&&(this.requiresImmediateRefresh=!0);const n=this.requiresImmediateRefresh?0:this.debounceTime;clearTimeout(this._timer),this._timer=setTimeout(this._refreshFunc.bind(this,e),n)}updateLazy(){return new Promise((e=>{app.lazy&&"function"==typeof app.lazy.update?scheduler.postTask((()=>{app.lazy.update()})).finally((()=>e(!0))):e(!0)}))}_attachEvents(){1!==ScrollTrigger.isTouch?(this._resizeObserver=new ResizeObserver(this.run.bind(this,!0,!1)),app.utilities.addAJAXStartEventListener((()=>new Promise((e=>{this._resizeObserver.unobserve(app.elements.content),e(!0)})))),app.utilities.addAJAXEndEventListener((()=>new Promise((e=>{this.run(!0,!0),this._resizeObserver.observe(app.elements.content),e(!0)}),"end"))),this._resizeObserver.observe(app.elements.content)):this.mq=app.utilities.attachResponsiveResize({callback:this.run.bind(this),immediateCall:!0,autoDetachOnTransitionStart:!1})}_refreshFunc(e){return new Promise((t=>{this.currentlyRefreshing=!0,this._sortTriggers().then((()=>this._refreshST(e))).then((()=>this.updateLazy())).then((()=>this._reset())).finally((()=>{t(!0)}))}))}_reset(){return new Promise((e=>{scheduler.postTask((()=>{this.setRefreshComplete(),this.refreshComplete=new Promise((e=>{this.setRefreshComplete=e})),this.setRefreshComplete(),this.currentlyRefreshing=!1,this.requiresImmediateRefresh=!1})).finally((()=>e(!0)))}))}_sortTriggers(){return new Promise((e=>{scheduler.postTask((()=>{this.requiresTriggersSorting&&ScrollTrigger.sort(((e,t)=>e.start-t.start))})).finally((()=>e(!0)))}))}_refreshST(e){return new Promise((t=>{scheduler.postTask((()=>{ScrollTrigger.refresh(!e)})).finally((()=>t(!0)))}))}setRefreshComplete(){}}class Utilities{constructor(){this._handlers={resize:this.debounce(this._updateMobileBarVh.bind(this),this.getDebounceTime(200))},this.lastVW=window.innerWidth,this.lastVH=window.innerHeight,this.adminBar=document.getElementById("wpadminbar"),this.adminBar||(this.adminBar={offsetHeight:0,offsetWidth:0}),this.mqPointer=window.matchMedia("(hover: hover) and (pointer: fine)"),this.init()}init(){this._attachEvents()}update(){this._updateMobileBarVh()}updateLazy(){return new Promise((e=>{app.lazy&&"function"==typeof app.lazy.update?scheduler.postTask((()=>{app.lazy.update()})).finally((()=>e(!0))):e(!0)}))}destroy(){return new Promise((e=>{this._detachEvents(),e(!0)}))}addMediaQueryListener(e,t,n="change"){e&&"function"==typeof t&&("function"==typeof e.addEventListener?e.addEventListener(n,t):e.addListener(t))}removeMediaQueryListener(e,t,n="change"){e&&"function"==typeof t&&("function"==typeof e.removeEventListener?e.removeEventListener(n,t):e.removeListener(t))}_attachEvents(){this.attachResponsiveResize({callback:this._handlers.resize,immediateCall:!1,autoDetachOnTransitionStart:!1})}_detachEvents(){}attachResponsiveResize({callback:e,immediateCall:t=!0,autoDetachOnTransitionStart:n=!0}={}){if("function"!=typeof e)return;const s=this,i=e.bind(e);function a(e){this.lastVW!==window.innerWidth&&(this.lastVW=window.innerWidth,i())}function r(e){this.lastVH!==window.innerHeight&&(this.lastVH=window.innerHeight,i())}const o=a.bind(a),l=r.bind(r);function c(e,t=!1){e.matches?window.addEventListener("resize",l,!1):window.removeEventListener("resize",l,!1),t&&i()}function p(){window.removeEventListener("resize",o,!1),window.removeEventListener("resize",l,!1),"function"==typeof s.mqPointer.removeEventListener?s.mqPointer.removeEventListener("change",c):s.mqPointer.removeListener(c)}return window.addEventListener("resize",o,!1),c({matches:s.mqPointer.matches},t),"function"==typeof s.mqPointer.addEventListener?s.mqPointer.addEventListener("change",c):s.mqPointer.addListener(c),n&&app.utilities.addAJAXStartEventListener((()=>new Promise((e=>{p(),e(!0)})))),{clear:p}}_updateMobileBarVh(){return new Promise((e=>{let t;const n=scheduler.postTask((()=>{t=document.documentElement.clientHeight})),s=scheduler.postTask((()=>{document.documentElement.style.setProperty("--client-height",t)})),i=scheduler.postTask((()=>{document.documentElement.style.setProperty("--fix-bar-vh",.01*t+"px")}));n.finally((()=>{Promise.all([s,i]).finally((()=>e(!0)))}))}))}elementIsVisibleInViewport(e,t=!0){if(!e)return;const{top:n,left:s,bottom:i,right:a}=e.getBoundingClientRect(),{innerWidth:r,innerHeight:o}=window;return t?(n>0&&n<=o||i>0&&i<=o)&&(s>0&&s<=r||a>0&&a<=r):n>=0&&s>=0&&i<=o&&a<=r}elementIsVisible(e){return e&&"visible"===gsap.getProperty(e,"visibility")&&gsap.getProperty(e,"opacity")>0}scrollTo({target:e=0,ease:t="expo.inOut",delay:n=0,duration:s=.8,offset:i=0,container:a=window,cb:r,lockReveal:o=!0}){const l=app.componentsManager.getComponentByName("Header"),c=app.componentsManager.getComponentByName("Scroll"),p=this.isSmoothScrollingEnabled();return app.utilities.adminBar&&(i+=app.utilities.adminBar.offsetHeight),o&&l&&s>0&&l.lockReveal(!0,{ease:t,delay:n,duration:s}),0===s?p?gsap.set(a,{delay:n,scrollTo:{y:e,offsetY:i},onComplete:()=>{c.instance.scrollTo(e,{immediate:!0,offset:-i,force:!0}),"function"==typeof r&&r()}}):gsap.set(a,{delay:n,scrollTo:e,onComplete:()=>{o&&l&&s>0&&l.lockReveal(!1),"function"==typeof r&&r()}}):p?gsap.to(a,{duration:s,delay:n,ease:t,onStart:()=>{c.instance.scrollTo(e,{duration:s,offset:-i,force:!0,easing:gsap.parseEase(t)})},onComplete:()=>{o&&l&&s>0&&l.lockReveal(!1),"function"==typeof r&&r()}}):gsap.to(a,{duration:s,delay:n,scrollTo:{y:e,offsetY:i},ease:t,onComplete:()=>{o&&l&&s>0&&l.lockReveal(!1),"function"==typeof r&&r()}})}scrollLock(e=!0){return new Promise((t=>{const n=app.componentsManager.getComponentByName("Scroll");document.documentElement.classList.toggle("lock-scroll",e),this.isSmoothScrollingEnabled()&&n.instance&&(e?n.instance.stop():n.instance.start()),t(!0)}))}scrollToAnchorFromHash(e=.3){if(window.location.hash)try{const t=document.querySelector(window.location.hash);if(t)return this.scrollTo({target:t,delay:e})}catch(e){}}isSmoothScrollingEnabled(){const e=app.componentsManager.getComponentByName("Scroll");return e&&!!e.instance}toggleClasses(e,t,n){if(e&&this.isHTMLElement(e)){const s=t.split(" ");s.length&&s.map((t=>e.classList.toggle(t,n)))}}debounce(e,t,n){let s;return function(...i){let a=this,r=n&&!s;clearTimeout(s),s=setTimeout((()=>{s=null,n||e.apply(a,i)}),t),r&&e.apply(a,i)}}getDebounceTime(e=400){return e}parseOptionsStringObject(e){let t={};if(!e)return t;try{t=JSON.parse(this.convertStringToJSON(e))}catch(t){console.warn(`${e} is not a valid parameters object`)}return t}convertStringToJSON(e){if(!e)return;return e.replace(/'/g,'"').replace(/(?=[^"]*(?:"[^"]*"[^"]*)*$)(\w+:)|(\w+ :)/g,(function(e){return'"'+e.substring(0,e.length-1)+'":'}))}pageLock(e=!0,t=!1){return new Promise((n=>{const s=[],i=document.getElementById("page-blocking-curtain");i&&s.push(new Promise((t=>{gsap.set(i,{display:e?"block":"none",onComplete:()=>t(!0)})}))),e?(window.addEventListener("wheel",app.hoverEffect.preventDefault,{passive:t}),window.addEventListener("touchmove",app.hoverEffect.preventDefault,{passive:t}),window.addEventListener("keydown",app.hoverEffect.preventDefault,{passive:t})):(window.removeEventListener("wheel",app.hoverEffect.preventDefault,{passive:t}),window.removeEventListener("touchmove",app.hoverEffect.preventDefault,{passive:t}),window.removeEventListener("keydown",app.hoverEffect.preventDefault,{passive:t})),Promise.all(s).finally((()=>n(!0)))}))}getLinkTarget(e){const t=e.target;if(this.isHTMLElement(t)){const e=t.closest("a")||t.closest(".virtual-link");if(e)return e}return null}getHeaderHeight(){return parseInt(document.documentElement.style.getPropertyValue("--header-height"))}dispatchEvent(e,t,n=document){const s=new CustomEvent(e,t);n.dispatchEvent(s)}waitForVariable(e,t=20,n=1e4){return new Promise(((s,i)=>{const a=setInterval((()=>{void 0!==window[e]&&(clearInterval(a),s(window[e]))}),t);setTimeout((()=>{clearInterval(a),i(`Global variable "window.${e}" is still not defined after ${n}ms.`)}),n)}))}isEnabledOption(e){return!0===e||"object"==typeof e&&(!("enabled"in e)||"enabled"in e&&!0===e.enabled)}getTimeScaleValue(){const e=app.options.animations.timeScale;return"number"==typeof e?0===e?1:gsap.utils.clamp(.01,1/0,e):1}getTriggerHookValue(e=.15){return"triggerHook"in app.options.animations&&"number"==typeof app.options.animations.triggerHook?gsap.utils.clamp(0,1,app.options.animations.triggerHook):e}isHTMLElement(e,t="Element"){if(!e)return!1;let n=e.__proto__;for(;null!==n;){if(n.constructor.name===t)return!0;n=n.__proto__}return!1}addMarqueeDelimiter(e="",t=[]){return new Promise((n=>{const s=[];if("string"==typeof e&&t.length){const n=document.createElement("span"),i=document.createElement("span");i.classList.add("marquee-heading"),n.classList.add("marquee-delimiter"),n.innerHTML=e.length?e:null,t.forEach((t=>{const a=scheduler.postTask((()=>{const s=t.cloneNode(!0),a=i.cloneNode(!0),r=new DocumentFragment;if(a.innerHTML=s.innerHTML,this.removeAllChildNodes(s),s.appendChild(a),r.appendChild(s),e.length){const e=n.cloneNode(!0);s.appendChild(e)}t.replaceWith(r)}));s.push(a)}))}Promise.all(s).finally((()=>n(!0)))}))}removeAllChildNodes(e){for(;e.firstChild;)e.removeChild(e.firstChild)}addAJAXStartEventListener(e){return new Promise((t=>{app.shoudLoadAJAX()&&"function"==typeof e?app.AJAXReady.finally((()=>{app.componentsManager.getComponentByName("AJAX").scheduleStartTask(e)})):t(!0)}))}addAJAXEndEventListener(e){return new Promise((t=>{app.shoudLoadAJAX()&&"function"==typeof e?app.AJAXReady.finally((()=>{app.componentsManager.getComponentByName("AJAX").scheduleEndTask(e)})):t(!0)}))}shouldPreventLinkClick(e){return!!app.options.isElementorEditor||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey}}(()=>{"use strict";var e={417:e=>{var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===n}(e)}(e)},n="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function s(e,t){return!1!==t.clone&&t.isMergeableObject(e)?o((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function i(e,t,n){return e.concat(t).map((function(e){return s(e,n)}))}function a(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return e.propertyIsEnumerable(t)})):[]}(e))}function r(e,t){try{return t in e}catch(e){return!1}}function o(e,n,l){(l=l||{}).arrayMerge=l.arrayMerge||i,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=s;var c=Array.isArray(n);return c===Array.isArray(e)?c?l.arrayMerge(e,n,l):function(e,t,n){var i={};return n.isMergeableObject(e)&&a(e).forEach((function(t){i[t]=s(e[t],n)})),a(t).forEach((function(a){(function(e,t){return r(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,a)||(r(e,a)&&n.isMergeableObject(t[a])?i[a]=function(e,t){if(!t.customMerge)return o;var n=t.customMerge(e);return"function"==typeof n?n:o}(a,n)(e[a],t[a],n):i[a]=s(t[a],n))})),i}(e,n,l):s(n,l)}o.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return o(e,n,t)}),{})};var l=o;e.exports=l},76:(e,t,n)=>{n.r(t)},549:(e,t,n)=>{n.r(t)}},t={};function n(s){var i=t[s];if(void 0!==i)return i.exports;var a=t[s]={exports:{}};return e[s](a,a.exports,n),a.exports}n.d=(e,t)=>{for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};(()=>{n.d(s,{default:()=>c});var e=n(417);const t={init:!0,matchMedia:!1,type:"lines",set:!1,lineClass:"js-arts-split-text__line",wordClass:"js-arts-split-text__word",charClass:"js-arts-split-text__char",wrap:!1,wrapClass:!1,dropCapSelector:".has-drop-cap",reduceWhiteSpace:!0};class i{static getElementByStringSelector(e,t=document){if("string"==typeof e){const n=t.querySelector(e);if(n&&null!==n)return n}if(i.isHTMLElement(e))return e}static isHTMLElement(e,t="Element"){if(!e)return!1;let n=e.__proto__;for(;null!==n;){if(n.constructor.name===t)return!0;n=n.__proto__}return!1}static getElementsInContainer(e,t){return"string"==typeof t&&e&&null!==e?[...e.querySelectorAll(t)]:[...t]}}class a{constructor({container:e,attributeSelector:n="data-arts-split-text-options",options:s}){this._data=t,i.isHTMLElement(e)&&this._transformOptions({container:e,attributeSelector:n,options:s})}get data(){return this._data}set data(e){this._data=e}_transformOptions({container:n,attributeSelector:s,options:i}){if(!n)return{};let r={};if(i&&t&&(r=e(t,i)),s){let t;t="DATA"===s?function(e,t={separator:"-",pattern:/^/}){let n={};var s;return void 0===t.separator&&(t.separator="-"),Array.prototype.slice.call(e.attributes).filter((s=t.pattern,function(e){let t;return t=/^data\-/.test(e.name),void 0===s?t:t&&s.test(e.name.slice(5))})).forEach((function(e){e.name.slice(5).split(t.separator).reduce((function(t,n,s,i){return"data"===n?t:(s===i.length-1?t[n]=e.value:t[n]=t[n]||{},t[n])}),n)})),n}(n):a.parseOptionsStringObject(n.getAttribute(s)),t&&0!==Object.keys(t).length&&(t=a.transformPluginOptions(t),r=e(r,t))}this.data=r}static parseOptionsStringObject(e){let t={};if(!e)return t;try{t=JSON.parse(a.convertStringToJSON(e))}catch(t){console.warn(`${e} is not a valid parameters object`)}return t}static convertStringToJSON(e){if(e)return e.replace(/'/g,'"').replace(/(?=[^"]*(?:"[^"]*"[^"]*)*$)(\w+:)|(\w+ :)/g,(function(e){return'"'+e.substring(0,e.length-1)+'":'}))}static transformPluginOptions(e){return e}}function r(e,t=!1){return(t?e.toLowerCase():e).replace(/(?:^|\s|["'([{])+\S/g,(e=>e.toUpperCase()))}class o{constructor({condition:e,callbackMatch:t,callbackNoMatch:n}){this._handlers={change:this._onChange.bind(this)},this.condition=e,this.callbacks={match:t,noMatch:n},(this._hasMatchFunction()||this._hasNoMatchFunction())&&this.init()}init(){this.mediaQuery=this._addMatchMedia(),this._attachEvents()}destroy(){this._detachEvents(),this.mediaQuery=null}get mediaQuery(){return this._mediaQuery}set mediaQuery(e){this._mediaQuery=e}get callbacks(){return this._callbacks}set callbacks(e){this._callbacks=e}get condition(){return this._condition}set condition(e){this._condition=e}_hasMatchFunction(){return"function"==typeof this.callbacks.match}_hasNoMatchFunction(){return"function"==typeof this.callbacks.noMatch}_addMatchMedia(){return window.matchMedia(`${this.condition}`)}_attachEvents(){"function"==typeof this.mediaQuery.addEventListener?this.mediaQuery.addEventListener("change",this._handlers.change):this.mediaQuery.addListener(this._handlers.change)}_detachEvents(){"function"==typeof this.mediaQuery.removeEventListener?this.mediaQuery.removeEventListener("change",this._handlers.change):this.mediaQuery.removeListener(this._handlers.change)}_onChange(e){e.matches?this._hasMatchFunction()&&this.callbacks.match():e.matches||this._hasNoMatchFunction()&&this.callbacks.noMatch()}}gsap.registerPlugin(ScrollTrigger),gsap.registerPlugin(SplitText);var l=function(e,t){var n={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(n[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(s=Object.getOwnPropertySymbols(e);i<s.length;i++)t.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(e,s[i])&&(n[s[i]]=e[s[i]])}return n};n(76),n(549),new class{constructor({duration:e,ease:t,hideWithOpacity:n,linesSelector:s,wordsSelector:i,charsSelector:a}={duration:1.2,ease:"power4.out",hideWithOpacity:1,linesSelector:".js-arts-split-text__line",wordsSelector:".js-arts-split-text__word",charsSelector:".js-arts-split-text__char"}){this._animations={},this._options={duration:e,ease:t,hideWithOpacity:n,linesSelector:s,wordsSelector:i,charsSelector:a},this._animations={lines:{selector:this._options.linesSelector,duration:this._options.duration,ease:this._options.ease,x:0,y:0,autoAlpha:1,stagger:{amount:.08}},words:{selector:this._options.wordsSelector,duration:this._options.duration,ease:this._options.ease,x:0,y:0,autoAlpha:1,stagger:{amount:.2}},chars:{selector:this._options.charsSelector,duration:this._options.duration,ease:this._options.ease,x:0,y:0,autoAlpha:1,stagger:{from:"end",axis:"x",amount:.3}}},this._registerAnimations()}_registerAnimations(){for(const e in this._animations){const t=r(e),n=`animate${t}`,s=`hide${t}`;gsap.registerEffect({name:n,effect:this._effect,defaults:Object.assign(Object.assign({},this._animations[e]),{animationName:n,type:"reveal"}),extendTimeline:!0}),gsap.registerEffect({name:s,effect:this._effect,defaults:Object.assign(Object.assign({},this._animations[e]),{animationName:s,y:"-103%",autoAlpha:this._options.hideWithOpacity,type:"hide"}),extendTimeline:!0})}gsap.registerEffect({name:"animateCharsDirectional",effect:this._effectCharsDirectional,defaults:Object.assign(Object.assign({},this._animations.chars),{stagger:{from:"auto",amount:.3},animationName:"animateCharsDirectional",type:"reveal"}),extendTimeline:!0}),gsap.registerEffect({name:"hideCharsDirectional",effect:this._effectCharsDirectional,defaults:Object.assign(Object.assign({},this._animations.chars),{y:"-103%",autoAlpha:this._options.hideWithOpacity,animationName:"hideCharsDirectional",type:"hide"}),extendTimeline:!0})}_effect(e,t){const n=gsap.timeline({defaults:{duration:0}});if(e&&e[0]){const s=()=>[...e[0].querySelectorAll(t.selector)],{selector:i}=t,a=l(t,["selector"]);if("type"in t){const n=a.onStart,s=a.onComplete;"reveal"===t.type&&(a.onStart=()=>{"function"==typeof n&&n(),e[0].classList.remove("split-text-animation-hidden")},a.onComplete=()=>{"function"==typeof s&&s(),e[0].classList.remove("split-text-animation-hidden"),e[0].classList.add("split-text-animation-revealed")}),"hide"===t.type&&(a.onStart=()=>{"function"==typeof n&&n(),e[0].classList.remove("split-text-animation-revealed")},a.onComplete=()=>{"function"==typeof s&&s(),e[0].classList.remove("split-text-animation-revealed"),e[0].classList.add("split-text-animation-hidden")}),delete a.type}return 0===a.duration?n.add((()=>{e[0].dataset.artsSplitTextState=encodeURIComponent(JSON.stringify(t)),delete a.stagger,delete a.duration,delete a.animationName,delete a.selector,gsap.set(s(),a)})):(delete a.animationName,delete a.selector,n.to({},{delay:a.delay,duration:a.duration,ease:a.ease,onStart:()=>{e[0].dataset.artsSplitTextState=encodeURIComponent(JSON.stringify(t)),delete a.delay,n.to(s(),a,"<")}}))}return n}_effectCharsDirectional(e,t){if(e&&e[0]){const n=[...e[0].querySelectorAll(t.selector)];if(n.length){const{selector:e}=t,s=l(t,["selector"]);if("stagger"in s&&"from"in s.stagger&&"auto"===s.stagger.from){let e;switch(gsap.getProperty(n[0],"text-align")){case"left":default:e="start";break;case"center":e="center";break;case"right":e="end"}s.stagger=function(e){let t,n=e.ease,s=e.from||0,i=e.base||0,a=e.axis,r={center:.5,end:1}[s]||0;return function(o,l,c){if(!c)return 0;let p,h,u,d,m,f,g,y,b,_,v,w=c.length;if(!t){for(t=[],g=b=1/0,y=_=-g,v=[],f=0;f<w;f++)m=c[f].getBoundingClientRect(),u=(m.left+m.right)/2,d=(m.top+m.bottom)/2,u<g&&(g=u),u>y&&(y=u),d<b&&(b=d),d>_&&(_=d),v[f]={x:u,y:d};for(p=isNaN(s)?g+(y-g)*r:v[s].x||0,h=isNaN(s)?b+(_-b)*r:v[s].y||0,y=0,g=1/0,f=0;f<w;f++)u=v[f].x-p,d=h-v[f].y,t[f]=m=a?Math.abs("y"===a?d:u):Math.sqrt(u*u+d*d),m>y&&(y=m),m<g&&(g=m);t.max=y-g,t.min=g,t.v=w=e.amount||e.each*w||0,t.b=w<0?i-w:i}return w=(t[o]-t.min)/t.max,t.b+(n?n.getRatio(w):w)*t.v}}({from:e,amount:s.stagger.amount})}return 0===s.duration?gsap.set(n,s):gsap.to(n,s)}}}};const c=class extends class{constructor({container:e,options:t={}}){this._enabled=!1,this._initialized=!1,e&&t&&(this._updateContainerElement(e),this._updateOptions(e,t),this._updateSplitTarget())}get enabled(){return this._enabled}set enabled(e){this._enabled=e}get initialized(){return this._initialized}set initialized(e){this._initialized=e}get containerElement(){return this._containerElement}set containerElement(e){this._containerElement=e}_updateContainerElement(e){this.containerElement=i.getElementByStringSelector(e)}get options(){return this._options}set options(e){this._options=e}_updateOptions(e,t){this.options=new a({container:e,attributeSelector:"data-arts-split-text-options",options:t}).data}get matchMedia(){return this._matchMedia}set matchMedia(e){this._matchMedia=e}get splitTarget(){return this._splitTarget}set splitTarget(e){this._splitTarget=e}_updateSplitTarget(){const e=this.containerElement.children;e.length>0?([...e].forEach((e=>{i.isHTMLElement(e)&&(e.matches("ul")||e.matches("ol"))&&(this._wrapListElements(e),e.dataset.artsSplitTextElement="list")})),this.splitTarget=[...e]):this.splitTarget=[this.containerElement]}get splitInstance(){return this._splitInstance}set splitInstance(e){this._splitInstance=e,this.containerElement.dataset.artsSplitTextReady=null===e?"false":"true"}_updateSplitInstance(){this.splitInstance=new SplitText(this.splitTarget,{type:this.options.type,reduceWhiteSpace:this.options.reduceWhiteSpace})}_markSplitTextElements(){"chars"in this.splitInstance&&this.splitInstance.chars.length>0&&this.splitInstance.chars.forEach((e=>{i.isHTMLElement(e)&&"string"==typeof this.options.charClass&&e.classList.add(this.options.charClass)})),"words"in this.splitInstance&&this.splitInstance.words.length>0&&this.splitInstance.words.forEach((e=>{i.isHTMLElement(e)&&"string"==typeof this.options.wordClass&&e.classList.add(this.options.wordClass)})),"lines"in this.splitInstance&&this.splitInstance.lines.length>0&&this.splitInstance.lines.forEach((e=>{i.isHTMLElement(e)&&"string"==typeof this.options.lineClass&&e.classList.add(this.options.lineClass)}))}_wrapSplitTextElements(){if(this.options.wrap&&"string"==typeof this.options.wrap){let e="";"string"==typeof this.options.wrap&&(e=this.options.wrap.slice(0,-1).toLowerCase()),this.splitInstance[this.options.wrap].forEach((t=>{if(i.isHTMLElement(t)){const n=document.createElement("div");"string"==typeof this.options.wrapClass&&n.classList.add(this.options.wrapClass),n.classList.add(`js-arts-split-text__wrapper-${e}`),this._wrap(t,n)}}))}}_wrapListElements(e){[...e.querySelectorAll("li")].forEach((e=>{if(i.isHTMLElement(e)&&!e.querySelector('[data-arts-split-text-element="wrapperLi"]')){const t=document.createElement("div");t.dataset.artsSplitTextElement="wrapperLi",this._wrapInner(e,t)}}))}_wrap(e,t){e.parentNode&&(e.parentNode.insertBefore(t,e),t.appendChild(e))}_wrapInner(e,t){for(e.appendChild(t);e.firstChild!==t;)e.firstChild&&t.appendChild(e.firstChild)}_handleDropCap(){if("string"==typeof this.options.dropCapSelector){const e=this.containerElement.querySelectorAll(this.options.dropCapSelector);e.length>0&&[...e].forEach((e=>{const t=e.innerHTML[0],n=e.innerHTML.slice(1,-1);e.innerHTML=`<span data-arts-split-text-element="wrapperDropCap">${t}</span>${n}`}))}}_set(){if("artsSplitTextState"in this.containerElement.dataset&&"string"==typeof this.containerElement.dataset.artsSplitTextState){const{animationName:e,selector:t,x:n,y:s,autoAlpha:i}=JSON.parse(decodeURIComponent(this.containerElement.dataset.artsSplitTextState)),a={selector:t,duration:0,x:n,y:s,autoAlpha:i};gsap.effects[e](this.containerElement,a)}else if(this.options.set&&"type"in this.options.set&&"string"==typeof this.options.set.type){const e=`hide${r(this.options.set.type)}`;if(e in gsap.effects&&"function"==typeof gsap.effects[e]){const t={[this.options.set.direction]:"number"==typeof this.options.set.distance?`${this.options.set.distance}px`:this.options.set.distance,duration:0};"number"==typeof this.options.set.opacity&&(t.opacity=this.options.set.opacity),gsap.effects[e](this.containerElement,t)}}}}{constructor(e,t={}){super({container:e,options:t}),this.options.init&&(this.options.matchMedia&&!window.matchMedia(`${this.options.matchMedia}`).matches?this.matchMedia=new o({condition:this.options.matchMedia,callbackMatch:this.init.bind(this)}):this.init())}init(){this.initialized||(this.matchMedia&&this.matchMedia.destroy(),this.options.matchMedia&&(this.matchMedia=new o({condition:this.options.matchMedia,callbackMatch:this.init.bind(this),callbackNoMatch:this.destroy.bind(this)})),this._handleDropCap(),this._updateSplitInstance(),this._markSplitTextElements(),this._wrapSplitTextElements(),this._set(),this.initialized=!0,this.enabled=!0)}destroy(){this.enabled=!1,this.initialized=!1,this.splitInstance&&this.splitInstance.revert()}}})(),this.ArtsSplitText=s.default})(),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap=t()}(this,(function(){"use strict";const e="transitionend",t=e=>{const t=(e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&"#"!==n?n.trim():null}return t})(e);return t?document.querySelector(t):null},n=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),s=e=>n(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(e):null,i=e=>{if(!n(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),s=e.closest("details:not([open])");if(!s)return t;if(s!==e){const t=e.closest("summary");if(t&&t.parentNode!==s)return!1;if(null===t)return!1}return t},a=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),r=e=>{e.offsetHeight},o=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,l=[],c=()=>"rtl"===document.documentElement.dir,p=e=>{"function"==typeof e&&e()},h=(t,n,s=!0)=>{if(!s)return void p(t);const i=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const s=Number.parseFloat(t),i=Number.parseFloat(n);return s||i?(t=t.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(n))):0})(n)+5;let a=!1;const r=({target:s})=>{s===n&&(a=!0,n.removeEventListener(e,r),p(t))};n.addEventListener(e,r),setTimeout((()=>{a||n.dispatchEvent(new Event(e))}),i)},u=/[^.]*(?=\..*)\.|.*/,d=/\..*/,m=/::\d+$/,f={};let g=1;const y={mouseenter:"mouseover",mouseleave:"mouseout"},b=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function _(e,t){return t&&`${t}::${g++}`||e.uidEvent||g++}function v(e){const t=_(e);return e.uidEvent=t,f[t]=f[t]||{},f[t]}function w(e,t,n=null){return Object.values(e).find((e=>e.callable===t&&e.delegationSelector===n))}function E(e,t,n){const s="string"==typeof t,i=s?n:t||n;let a=C(e);return b.has(a)||(a=e),[s,i,a]}function S(e,t,n,s,i){if("string"!=typeof t||!e)return;let[a,r,o]=E(t,n,s);if(t in y){const e=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};r=e(r)}const l=v(e),c=l[o]||(l[o]={}),p=w(c,r,a?n:null);if(p)return void(p.oneOff=p.oneOff&&i);const h=_(r,t.replace(u,"")),d=a?function(e,t,n){return function s(i){const a=e.querySelectorAll(t);for(let{target:r}=i;r&&r!==this;r=r.parentNode)for(const o of a)if(o===r)return k(i,{delegateTarget:r}),s.oneOff&&L.off(e,i.type,t,n),n.apply(r,[i])}}(e,n,r):function(e,t){return function n(s){return k(s,{delegateTarget:e}),n.oneOff&&L.off(e,s.type,t),t.apply(e,[s])}}(e,r);d.delegationSelector=a?n:null,d.callable=r,d.oneOff=i,d.uidEvent=h,c[h]=d,e.addEventListener(o,d,a)}function A(e,t,n,s,i){const a=w(t[n],s,i);a&&(e.removeEventListener(n,a,Boolean(i)),delete t[n][a.uidEvent])}function T(e,t,n,s){const i=t[n]||{};for(const a of Object.keys(i))if(a.includes(s)){const s=i[a];A(e,t,n,s.callable,s.delegationSelector)}}function C(e){return e=e.replace(d,""),y[e]||e}const L={on(e,t,n,s){S(e,t,n,s,!1)},one(e,t,n,s){S(e,t,n,s,!0)},off(e,t,n,s){if("string"!=typeof t||!e)return;const[i,a,r]=E(t,n,s),o=r!==t,l=v(e),c=l[r]||{},p=t.startsWith(".");if(void 0===a){if(p)for(const n of Object.keys(l))T(e,l,n,t.slice(1));for(const n of Object.keys(c)){const s=n.replace(m,"");if(!o||t.includes(s)){const t=c[n];A(e,l,r,t.callable,t.delegationSelector)}}}else{if(!Object.keys(c).length)return;A(e,l,r,a,i?n:null)}},trigger(e,t,n){if("string"!=typeof t||!e)return null;const s=o();let i=null,a=!0,r=!0,l=!1;t!==C(t)&&s&&(i=s.Event(t,n),s(e).trigger(i),a=!i.isPropagationStopped(),r=!i.isImmediatePropagationStopped(),l=i.isDefaultPrevented());let c=new Event(t,{bubbles:a,cancelable:!0});return c=k(c,n),l&&c.preventDefault(),r&&e.dispatchEvent(c),c.defaultPrevented&&i&&i.preventDefault(),c}};function k(e,t){for(const[n,s]of Object.entries(t||{}))try{e[n]=s}catch(t){Object.defineProperty(e,n,{configurable:!0,get:()=>s})}return e}const P={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const n=[];let s=e.parentNode.closest(t);for(;s;)n.push(s),s=s.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((e=>`${e}:not([tabindex^="-"])`)).join(",");return this.find(t,e).filter((e=>!a(e)&&i(e)))}};function M(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function O(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}const x={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${O(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${O(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter((e=>e.startsWith("bs")&&!e.startsWith("bsConfig")));for(const s of n){let n=s.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1,n.length),t[n]=M(e.dataset[s])}return t},getDataAttribute:(e,t)=>M(e.getAttribute(`data-bs-${O(t)}`))},j=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",N=".sticky-top",I="padding-right",R="margin-right";class V{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,I,(t=>t+e)),this._setElementAttributes(j,I,(t=>t+e)),this._setElementAttributes(N,R,(t=>t-e))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,I),this._resetElementAttributes(j,I),this._resetElementAttributes(N,R)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){const s=this.getWidth();this._applyManipulationCallback(e,(e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+s)return;this._saveInitialAttribute(e,t);const i=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${n(Number.parseFloat(i))}px`)}))}_saveInitialAttribute(e,t){const n=e.style.getPropertyValue(t);n&&x.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,(e=>{const n=x.getDataAttribute(e,t);null!==n?(x.removeDataAttribute(e,t),e.style.setProperty(t,n)):e.style.removeProperty(t)}))}_applyManipulationCallback(e,t){if(n(e))t(e);else for(const n of P.find(e,this._element))t(n)}}const D=new Map,F={set(e,t,n){D.has(e)||D.set(e,new Map);const s=D.get(e);s.has(t)||0===s.size?s.set(t,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get:(e,t)=>D.has(e)&&D.get(e).get(t)||null,remove(e,t){if(!D.has(e))return;const n=D.get(e);n.delete(t),0===n.size&&D.delete(e)}};class H{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const s=n(t)?x.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof s?s:{},...n(t)?x.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const i of Object.keys(t)){const a=t[i],r=e[i],o=n(r)?"element":null==(s=r)?`${s}`:Object.prototype.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(a).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${o}" but expected type "${a}".`)}var s}}class $ extends H{constructor(e,t){super(),(e=s(e))&&(this._element=e,this._config=this._getConfig(t),F.set(this._element,this.constructor.DATA_KEY,this))}dispose(){F.remove(this._element,this.constructor.DATA_KEY),L.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){h(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return F.get(s(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.2.0"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const q="show",z="mousedown.bs.backdrop",B={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},W={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class J extends H{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return B}static get DefaultType(){return W}static get NAME(){return"backdrop"}show(e){if(!this._config.isVisible)return void p(e);this._append();const t=this._getElement();this._config.isAnimated&&r(t),t.classList.add(q),this._emulateAnimation((()=>{p(e)}))}hide(e){this._config.isVisible?(this._getElement().classList.remove(q),this._emulateAnimation((()=>{this.dispose(),p(e)}))):p(e)}dispose(){this._isAppended&&(L.off(this._element,z),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=s(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),L.on(e,z,(()=>{p(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(e){h(e,this._getElement(),this._config.isAnimated)}}const G=".bs.focustrap",Y="backward",X={autofocus:!0,trapElement:null},Q={autofocus:"boolean",trapElement:"element"};class K extends H{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return X}static get DefaultType(){return Q}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),L.off(document,G),L.on(document,"focusin.bs.focustrap",(e=>this._handleFocusin(e))),L.on(document,"keydown.tab.bs.focustrap",(e=>this._handleKeydown(e))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,L.off(document,G))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const n=P.focusableChildren(t);0===n.length?t.focus():this._lastTabNavDirection===Y?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?Y:"forward")}}const U="hidden.bs.modal",Z="show.bs.modal",ee="modal-open",te="show",ne="modal-static",se={backdrop:!0,focus:!0,keyboard:!0},ie={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class ae extends ${constructor(e,t){super(e,t),this._dialog=P.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new V,this._addEventListeners()}static get Default(){return se}static get DefaultType(){return ie}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||L.trigger(this._element,Z,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(ee),this._adjustDialog(),this._backdrop.show((()=>this._showElement(e))))}hide(){this._isShown&&!this._isTransitioning&&(L.trigger(this._element,"hide.bs.modal").defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(te),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){for(const e of[window,this._dialog])L.off(e,".bs.modal");this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new J({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new K({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=P.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),r(this._element),this._element.classList.add(te),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,L.trigger(this._element,"shown.bs.modal",{relatedTarget:e})}),this._dialog,this._isAnimated())}_addEventListeners(){L.on(this._element,"keydown.dismiss.bs.modal",(e=>{if("Escape"===e.key)return this._config.keyboard?(e.preventDefault(),void this.hide()):void this._triggerBackdropTransition()})),L.on(window,"resize.bs.modal",(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),L.on(this._element,"mousedown.dismiss.bs.modal",(e=>{e.target===e.currentTarget&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(ee),this._resetAdjustments(),this._scrollBar.reset(),L.trigger(this._element,U)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(L.trigger(this._element,"hidePrevented.bs.modal").defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(ne)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(ne),this._queueCallback((()=>{this._element.classList.remove(ne),this._queueCallback((()=>{this._element.style.overflowY=t}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){const e=c()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!n&&e){const e=c()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each((function(){const n=ae.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e](t)}}))}}var re,oe;return L.on(document,"click.bs.modal.data-api",'[data-bs-toggle="modal"]',(function(e){const n=t(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),L.one(n,Z,(e=>{e.defaultPrevented||L.one(n,U,(()=>{i(this)&&this.focus()}))}));const s=P.findOne(".modal.show");s&&ae.getInstance(s).hide(),ae.getOrCreateInstance(n).toggle(this)})),((e,n="hide")=>{const s=`click.dismiss${e.EVENT_KEY}`,i=e.NAME;L.on(document,s,`[data-bs-dismiss="${i}"]`,(function(s){if(["A","AREA"].includes(this.tagName)&&s.preventDefault(),a(this))return;const r=t(this)||this.closest(`.${i}`);e.getOrCreateInstance(r)[n]()}))})(ae),re=ae,oe=()=>{const e=o();if(e){const t=re.NAME,n=e.fn[t];e.fn[t]=re.jQueryInterface,e.fn[t].Constructor=re,e.fn[t].noConflict=()=>(e.fn[t]=n,re.jQueryInterface)}},"loading"===document.readyState?(l.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of l)e()})),l.push(oe)):oe(),{Modal:ae}}));