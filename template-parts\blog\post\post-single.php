<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

?>

<article <?php post_class( 'post' ); ?> id="post-<?php the_ID(); ?>">
	<!-- Post content -->
	<div class="post__content content clearfix" id="content">
		<?php the_content(); ?>
		<?php
			wp_link_pages(
				array(
					'before'      => '<div class="page-links">' . esc_html__( 'Pages:', 'asli' ),
					'after'       => '</div>',
					'link_before' => '<span class="page-number">',
					'link_after'  => '</span>',
				)
			);
			?>
	</div>
	<!-- - Post content -->
	<?php if ( wp_get_post_tags( $post->ID ) ) : ?>
		<!-- Post tags -->
		<div class="post__tags bt-solid pt-xsmall mt-xsmall">
			<div class="tagcloud">
				<?php the_tags( '', '', '' ); ?>
			</div>
		</div>
		<!-- - Post tags -->
	<?php endif; ?>

	<?php if ( comments_open() || get_comments_number() ) : ?>
		<!-- Post comments -->
		<div class="post__comments mt-small">
			<?php comments_template(); ?>
		</div>
		<!-- - Post comments -->
	<?php endif; ?>
</article>
