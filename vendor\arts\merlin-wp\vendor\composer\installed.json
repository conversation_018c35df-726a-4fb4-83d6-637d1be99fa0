{"packages": [{"name": "arts/utilities", "version": "dev-master", "version_normalized": "dev-master", "dist": {"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsUtilities", "reference": "282313d41a292caa3ae623a9c5ff5fb6485f3669"}, "require": {"php": ">=7.2"}, "require-dev": {"antecedent/patchwork": "^2.1", "brain/monkey": "^2.6", "phpunit/phpunit": "^9.6", "yoast/phpunit-polyfills": "^4.0"}, "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Arts\\Utilities\\": "src/php/"}}, "autoload-dev": {"psr-4": {"Tests\\": "__tests__/php/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A comprehensive collection of utility methods for WordPress theme and plugin development, with a focus on Elementor integration.", "transport-options": {"symlink": true, "relative": false}, "install-path": "../arts/utilities"}, {"name": "arts/wp-content-importer-v2", "version": "dev-master", "version_normalized": "dev-master", "dist": {"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsContentImporter", "reference": "3ebe9604477edc78487cf40b0be66b71aae9ac31"}, "require": {"php": ">=7.4"}, "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ProteusThemes\\WPContentImporter2\\": "src/"}}, "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://artemsemkin.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Humanmade contributors", "homepage": "https://github.com/humanmade/WordPress-Importer/graphs/contributors"}], "description": "Improved WP content importer used in OCDI plugin and MerlinWP. Forked from `proteusthemes/wp-content-importer-v2` and improved by <PERSON><PERSON>.", "keywords": ["content", "import", "proteusthemes", "theme", "wordpress", "wp"], "transport-options": {"symlink": true, "relative": false}, "install-path": "../arts/wp-content-importer-v2"}, {"name": "monolog/monolog", "version": "2.x-dev", "version_normalized": "2.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "cd82b5069148dd811ef54b4b92ce1b3aad84209b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/cd82b5069148dd811ef54b4b92ce1b3aad84209b", "reference": "cd82b5069148dd811ef54b4b92ce1b3aad84209b", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.38 || ^9.6.19", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2025-03-20T09:26:28+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.x"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "install-path": "../monolog/monolog"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}], "dev": true, "dev-package-names": []}