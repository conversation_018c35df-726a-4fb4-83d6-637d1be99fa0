<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_ajax_transitions_options' ) ) {
	/**
	 * Get the AJAX transitions options.
	 *
	 * @return array|false $options
	 */
	function arts_get_ajax_transitions_options() {
		$options      = false;
		$ajax_enabled = Utilities::get_kit_settings( 'ajax_enabled', false );

		if ( $ajax_enabled ) {
			$ajax_animation_timescale               = Utilities::get_kit_settings( 'ajax_animation_timescale', 1 );
			$ajax_timeout                           = Utilities::get_kit_settings( 'ajax_timeout', 10 );
			$ajax_prevent_rules                     = arts_get_ajax_prevent_rules();
			$ajax_custom_js                         = Utilities::get_kit_settings( 'ajax_custom_js', '' );
			$ajax_update_script_nodes               = Utilities::get_kit_settings( 'ajax_update_script_nodes', '' );
			$ajax_update_content_nodes              = Utilities::get_kit_settings( 'ajax_update_content_nodes', '' );
			$ajax_update_content_header_widget_area = Utilities::get_kit_settings( 'ajax_update_content_header_widget_area', true );
			$ajax_update_content_elementor_header   = Utilities::get_kit_settings( 'ajax_update_content_elementor_header', true );
			$ajax_load_missing_scripts              = Utilities::get_kit_settings( 'ajax_load_missing_scripts', true );
			$ajax_eval_inline_container_scripts     = Utilities::get_kit_settings( 'ajax_eval_inline_container_scripts', true );
			$ajax_update_head_nodes                 = arts_get_ajax_update_head_nodes();
			$ajax_load_missing_styles               = Utilities::get_kit_settings( 'ajax_load_missing_styles', true );
			$ajax_remove_missing_styles             = Utilities::get_kit_settings( 'ajax_remove_missing_styles', true );
			$ajax_scroll_to_transition_image        = Utilities::get_kit_settings( 'ajax_scroll_to_transition_image', 'partially_in_view' );

			$options = array(
				'customJS'                      => trim( $ajax_custom_js ),
				'transitionDuration'            => floatval( 2 / $ajax_animation_timescale ),
				'transitionEase'                => 'expo.inOut',
				'timeout'                       => floatval( 1000 * $ajax_timeout ),
				'preventRules'                  => $ajax_prevent_rules,
				'updateNodesAttributes'         => esc_js( $ajax_update_head_nodes ),
				'updateScriptNodes'             => esc_js( $ajax_update_script_nodes ),
				'updateContentNodes'            => esc_js( $ajax_update_content_nodes ),
				'updateContentHeaderWidgetArea' => boolval( $ajax_update_content_header_widget_area ),
				'updateContentElementorHeader'  => boolval( $ajax_update_content_elementor_header ),
				'loadMissingScripts'            => boolval( $ajax_load_missing_scripts ),
				'loadMissingStyles'             => boolval( $ajax_load_missing_styles ),
				'removeMissingStyles'           => boolval( $ajax_remove_missing_styles ),
				'evalInlineContainerScripts'    => boolval( $ajax_eval_inline_container_scripts ),
				'scrollToTransitionImage'       => esc_js( $ajax_scroll_to_transition_image ),
			);
		}

		return $options;
	}
}
