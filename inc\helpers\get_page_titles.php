<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_page_titles' ) ) {
	/**
	 * Retrieves the page titles, subtitles, and descriptions based on the current context.
	 *
	 * @param bool $bc_compatibility_enabled Optional. Whether to enable backward compatibility with the previous version. Default true.
	 * @return array {
	 *   Array containing the page title, subtitle, and description.
	 *
	 *   @type string $title       The page title.
	 *   @type string $subtitle    The page subtitle.
	 *   @type string $description The page description.
	 * }
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::get_page_titles()` method instead.
	 */
	function arts_get_page_titles( $bc_compatibility_enabled = true ) {
		$result = Utilities::get_page_titles();

		if ( $bc_compatibility_enabled ) {
			return array(
				$result['title'],
				$result['subtitle'],
				$result['description'],
			);
		}

		return $result;
	}
}
