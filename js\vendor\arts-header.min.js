(()=>{"use strict";var t={417:t=>{var e=function(t){return function(t){return!!t&&"object"==typeof t}(t)&&!function(t){var e=Object.prototype.toString.call(t);return"[object RegExp]"===e||"[object Date]"===e||function(t){return t.$$typeof===i}(t)}(t)},i="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(t,e){return!1!==e.clone&&e.isMergeableObject(t)?a((i=t,Array.isArray(i)?[]:{}),t,e):t;var i}function s(t,e,i){return t.concat(e).map((function(t){return n(t,i)}))}function o(t){return Object.keys(t).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return t.propertyIsEnumerable(e)})):[]}(t))}function r(t,e){try{return e in t}catch(t){return!1}}function a(t,i,l){(l=l||{}).arrayMerge=l.arrayMerge||s,l.isMergeableObject=l.isMergeableObject||e,l.cloneUnlessOtherwiseSpecified=n;var h=Array.isArray(i);return h===Array.isArray(t)?h?l.arrayMerge(t,i,l):function(t,e,i){var s={};return i.isMergeableObject(t)&&o(t).forEach((function(e){s[e]=n(t[e],i)})),o(e).forEach((function(o){(function(t,e){return r(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))})(t,o)||(r(t,o)&&i.isMergeableObject(e[o])?s[o]=function(t,e){if(!e.customMerge)return a;var i=e.customMerge(t);return"function"==typeof i?i:a}(o,i)(t[o],e[o],i):s[o]=n(e[o],i))})),s}(t,i,l):n(i,l)}a.all=function(t,e){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce((function(t,i){return a(t,i,e)}),{})};var l=a;t.exports=l},979:(t,e,i)=>{i.r(e)},264:(t,e,i)=>{i.r(e)}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,i),o.exports}i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};(()=>{i.d(n,{default:()=>y});var t=i(417);const e={init:!0,matchMedia:!1,trigger:void 0,sticky:{containerSelector:'[data-arts-header-element="bar"]',toggleAttributes:!1,toggleReveal:!0,toggleStickyClass:"sticky",toggleRevealingClass:!1,toggleScrollingDownClass:!1},overlay:{containerSelector:'[data-arts-header-element="overlayContainer"]',toggleAttributes:!1,toggleOpenedClass:"opened",toggleAnimatingClass:"animating",beforeOpen:void 0,onOpen:void 0,onClose:void 0},switcher:{elementSelector:'[data-arts-header-element="overlaySwitcher"]',toggleActiveClass:"active"},menu:{menuSelector:".menu-overlay",submenuBackButtonSelector:'[data-arts-header-element="overlaySubmenuBack"]',submenuLabelSelector:'[data-arts-header-element="overlaySubmenuLabel"]',toggleCurrentMenuClass:"current",toggleSubmenuOpenedClass:"opened-submenu",toggleOpeningClass:"animating",beforeOpen:void 0,onOpen:void 0,onClose:void 0},anchors:{autoCloseOverlay:!0,onClick:t=>{"undefined"!=typeof ScrollToPlugin&&gsap.to(window,{duration:.8,scrollTo:t,ease:"expo.inOut"})}},heightObserver:{containerSelector:'[data-arts-header-element="bar"]',updateCSSVar:"--header-height",observe:!0,cleanupOnDestroy:!1},absolute:!1,hasSmoothScrolling:()=>"undefined"!=typeof ScrollSmoother&&void 0!==ScrollSmoother.get()};class s{static getElementByStringSelector(t,e=document){if("string"==typeof t){const i=e.querySelector(t);if(i&&null!==i)return i}if(s.isHTMLElement(t))return t}static isHTMLElement(t,e="Element"){if(!t)return!1;let i=t.__proto__;for(;null!==i;){if(i.constructor.name===e)return!0;i=i.__proto__}return!1}static getElementsInContainer(t,e){return"string"==typeof e&&t&&null!==t?[...t.querySelectorAll(e)]:"object"==typeof e?[...e]:void 0}}class o{constructor({container:t,attributeSelector:i="data-arts-header-options",options:n}){this._data=e,s.isHTMLElement(t)&&this._transformOptions({container:t,attributeSelector:i,options:n})}get data(){return this._data}set data(t){this._data=t}_transformOptions({container:i,attributeSelector:n,options:s}){if(!i)return{};let r={};if(s&&e&&(r=t(e,s)),n){let e;e="DATA"===n?function(t,e={separator:"-",pattern:/^/}){let i={};var n;return void 0===e.separator&&(e.separator="-"),Array.prototype.slice.call(t.attributes).filter((n=e.pattern,function(t){let e;return e=/^data\-/.test(t.name),void 0===n?e:e&&n.test(t.name.slice(5))})).forEach((function(t){t.name.slice(5).split(e.separator).reduce((function(e,i,n,s){return"data"===i?e:(n===s.length-1?e[i]=t.value:e[i]=e[i]||{},e[i])}),i)})),i}(i):o.parseOptionsStringObject(i.getAttribute(n)),e&&0!==Object.keys(e).length&&(e=o.transformPluginOptions(e),r=t(r,e))}this.data=r}static parseOptionsStringObject(t){let e={};if(!t)return e;try{const i=o.convertStringToJSON(t);"string"==typeof i&&(e=JSON.parse(i))}catch(e){console.warn(`${t} is not a valid parameters object`)}return e}static convertStringToJSON(t){if(t)return t.replace(/'/g,'"').replace(/(?=[^"]*(?:"[^"]*"[^"]*)*$)(\w+:)|(\w+ :)/g,(function(t){return'"'+t.substring(0,t.length-1)+'":'}))}static transformPluginOptions(t){return t}}class r{constructor({autoload:t=!0,options:e,containerElement:i,attributes:n}){this._attributes={},this._toggleAttributes={},this.options=e,this.containerElement=i,n&&(this.attributes=n),t&&this.init()}init(){this.enabled=!0}destroy(){this.enabled=!1}enable(){this.enabled||this.init()}disable(){this.enabled&&this.destroy()}update(){}set enabled(t){this._enabled=t}get enabled(){return this._enabled}get containerElement(){return this._containerElement}set containerElement(t){this._containerElement=t}get options(){return this._options}set options(t){this._options=t}get attributes(){return this._attributes}set attributes(t){this._attributes=t}get toggleAttributes(){return this._toggleAttributes}set toggleAttributes(t){this._toggleAttributes=t}_updateToggleAttributes(t=this.attributes,e=this.containerElement){if("object"==typeof t)for(const i in t){const n=t[i];e.hasAttribute(i)&&Object.assign(this.toggleAttributes,{[i]:{default:e.getAttribute(i),active:e.getAttribute(n)||""}})}}_setAttributes(t=!0,e=this.containerElement){for(const i in this.toggleAttributes){const n=this.toggleAttributes[i],s=n&&n.active&&n.active.length?n.active.split(" ").filter((t=>t.length)):[];t?"class"===i?s.length&&e.classList.add(...s):n.active&&n.active.length&&e.setAttribute(i,n.active):"class"===i?s.length&&e.classList.remove(...s):n.default&&n.default.length&&e.setAttribute(i,n.default)}}}function a(t,e,i){if(t&&s.isHTMLElement(t)){const n=e.split(" ");n.length&&n.map((e=>t.classList.toggle(e,i)))}}class l extends r{constructor({containerElement:t,options:e,attributes:i}){super({autoload:!1,containerElement:t,attributes:i,options:e}),this._locked=!1,this._sticking=!1,this._revealing=!1,this._scrollingDown=!1,this._direction=0,this._revealStart=0,this._revealEnd=0,this._containerHeight=0,this._updateContainerElement(),this._updateToggleAttributes(),this.sticking=!1}init(){return new Promise((t=>{this.enabled?t(!0):this.options.sticky&&this.options.sticky.toggleReveal?this._updateContainerHeight().then((()=>this._updateAnimationReveal())).then((()=>this._createScrollTriggerToggleReveal())).then((()=>{this.options.sticky&&"string"==typeof this.options.sticky.toggleScrollingDownClass&&this._createScrollTriggerScrollingDown(),this._createScrollTriggerSticky(),this.enabled=!0,t(!0)})):this._createScrollTriggerSticky().then((()=>{this.enabled=!0,t(!0)}))}))}destroy(){return new Promise((t=>{this.options.sticky&&this.options.sticky.toggleReveal&&(this._killScrollTriggerToggleReveal(),"string"==typeof this.options.sticky.toggleScrollingDownClass&&this._killScrollTriggerScrollingDown()),this._killScrollTriggerSticky(),this.enabled=!1,t(!0)}))}enable(){if(!this.enabled){if(this.options.sticky&&this.options.sticky.toggleReveal){const t=window.scrollY;this.revealStart=t,this.revealEnd=t+this.containerHeight,this.triggerToggleReveal.enable(!1,!1)}this.triggerSticky.enable(!1,!0),this.sticking=this.triggerSticky.isActive,this.enabled=!0}}disable(){this.enabled&&(this.options.sticky&&this.options.sticky.toggleReveal&&(this.revealing=!1,this.triggerToggleReveal.disable(!1,!0)),this.triggerSticky.disable(!0),this.sticking=!1,this.enabled=!1)}update(){this._updateToggleAttributes(),this.options.sticky&&this.options.sticky.toggleReveal?this._updateContainerHeight().then((()=>{this._updateRevealTriggers(),this.triggerToggleReveal.refresh(),this.triggerSticky.refresh()})):this.triggerSticky.refresh()}_updateContainerElement(){if(this.options.sticky&&"string"==typeof this.options.sticky.containerSelector){const t=s.getElementsInContainer(this.containerElement,this.options.sticky.containerSelector);t&&t[0]&&(this.containerElement=t[0])}}get direction(){return this._direction}set direction(t){this._direction=t}get triggerSticky(){return this._triggerSticky}set triggerSticky(t){this._triggerSticky=t}_createScrollTriggerSticky(){return new Promise((t=>{if(this.triggerSticky)return void t(!0);const e={start:()=>`top+=${1-parseFloat(getComputedStyle(document.documentElement).marginTop)}px top`,end:()=>"bottom top",scrub:!0,refreshPriority:-999,onToggle:t=>{this.sticking=t.isActive,!t.isActive&&this.triggerToggleReveal instanceof ScrollTrigger&&this._updateRevealTriggers("down")}};this.options.trigger&&(e.trigger=this.options.trigger),this.options.sticky&&this.options.sticky.toggleReveal&&(e.onUpdate=this._revealOnUpdate.bind(this)),this.triggerSticky=ScrollTrigger.create(e),t(!0)}))}_killScrollTriggerSticky(){this.triggerSticky&&"function"==typeof this.triggerSticky.kill&&this.triggerSticky.kill(!0)}get triggerToggleReveal(){return this._triggerToggleReveal}set triggerToggleReveal(t){this._triggerToggleReveal=t}_createScrollTriggerToggleReveal(){return new Promise((t=>{this.triggerToggleReveal||(this._updateRevealTriggers("down"),this.triggerToggleReveal=ScrollTrigger.create({start:()=>this.revealStart,end:()=>this.revealEnd,animation:this.animationReveal,scrub:!0,invalidateOnRefresh:!1,onToggle:t=>{this.revealing=t.isActive}})),t(!0)}))}_killScrollTriggerToggleReveal(){this.triggerToggleReveal&&"function"==typeof this.triggerToggleReveal.kill&&(this.triggerToggleReveal.kill(!0),this.animationReveal.kill())}get triggerScrollingDown(){return this._triggerScrollingDown}set triggerScrollingDown(t){this._triggerScrollingDown=t}_createScrollTriggerScrollingDown(){this.triggerScrollingDown||(this.triggerScrollingDown=ScrollTrigger.create({start:0,end:()=>this.containerHeight+parseFloat(getComputedStyle(document.documentElement).marginTop),scrub:!0,onEnter:()=>{this.scrollingDown=!0},onLeave:()=>{this.scrollingDown=!1},onLeaveBack:()=>{this.scrollingDown=!0}}))}_killScrollTriggerScrollingDown(){this.triggerScrollingDown&&"function"==typeof this.triggerScrollingDown.kill&&this.triggerScrollingDown.kill(!0)}get animationReveal(){return this._animationReveal}set animationReveal(t){this._animationReveal=t}_updateAnimationReveal(){return new Promise((t=>{this.animationReveal=gsap.timeline({paused:!0,defaults:{ease:"none"}}).fromTo(this.containerElement,{"--translateY":"0%",immediateRender:!0},{"--translateY":"-100%"}),t(!0)}))}_updateRevealTriggers(t="down"){const e=window.scrollY;"down"===t&&(this.revealStart=e,this.revealEnd=e+this.containerHeight),"up"===t&&(this.revealStart=e-this.containerHeight,this.revealEnd=e)}_revealOnUpdate(t){t.direction!==this.direction&&(1===t.direction?0===this.triggerToggleReveal.progress&&this._updateRevealTriggers("down"):1===this.triggerToggleReveal.progress&&this._updateRevealTriggers("up"),this.triggerToggleReveal.refresh()),this.direction=t.direction}get revealStart(){return this._revealStart}set revealStart(t){this._revealStart=t}get revealEnd(){return this._revealEnd}set revealEnd(t){this._revealEnd=t}get containerHeight(){return this._containerHeight}set containerHeight(t){this._containerHeight=t}get sticking(){return this._sticking}set sticking(t){this.options.sticky&&("string"==typeof this.options.sticky.toggleStickyClass&&a(this.containerElement,this.options.sticky.toggleStickyClass,t),"object"==typeof this.options.sticky.toggleAttributes&&this._setAttributes(t)),this._sticking=t}get locked(){return this._locked}set locked(t){this._locked=t}get revealing(){return this._revealing}set revealing(t){this.options.sticky&&"string"==typeof this.options.sticky.toggleRevealingClass&&a(this.containerElement,this.options.sticky.toggleRevealingClass,t),this._revealing=t}get scrollingDown(){return this._scrollingDown}set scrollingDown(t){this.options.sticky&&"string"==typeof this.options.sticky.toggleScrollingDownClass&&a(this.containerElement,this.options.sticky.toggleScrollingDownClass,t),this._scrollingDown=t}_updateContainerHeight(){return new Promise((t=>{this.containerHeight=this.containerElement.offsetHeight,t(!0)}))}}class h extends r{constructor({containerElement:t,options:e}){super({autoload:!1,containerElement:t,options:e}),this._containerHeight=0}init(){return new Promise((t=>{this.enabled?t(!0):this._updateContainerHeight().then((()=>this._updateAnimationReveal())).then((()=>this._createScrollTriggerAbsolute())).then((()=>{this.enabled=!0,t(!0)}))}))}destroy(){return new Promise((t=>{this.enabled?(this._killScrollTriggerAbsolute(),this.enabled=!1,t(!0)):t(!0)}))}enable(){this.enabled||this.triggerAbsolute.enable()}disable(){this.enabled&&this.triggerAbsolute.disable()}update(){this._updateContainerHeight().then((()=>{this.triggerAbsolute.refresh()}))}get triggerAbsolute(){return this._triggerAbsolute}set triggerAbsolute(t){this._triggerAbsolute=t}_createScrollTriggerAbsolute(){return new Promise((t=>{this.triggerAbsolute=ScrollTrigger.create({animation:this.animationReveal,start:0,end:()=>this.containerHeight,scrub:!0,invalidateOnRefresh:!0}),t(!0)}))}_killScrollTriggerAbsolute(){this.triggerAbsolute&&"function"==typeof this.triggerAbsolute.kill&&this.triggerAbsolute.kill()}get containerHeight(){return this._containerHeight}set containerHeight(t){this._containerHeight=t}_updateContainerHeight(){return new Promise((t=>{this.containerHeight=this.containerElement.offsetHeight,t(!0)}))}get animationReveal(){return this._animationReveal}set animationReveal(t){this._animationReveal=t}_updateAnimationReveal(){return new Promise((t=>{this.animationReveal=gsap.timeline({paused:!0}).fromTo(this.containerElement,{y:"0%"},{ease:"none",y:"-100%",duration:1}),t(!0)}))}}class c extends r{constructor({containerElement:t,options:e,attributes:i}){super({autoload:!1,containerElement:t,options:e,attributes:i}),this._opened=!1,this._animating=!1,this._timeline=gsap.timeline({paused:!0,onStart:()=>{this.animating=!0},onComplete:()=>{this.animating=!1}}),this._updateBarElement(),this.options.overlay&&"object"==typeof this.options.overlay.toggleAttributes&&this._updateToggleAttributes(this.options.overlay.toggleAttributes,this.barElement)}init(){return new Promise((t=>{this.enabled||(this.enabled=!0),t(!0)}))}destroy(){return new Promise((t=>{this.enabled?(this.opened&&this.animateOverlayClose(),this.enabled=!1,t(!0)):t(!0)}))}_updateBarElement(){let t;if(this.options.sticky&&"string"==typeof this.options.sticky.containerSelector?t=this.options.sticky.containerSelector:this.options.overlay&&"string"==typeof this.options.overlay.containerSelectorStickyFallback&&(t=this.options.overlay.containerSelectorStickyFallback),t){const e=s.getElementsInContainer(this.containerElement,t);e&&e[0]&&(this.barElement=e[0])}}get barElement(){return this._barElement}set barElement(t){this._barElement=t}get opened(){return this._opened}set opened(t){this.options.overlay&&"string"==typeof this.options.overlay.toggleOpenedClass&&a(this.containerElement,this.options.overlay.toggleOpenedClass,t),this._opened=t}get animating(){return this._animating}set animating(t){this.options.overlay&&"string"==typeof this.options.overlay.toggleAnimatingClass&&a(this.containerElement,this.options.overlay.toggleAnimatingClass,t),this._animating=t}get timeline(){return this._timeline}set timeline(t){this._timeline=t}animateOverlayOpen(t){this.enabled&&(this.timeline.clear(),this.options.overlay&&"function"==typeof this.options.overlay.onOpen&&this.timeline.add(this.options.overlay.onOpen().play()).play(),this.options.overlay&&"object"==typeof this.options.overlay.toggleAttributes&&this.timeline.set({},{onComplete:()=>this._setAttributes(!0,this.barElement)},"<25%"),this.timeline.add((()=>{this.opened=!0,"function"==typeof t&&t()}),"<50%"))}animateOverlayClose(t){this.enabled&&(this.timeline.clear(),this.options.overlay&&"object"==typeof this.options.overlay.toggleAttributes&&this.timeline.set({},{onComplete:()=>this._setAttributes(!1,this.barElement)},"<25%"),this.options.overlay&&"function"==typeof this.options.overlay.onClose&&this.timeline.add(this.options.overlay.onClose().play()).play(),this.timeline.add((()=>{this.opened=!1,"function"==typeof t&&t()}),"<50%"))}}class u extends r{constructor({containerElement:t,options:e,overlay:i}){super({autoload:!1,containerElement:t,options:e}),this._opening=!1,this._handlers={onSubmenuOpenersClick:this._onSubmenuOpenersClick.bind(this),onSubmenuBackClick:this._onSubmenuBackClick.bind(this)},this._timeline=gsap.timeline({paused:!0,onStart:()=>{this.opening=!0}}),i&&(this.overlay=i)}init(){return new Promise((t=>{this.enabled||(this._updateElements(),this._attachEvents(),this.set(),this.enabled=!0),t(!0)}))}destroy(){return new Promise((t=>{this.enabled?(this._detachEvents(),this.set(),this.enabled=!1,t(!0)):t(!0)}))}set(){this.currentMenu=this.menu,this.previousMenu=null,this.enabled&&this.options.menu&&"function"==typeof this.options.menu.beforeOpen&&this.timeline.clear().add(this.options.menu.beforeOpen())}open(t=this.menu){if(this.previousMenu=this.currentMenu,this.currentMenu=t,s.isHTMLElement(t)&&t.parentElement){const e=t.parentElement.querySelector(":scope > a");e&&"string"==typeof e.textContent?this.label=e.textContent:this.label=""}this.timeline.clear(),this.options.menu&&(this.previousMenu&&"function"==typeof this.options.menu.onClose&&this.overlay.opened&&this.timeline.add(this.options.menu.onClose(this.currentMenu,this.previousMenu)),"function"==typeof this.options.menu.onOpen&&this.timeline.add(this.options.menu.onOpen(this.currentMenu,this.previousMenu,this.label),"<50%")),this.timeline.play(),this.timeline.add((()=>{this.opening=!1}),"<80%")}close(t){this.previousMenu=this.currentMenu,this.currentMenu=t,this.timeline.clear(),this.options.menu&&"function"==typeof this.options.menu.onClose&&this.timeline.add(this.options.menu.onClose(this.currentMenu,this.previousMenu)),this.timeline.play(),this.timeline.add((()=>{this.opening=!1}),"<80%")}get menu(){return this._menu}set menu(t){this._menu=t}get submenuBackButton(){return this._submenuBackButton}set submenuBackButton(t){this._submenuBackButton=t}get submenuLabel(){return this._submenuLabel}set submenuLabel(t){this._submenuLabel=t}_updateElements(){if(this.options.menu&&s.isHTMLElement(this.containerElement)){if("string"==typeof this.options.menu.submenuBackButtonSelector){const t=s.getElementsInContainer(this.containerElement,this.options.menu.submenuBackButtonSelector);t&&s.isHTMLElement(t[0])&&(this.submenuBackButton=t[0])}if("string"==typeof this.options.menu.submenuLabelSelector){const t=s.getElementsInContainer(this.containerElement,this.options.menu.submenuLabelSelector);t&&t[0]&&(this.submenuLabel=t[0])}if("string"==typeof this.options.menu.menuSelector){const t=s.getElementsInContainer(this.containerElement,this.options.menu.menuSelector);t&&t[0]&&(this.menu=t[0])}}}get opening(){return this._opening}set opening(t){this.options.menu&&"string"==typeof this.options.menu.toggleOpeningClass&&a(this.containerElement,this.options.menu.toggleOpeningClass,t),this._opening=t}get timeline(){return this._timeline}set timeline(t){this._timeline=t}get overlay(){return this._overlay}set overlay(t){this._overlay=t}get label(){return this._label}set label(t){this.submenuLabel&&(this.submenuLabel.innerHTML=t),this._label=t}get currentMenu(){return this._currentMenu}set currentMenu(t){if(this.menu){if(this.options.menu&&"string"==typeof this.options.menu.toggleCurrentMenuClass){const e=this.options.menu.toggleCurrentMenuClass;[this.menu,...this.menu.getElementsByClassName(e)].forEach((t=>t.classList.remove(e))),t&&(t.classList.add(e),"string"==typeof this.options.menu.toggleSubmenuOpenedClass&&a(this.containerElement,this.options.menu.toggleSubmenuOpenedClass,!0))}t===this.menu?(this.topLevel=!0,this.options.menu&&"string"==typeof this.options.menu.toggleSubmenuOpenedClass&&a(this.containerElement,this.options.menu.toggleSubmenuOpenedClass,!1)):this.topLevel=!1,this._currentMenu=t}}get previousMenu(){return this._previousMenu}set previousMenu(t){this._previousMenu=t}get topLevel(){return this._topLevel}set topLevel(t){this._topLevel=t}_attachEvents(){this.menu&&this.menu.addEventListener("click",this._handlers.onSubmenuOpenersClick),this.submenuBackButton&&this.submenuBackButton.addEventListener("click",this._handlers.onSubmenuBackClick)}_detachEvents(){this.menu&&this.menu.removeEventListener("click",this._handlers.onSubmenuOpenersClick),this.submenuBackButton&&this.submenuBackButton.removeEventListener("click",this._handlers.onSubmenuBackClick)}_onSubmenuOpenersClick(t){const e=t.target;if(s.isHTMLElement(e)&&this._isSubmenuOpener(e)){if(t.preventDefault(),this._shouldPreventSubmenuOpen())return;this.open(e.nextElementSibling)}}_onSubmenuBackClick(t){if(t.preventDefault(),this._shouldPreventSubmenuOpen()||this.topLevel)return;const e=this.currentMenu&&this.currentMenu.parentElement?this.currentMenu.parentElement.parentElement:null;e&&this.open(e)}_isSubmenuOpener(t){if(!t)return!1;const e=t.nextElementSibling;return"A"===t.tagName&&e&&"UL"===e.tagName}_shouldPreventSubmenuOpen(){return this.opening||this.overlay.animating||!this.overlay.opened}}class g extends r{constructor({containerElement:t,overlay:e,callback:i,options:n}){super({autoload:!1,containerElement:t,options:n}),this._active=!1,this._handlers={onClick:this._onClick.bind(this)},this.callback=i,e&&(this.overlay=e)}init(){return new Promise((t=>{this.enabled||(this._updateElement(),this.element&&(this._attachEvents(),this.enabled=!0)),t(!0)}))}destroy(){return new Promise((t=>{this.enabled?(this._detachEvents(),this.active=!1,this.enabled=!1,t(!0)):t(!0)}))}get active(){return this._active}set active(t){this.element&&this.options.switcher&&"string"==typeof this.options.switcher.toggleActiveClass&&a(this.element,this.options.switcher.toggleActiveClass,t),this._active=t}get element(){return this._element}set element(t){this._element=t}_updateElement(){if(this.options.switcher&&"string"==typeof this.options.switcher.elementSelector){const t=s.getElementsInContainer(this.containerElement,this.options.switcher.elementSelector);t&&t[0]&&(this.element=t[0])}}get overlay(){return this._overlay}set overlay(t){this._overlay=t}get callback(){return this._callback}set callback(t){this._callback=t}_attachEvents(){this.element.addEventListener("click",this._handlers.onClick)}_detachEvents(){this.element.removeEventListener("click",this._handlers.onClick)}_onClick(t){t.preventDefault(),"function"==typeof this.callback&&this.callback()}}class p extends r{constructor({containerElement:t,overlay:e,callback:i,options:n}){super({autoload:!1,containerElement:t,options:n}),this._handlers={click:this._onClick.bind(this)},this.callback=i,e&&(this.overlay=e)}init(){return new Promise((t=>{this.enabled||(this._attachEvents(),this.enabled=!0),t(!0)}))}destroy(){return new Promise((t=>{this.enabled?(this._detachEvents(),this.enabled=!1,t(!0)):t(!0)}))}get overlay(){return this._overlay}set overlay(t){this._overlay=t}get callback(){return this._callback}set callback(t){this._callback=t}_attachEvents(){this.containerElement.addEventListener("click",this._handlers.click)}_detachEvents(){this.containerElement.removeEventListener("click",this._handlers.click)}_onClick(t){const e=t.target;if(s.isHTMLElement(e)){const i=this._getPageElementAnchor(e);i&&(t.preventDefault(),this.options.anchors&&(this.options.anchors.autoCloseOverlay&&"function"==typeof this.options.anchors.onClick?this.overlay.opened?this.callback(!1,!1,this.options.anchors.onClick.bind(this,i)):this.options.anchors.onClick(i):this.options.anchors.autoCloseOverlay||"function"!=typeof this.options.anchors.onClick?this.options.anchors.autoCloseOverlay&&!this.options.anchors.onClick&&this.callback(!1,!1):this.options.anchors.onClick(i)))}}_getPageElementAnchor(t){const e=t.getAttribute("href");return"string"==typeof e&&this._isValidAnchor(e)?this._getAnchorTarget(e):null}_isValidAnchor(t){return t&&"#"!==t&&!t.includes("elementor-action")}_getAnchorTarget(t){const e=t.lastIndexOf("#");let i;return-1!==e&&(i=t.slice(e+1)),"string"==typeof i?document.getElementById(i):null}}class m{constructor({elements:t,callback:e,callbackDebounced:i}){this._handlers={update:this._onUpdate.bind(this)},this.elements=t,this.callback=e,"function"==typeof i&&(this.callbackDebounced=i),this.elements.length&&this._hasCallback()&&this.init()}set instance(t){this._instance=t}get instance(){return this._instance}set callback(t){this._callback=t}get callback(){return this._callback}set callbackDebounced(t){this._callbackDebounced=t}get callbackDebounced(){return this._callbackDebounced}set elements(t){this._elements=t}get elements(){return this._elements}init(){this.instance=new ResizeObserver(this._handlers.update),this._observeElements()}destroy(){this.instance&&this.instance.disconnect()}_onUpdate(t){const e=[];for(const i of t)e.push(i.target);this.callback(e),"function"==typeof this.callbackDebounced&&this.callbackDebounced(e)}_observeElements(){if(this.instance)for(let t=0;t<this.elements.length;t++)this.instance.observe(this.elements[t])}_hasCallback(){return"function"==typeof this.callback}}class d extends r{constructor({containerElement:t,hasSmoothScrolling:e,options:i}){super({autoload:!1,containerElement:t,options:i}),this._value=0,this._hasSmoothScrolling=!1,this.hasSmoothScrolling=e,this._updateContainerElement()}init(){return new Promise((t=>{this.enabled?t(!0):this._updateValue().then((()=>this._updateCSSVar())).then((()=>{!this.resize&&this.options.heightObserver&&this.options.heightObserver.observe&&this._updateResize(),this.options.heightObserver&&"string"==typeof this.options.heightObserver.updateCSSVar&&this.options.heightObserver.updateCSSVar.length&&document.documentElement.classList.add("has-header-height"),this.enabled=!0,t(!0)}))}))}destroy(){return new Promise((t=>{this.enabled?(this.resize&&this.resize.destroy(),this.options.heightObserver&&this.options.heightObserver.cleanupOnDestroy&&"string"==typeof this.options.heightObserver.updateCSSVar&&this.options.heightObserver.updateCSSVar.length&&(document.documentElement.style.removeProperty(this.options.heightObserver.updateCSSVar),document.documentElement.classList.remove("has-header-height")),this.enabled=!1,t(!0)):t(!0)}))}_updateContainerElement(){if(this.options.heightObserver&&"string"==typeof this.options.heightObserver.containerSelector){const t=s.getElementsInContainer(this.containerElement,this.options.heightObserver.containerSelector);t&&t[0]&&(this.containerElement=t[0])}}get value(){return this._value}set value(t){const e=this.value;this._value=t,e!==t&&this._updateCSSVar()}_updateValue(){return new Promise((t=>{this.value=this.containerElement.offsetHeight,t(!0)}))}get resize(){return this._resize}set resize(t){this._resize=t}_updateResize(){this.resize=new m({elements:[this.containerElement],callback:this._updateValue.bind(this)})}_updateCSSVar(){return new Promise((t=>{this.options.heightObserver&&"string"==typeof this.options.heightObserver.updateCSSVar&&this.options.heightObserver.updateCSSVar.length&&(document.documentElement.style.setProperty(this.options.heightObserver.updateCSSVar,`${this.value}px`),this._updateScrollSmoother()),t(!0)}))}_updateScrollSmoother(){this.hasSmoothScrolling&&"undefined"!=typeof ScrollSmoother&&ScrollSmoother.refresh(!0)}get hasSmoothScrolling(){return this._hasSmoothScrolling}set hasSmoothScrolling(t){this._hasSmoothScrolling=t}}class b{constructor({condition:t,callbackMatch:e,callbackNoMatch:i}){this._handlers={change:this._onChange.bind(this)},this.condition=t,this.callbacks={match:e,noMatch:i},"function"!=typeof this.callbacks.match&&"function"!=typeof this.callbacks.noMatch||this.init()}init(){this.mediaQuery=this._addMatchMedia(),this._attachEvents()}destroy(){this._detachEvents()}get mediaQuery(){return this._mediaQuery}set mediaQuery(t){this._mediaQuery=t}get callbacks(){return this._callbacks}set callbacks(t){this._callbacks=t}get condition(){return this._condition}set condition(t){this._condition=t}_addMatchMedia(){return window.matchMedia(`${this.condition}`)}_attachEvents(){"function"==typeof this.mediaQuery.addEventListener?this.mediaQuery.addEventListener("change",this._handlers.change):this.mediaQuery.addListener(this._handlers.change)}_detachEvents(){"function"==typeof this.mediaQuery.removeEventListener?this.mediaQuery.removeEventListener("change",this._handlers.change):this.mediaQuery.removeListener(this._handlers.change)}_onChange(t){t.matches?"function"==typeof this.callbacks.match&&this.callbacks.match():t.matches||"function"==typeof this.callbacks.noMatch&&this.callbacks.noMatch()}}i(264),i(979);const y=class extends class{constructor({container:t,options:e={}}){this._enabled=!1,this._initialized=!1,t&&e&&(this._updateContainerElement(t),this._updateOptions(this.containerElement,e),this._updateSmoothScrollingEnabled())}init(){}destroy(){}update(){this.sticky&&"function"==typeof this.sticky.update&&this.sticky.update()}get enabled(){return this._enabled}set enabled(t){this._enabled=t}get initialized(){return this._initialized}set initialized(t){this._initialized=t}get containerElement(){return this._containerElement}set containerElement(t){this._containerElement=t}_updateContainerElement(t){const e=s.getElementByStringSelector(t);s.isHTMLElement(e)&&(this.containerElement=e)}get sticky(){return this._sticky}set sticky(t){this._sticky=t}get absolute(){return this._absolute}set absolute(t){this._absolute=t}get overlay(){return this._overlay}set overlay(t){this._overlay=t}get menu(){return this._menu}set menu(t){this._menu=t}get switcher(){return this._switcher}set switcher(t){this._switcher=t}get anchors(){return this._anchors}set anchors(t){this._anchors=t}get heightObserver(){return this._heightObserver}set heightObserver(t){this._heightObserver=t}_initPlugins(){return new Promise((t=>{const e=[];this.options.sticky&&(this.sticky||(this.sticky=new l({containerElement:this.containerElement,options:this.options,attributes:this.options.sticky.toggleAttributes})),e.push(this.sticky.init())),this.options.absolute&&(this.absolute||(this.absolute=new h({containerElement:this.containerElement,options:this.options})),e.push(this.absolute.init())),this.options.overlay&&(this.overlay||(this.overlay=new c({containerElement:this.containerElement,options:this.options,attributes:this.options.overlay.toggleAttributes})),e.push(this.overlay.init())),this.options.menu&&(this.menu||(this.menu=new u({containerElement:this.containerElement,options:this.options,overlay:this.overlay})),e.push(this.menu.init())),this.options.switcher&&(this.switcher||(this.switcher=new g({containerElement:this.containerElement,options:this.options,overlay:this.overlay,callback:this.toggleOverlay.bind(this)})),e.push(this.switcher.init())),this.options.anchors&&!this.anchors&&(this.anchors=new p({containerElement:this.containerElement,options:this.options,overlay:this.overlay,callback:this.toggleOverlay.bind(this)}),e.push(this.anchors.init())),this.options.heightObserver&&(this.heightObserver=new d({containerElement:this.containerElement,hasSmoothScrolling:this.smoothScrollingEnabled,options:this.options}),e.push(this.heightObserver.init())),Promise.all(e).then((()=>t(!0))).catch((()=>t(!0)))}))}_destroyPlugins(){return new Promise((t=>{const e=[];this.sticky&&e.push(this.sticky.destroy()),this.absolute&&e.push(this.absolute.destroy()),this.overlay&&e.push(this.overlay.destroy()),this.menu&&e.push(this.menu.destroy()),this.switcher&&e.push(this.switcher.destroy()),this.heightObserver&&e.push(this.heightObserver.destroy()),Promise.all(e).then((()=>t(!0))).catch((()=>t(!0)))}))}get options(){return this._options}set options(t){this._options=t}_updateOptions(t,e){this.options=new o({container:t,attributeSelector:"data-arts-header-options",options:e}).data}get matchMedia(){return this._matchMedia}set matchMedia(t){this._matchMedia=t}toggleOverlay(t=!this.overlay.opened,e=!1,i){!e&&(this.overlay.animating||this.menu&&this.menu.opening)||(this.overlay&&(t&&!this.overlay.opened&&(this.switcher&&(this.switcher.active=!0),this.overlay.animateOverlayOpen(i)),!t&&this.overlay.opened&&(this.switcher&&(this.switcher.active=!1),this.overlay.animateOverlayClose(i))),this.menu&&(t?(this.menu.set(),this.menu.open()):this.menu.close()),this.sticky&&!this.sticky.locked&&"function"==typeof this.sticky.disable&&"function"==typeof this.sticky.enable&&(t?this.sticky.disable():this.sticky.enable()))}get opened(){if(this.overlay)return this.overlay.opened}get animating(){if(this.overlay)return this.overlay.animating}get sticking(){if(this.sticky)return this.sticky.sticking}get currentMenuElement(){if(this.menu)return this.menu.currentMenu}get currentMenuIsTopLevel(){if(this.menu)return this.menu.topLevel}get smoothScrollingEnabled(){return this._smoothScrollingEnabled}set smoothScrollingEnabled(t){this._smoothScrollingEnabled=t}_updateSmoothScrollingEnabled(){"function"==typeof this.options.hasSmoothScrolling?this.smoothScrollingEnabled=this.options.hasSmoothScrolling():this.smoothScrollingEnabled=Boolean(this.options.hasSmoothScrolling)}}{constructor(t=document.querySelector('[data-arts-header="container"]'),e={}){super({container:t,options:e}),this.ready=new Promise((t=>{this.setReady=t})),this.options.init&&(this.options.matchMedia&&!window.matchMedia(`${this.options.matchMedia}`).matches?this.matchMedia=new b({condition:this.options.matchMedia,callbackMatch:this.init.bind(this)}):this.init())}init(){return new Promise((t=>{this.initialized||(this.matchMedia&&this.matchMedia.destroy(),this.options.matchMedia&&(this.matchMedia=new b({condition:this.options.matchMedia,callbackMatch:this.init.bind(this),callbackNoMatch:this.destroy.bind(this)})),this._initPlugins().then((()=>{this.initialized=!0,this.enabled=!0,this.setReady(),t(!0)})))}))}destroy(){return new Promise((t=>{this._destroyPlugins().then((()=>{this.enabled=!1,this.initialized=!1,t(!0)}))}))}setReady(){}}})(),this.ArtsHeader=n.default})();