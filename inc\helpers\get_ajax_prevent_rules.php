<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_ajax_prevent_rules' ) ) {
	/**
	 * Retrieves the string of CSS selectors that prevent AJAX transitions from
	 * specified pages or elements.
	 *
	 * @return string A string of CSS selectors to prevent AJAX transitions.
	 */
	function arts_get_ajax_prevent_rules() {
		$ajax_prevent_rules             = rtrim( Utilities::get_kit_settings( 'ajax_prevent_rules', '' ), ',' ); // Remove all commas off the string
		$ajax_prevent_woocommerce_pages = Utilities::get_kit_settings( 'ajax_prevent_woocommerce_pages', true );

		if ( $ajax_prevent_woocommerce_pages && class_exists( 'WooCommerce' ) && function_exists( 'arts_get_woocommerce_urls' ) ) {
			// Add AJAX rules that prevents all "TO" WooCommerce pages
			$woocommerce_urls        = Utilities::get_woocommerce_urls();
			$woocommerce_urls_string = '';

			foreach ( $woocommerce_urls as $url ) {
				if ( ! empty( $url ) ) {
					$woocommerce_urls_string .= 'a[href*="' . $url . '"],';
				}
			}

			$ajax_prevent_rules .= $woocommerce_urls_string;

			// Add AJAX rule that prevents all the links "FROM" WooCommerce pages to other website pages
			$ajax_prevent_rules .= '.woocommerce-page a';
		}

		return preg_replace( '/\s+/S', ' ', $ajax_prevent_rules );
	}
}
