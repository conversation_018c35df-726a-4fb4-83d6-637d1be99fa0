<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'ProteusThemes\\WPContentImporter2\\' => array($vendorDir . '/arts/wp-content-importer-v2/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Asli\\Theme\\' => array('/'),
    'Arts\\WizardSetup\\' => array($vendorDir . '/arts/wizard-setup/src'),
    'Arts\\Utilities\\' => array($vendorDir . '/arts/utilities/src/php'),
    'Arts\\NoticeManager\\' => array($vendorDir . '/arts/notice-manager/src/php'),
    'Arts\\Merlin\\' => array($vendorDir . '/arts/merlin-wp/src'),
    'Arts\\LicenseManager\\' => array($vendorDir . '/arts/license-manager/src/php'),
);
