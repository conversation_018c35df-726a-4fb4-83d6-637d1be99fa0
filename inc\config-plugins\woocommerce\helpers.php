<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_is_shop' ) ) {
	/**
	 * Check if the current page is the shop page.
	 *
	 * @return bool
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_shop()` method instead.
	 */
	function arts_is_shop() {
		return Utilities::is_shop();
	}
}

if ( ! function_exists( 'arts_is_product_category' ) ) {
	/**
	 * Check if the current page is a product category archive page.
	 *
	 * @param string $term Optional. The term to check. Defaults to empty string.
	 *
	 * @return bool
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_product_category()` method instead.
	 */
	function arts_is_product_category( $term = '' ) {
		return Utilities::is_product_category( $term );
	}
}

if ( ! function_exists( 'arts_is_product_tag' ) ) {
	/**
	 * Check if the current page is a product tag archive page.
	 *
	 * @param string $term Optional. The term to check. Defaults to empty string.
	 *
	 * @return bool
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_product_tag()` method instead.
	 */
	function arts_is_product_tag() {
		return function_exists( 'is_product_tag' ) && is_product_tag();
	}
}

if ( ! function_exists( 'arts_is_woocommerce' ) ) {
	/**
	 * Check if the current page is a WooCommerce page.
	 *
	 * @return bool
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_woocommerce()` method instead.
	 */
	function arts_is_woocommerce() {
		return Utilities::is_woocommerce();
	}
}

if ( ! function_exists( 'arts_is_checkout' ) ) {
	/**
	 * Check if the current page is the checkout page.
	 *
	 * @return bool
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_checkout()` method instead.
	 */
	function arts_is_checkout() {
		return Utilities::is_checkout();
	}
}

if ( ! function_exists( 'arts_is_cart' ) ) {
	/**
	 * Check if the current page is the cart page.
	 *
	 * @return bool
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_cart()` method instead.
	 */
	function arts_is_cart() {
		return Utilities::is_cart();
	}
}

if ( ! function_exists( 'arts_is_account_page' ) ) {
	/**
	 * Check if the current page is the account page.
	 *
	 * @return bool
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_account_page()` method instead.
	 */
	function arts_is_account_page() {
		return Utilities::is_account_page();
	}
}

if ( ! function_exists( 'arts_is_order_received_page' ) ) {
	/**
	 * Check if the current page is the order received page.
	 *
	 * @return bool
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::is_order_received_page()` method instead.
	 */
	function arts_is_order_received_page() {
		return Utilities::is_order_received_page();
	}
}
