<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'wp_ajax_post_review', 'arts_ajax_post_review' );
add_action( 'wp_ajax_nopriv_post_review', 'arts_ajax_post_review' );
if ( ! function_exists( 'arts_ajax_post_review' ) ) {
	function arts_ajax_post_review() {
		check_ajax_referer( 'post_comment' );

		if ( ! isset( $_POST['rating'] ) || ! isset( $_POST['comment_post_ID'] ) ) {
			wp_die();
			return;
		}

		$comment_data                 = $_POST;
		$comment_data['comment_type'] = '';

		WC_Comments::check_comment_rating( $comment_data );

		$comment_post_id = absint( $_POST['comment_post_ID'] );
		$rating          = intval( $_POST['rating'] );
		$post_type       = get_post_type( $comment_post_id );

		if ( $post_type === 'product' ) { // WPCS: input var ok, CSRF ok.
			if ( ! $rating || $rating > 5 || $rating < 0 ) { // WPCS: input var ok, CSRF ok, sanitization ok.
				wp_die();
				return;
			}

			// Post comment
			$comment = wp_handle_comment_submission( wp_unslash( $comment_data ) );
			WC_Comments::add_comment_rating( $comment->comment_ID );

			if ( is_wp_error( $comment ) ) {
				$data = (int) $comment->get_error_data();

				if ( ! empty( $data ) ) {
					wp_die( $comment->get_error_message(), '', array( 'response' => $data ) );
				} else {
					exit;
				}
			}

			$user            = wp_get_current_user();
			$cookies_consent = ( isset( $_POST['wp-comment-cookies-consent'] ) );

			do_action( 'set_comment_cookies', $comment, $user, $cookies_consent );

			$GLOBALS['comment']    = $comment;
			$GLOBALS['comment_id'] = get_comment_ID();

			// If user didn't consent to cookies, add specific query arguments to display the awaiting moderation message.
			if ( ! $cookies_consent && 'unapproved' === wp_get_comment_status( $comment ) && ! empty( $comment->comment_author_email ) ) {
				$_GET['unapproved']      = $comment->comment_ID;
				$_GET['moderation-hash'] = wp_hash( $comment->comment_date_gmt );
			}

			// Markup an added comment with class
			add_filter( 'comment_class', 'arts_add_ajax_comment_class', 10, 5 );

			if ( $comment_post_id ) {
				WC_Comments::clear_transients( $comment_post_id );
			}
		}

		$query_args = array(
			'post_type'      => $post_type,
			'p'              => $comment_post_id,
			'posts_per_page' => -1,
		);

		$loop = new WP_Query( $query_args );

		add_action(
			'comment_form',
			function( $post_id ) {
				$fragments = array();
				$tabs      = woocommerce_default_product_tabs();

				if ( array_key_exists( 'reviews', $tabs ) && array_key_exists( 'title', $tabs['reviews'] ) ) {
					$fragments [] = array(
						'selector' => '.woocommerce-tabs a[href="#tab-reviews"]',
						'method'   => 'text',
						'content'  => $tabs['reviews']['title'],
					);
				}

				ob_start();
				wc_get_template( 'single-product/rating.php' );
				$output = ob_get_clean();

				$fragments[] = array(
					'selector' => '.woocommerce-product-rating',
					'method'   => 'replaceWith',
					'content'  => $output,
				);

				echo '<script id="arts-rating-fragments" type="application/json">' . json_encode( $fragments ) . '</script>';
			},
			10
		);

		if ( $loop->have_posts() ) {
			while ( $loop->have_posts() ) {
				$loop->the_post();

				global $withcomments;
				$withcomments = true;

				// Echo updated comments template
				comments_template();
			}

			wp_reset_postdata();
		}

		wp_die();
	}
}
