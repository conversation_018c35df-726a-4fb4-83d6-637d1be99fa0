#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Asli\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-16 06:23+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.6; wp-6.4.2\n"
"X-Domain: asli"

#: template-parts/blog/post/partials/meta.php:45
msgid "% Comments"
msgstr ""

#. 1: comment date, 2: comment time
#: inc/comments/class-arts-walker-comment.php:71
#, php-format
msgid "%1$s at %2$s"
msgstr ""

#. 1: Title Tag 2: Theme Name 3: Closing Title Tag
#: inc/import/merlin/merlin-config.php:26
#, php-format
msgid "%1$s%2$s Themes &lsaquo; Theme Setup: %3$s%4$s"
msgstr ""

#. Theme Name
#: inc/import/merlin/merlin-config.php:42
#, php-format
msgid "%s is Activated"
msgstr ""

#: inc/constants.php:16 inc/constants.php:24 inc/constants.php:32
#: inc/constants.php:40 inc/constants.php:48 inc/constants.php:58
#: inc/constants.php:73 inc/constants.php:88 inc/constants.php:96
#: inc/constants.php:104
msgid "+ Large"
msgstr ""

#: inc/constants.php:15 inc/constants.php:23 inc/constants.php:31
#: inc/constants.php:39 inc/constants.php:47 inc/constants.php:57
#: inc/constants.php:72 inc/constants.php:87 inc/constants.php:95
#: inc/constants.php:103
msgid "+ Medium"
msgstr ""

#: inc/constants.php:14 inc/constants.php:22 inc/constants.php:30
#: inc/constants.php:38 inc/constants.php:46 inc/constants.php:56
#: inc/constants.php:71 inc/constants.php:86 inc/constants.php:94
#: inc/constants.php:102
msgid "+ Small"
msgstr ""

#: inc/constants.php:17 inc/constants.php:25 inc/constants.php:33
#: inc/constants.php:41 inc/constants.php:49 inc/constants.php:59
#: inc/constants.php:74 inc/constants.php:89 inc/constants.php:97
#: inc/constants.php:105
msgid "+ XLarge"
msgstr ""

#: inc/constants.php:13 inc/constants.php:21 inc/constants.php:29
#: inc/constants.php:37 inc/constants.php:45 inc/constants.php:55
#: inc/constants.php:70 inc/constants.php:85 inc/constants.php:93
#: inc/constants.php:101
msgid "+ XSmall"
msgstr ""

#: inc/constants.php:65 inc/constants.php:80
msgid "- Large"
msgstr ""

#: inc/constants.php:64 inc/constants.php:79
msgid "- Medium"
msgstr ""

#: inc/constants.php:63 inc/constants.php:78
msgid "- Small"
msgstr ""

#: inc/constants.php:66 inc/constants.php:81
msgid "- XLarge"
msgstr ""

#: inc/constants.php:62 inc/constants.php:77
msgid "- XSmall"
msgstr ""

#: template-parts/blog/post/partials/meta.php:45
msgid "1 Comment"
msgstr ""

#: inc/import/merlin/merlin-config.php:37
msgid "Activate"
msgstr ""

#. Theme Name
#: inc/import/merlin/merlin-config.php:40
#, php-format
msgid "Activate %s"
msgstr ""

#: inc/config-plugins/acf/fields.php:8
msgid "Additional Content"
msgstr ""

#: inc/config-plugins/acf/fields.php:172
msgid "Additional Featured Media"
msgstr ""

#: inc/config-plugins/acf/fields.php:256
msgid "Additional Media Fields"
msgstr ""

#: inc/import/merlin/merlin-config.php:65
#: inc/import/merlin/merlin-config.php:68
msgid "Advanced"
msgstr ""

#: inc/tgm/load_plugins.php:37
msgid "Advanced Custom Fields PRO"
msgstr ""

#. Description of the theme
msgid "AJAX Portfolio Elementor Theme"
msgstr ""

#: inc/import/merlin/merlin-config.php:69
msgid "All done. Have fun!"
msgstr ""

#: inc/core/widget_areas.php:14
msgid "Appears in Blog"
msgstr ""

#. Author of the theme
msgid "Artem Semkin"
msgstr ""

#. Name of the theme
msgid "Asli"
msgstr ""

#: inc/tgm/load_plugins.php:45
msgid "Asli Core"
msgstr ""

#: inc/import/setup-filters.php:12
msgid "Asli Demo Data"
msgstr ""

#: template-parts/preloader/preloader.php:14
msgid "Asli Wells"
msgstr ""

#: inc/constants.php:12 inc/constants.php:20 inc/constants.php:28
#: inc/constants.php:36 inc/constants.php:44 inc/constants.php:54
#: inc/constants.php:69 inc/constants.php:84 inc/constants.php:92
#: inc/constants.php:100 inc/constants.php:155
msgid "Auto"
msgstr ""

#: inc/import/merlin/merlin-config.php:59
msgid ""
"Awesome. Your child theme has already been installed and is now activated."
msgstr ""

#: inc/import/merlin/merlin-config.php:60
msgid "Awesome. Your child theme has been created and is now activated."
msgstr ""

#: template-parts/header/fullscreen-overlay-container/partials/submenu-back.php:8
#: template-parts/header/fullscreen-overlay-container/partials/submenu-back.php:9
msgid "Back"
msgstr ""

#: inc/helpers/get_page_titles.php:66
msgid "Blog"
msgstr ""

#: inc/core/widget_areas.php:12
msgid "Blog Sidebar"
msgstr ""

#: inc/constants.php:159
msgid "Bottom Arc"
msgstr ""

#: inc/constants.php:140
msgid "Boxed"
msgstr ""

#: template-parts/blog/post/partials/meta.php:51
msgid "by"
msgstr ""

#: inc/import/merlin/merlin-config.php:32
msgid "Cancel"
msgstr ""

#: template-parts/header/top-bar/partials/burger.php:6
#: template-parts/header/top-bar/partials/burger.php:7
msgid "Close"
msgstr ""

#: comments.php:49
msgid "Comment"
msgstr ""

#: comments.php:51
msgid "Comments"
msgstr ""

#: comments.php:71
msgid "Comments are closed."
msgstr ""

#: inc/tgm/load_plugins.php:53
msgid "Contact Form 7"
msgstr ""

#: inc/helpers/get_page_titles.php:39
msgid "Day archive"
msgstr ""

#: inc/config-plugins/acf/fields.php:31
msgid "Description"
msgstr ""

#: inc/import/merlin/merlin-config.php:28
msgid "Disable this wizard"
msgstr ""

#: inc/comments/class-arts-walker-comment.php:26
#: inc/comments/class-arts-walker-comment.php:96
msgid "Edit"
msgstr ""

#: inc/tgm/load_plugins.php:59
msgid "Elementor"
msgstr ""

#: inc/tgm/load_plugins.php:86
msgid "Elementor Pro"
msgstr ""

#: inc/comments/comment_form_default_fields.php:53
msgid "Email"
msgstr ""

#. Theme Name
#: inc/import/merlin/merlin-config.php:44
msgid "Enter your license key to enable remote updates and theme support."
msgstr ""

#: inc/tgm/load_plugins.php:65
msgid "Envato Market"
msgstr ""

#: 404.php:81
msgid "Error 404"
msgstr ""

#: inc/import/merlin/merlin-config.php:74
msgid "Explore WordPress"
msgstr ""

#: inc/config-plugins/acf/fields.php:260
msgid "External URL"
msgstr ""

#: inc/import/merlin/merlin-config.php:72
msgid "Extras"
msgstr ""

#: inc/config-plugins/acf/fields.php:200
msgid "Featured Video"
msgstr ""

#: template-parts/preloader/preloader.php:10
msgid "Filmmaker & Photographer"
msgstr ""

#: inc/constants.php:141
msgid "Fullwidth / Bootstrap gutters"
msgstr ""

#: inc/constants.php:143
msgid "Fullwidth / No gutters"
msgstr ""

#: inc/constants.php:142
msgid "Fullwidth / Theme gutters"
msgstr ""

#: template-parts/blog/post/content/content-none.php:10
msgid "Get started here!"
msgstr ""

#: inc/import/merlin/merlin-config.php:75
msgid "Get Theme Support"
msgstr ""

#: inc/import/merlin/merlin-config.php:51
msgid "Hi. Welcome back"
msgstr ""

#. Author URI of the theme
msgid "https://artemsemkin.com"
msgstr ""

#. URI of the theme
msgid "https://artemsemkin.com/asli/wp/"
msgstr ""

#: inc/import/merlin/merlin-config.php:36
msgid "Import"
msgstr ""

#: inc/import/merlin/merlin-config.php:66
msgid "Import Content"
msgstr ""

#: inc/import/merlin/merlin-config.php:33
#: inc/import/merlin/merlin-config.php:34
#: inc/import/merlin/merlin-config.php:35
msgid "Install"
msgstr ""

#: inc/import/merlin/merlin-config.php:54
msgid "Install Child Theme"
msgstr ""

#: inc/import/merlin/merlin-config.php:61
msgid "Install Plugins"
msgstr ""

#: inc/config-plugins/acf/fields.php:263
msgid "Internal Page or External Media Source (YouTube, Vimeo, etc)"
msgstr ""

#: inc/tgm/load_plugins.php:72
msgid "Intuitive Custom Post Order"
msgstr ""

#: 404.php:89
msgid ""
"It looks like nothing found here. Try to navigate the menu or return to the "
"home page."
msgstr ""

#: template-parts/blog/post/content/content-none.php:15
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""

#: inc/import/merlin/merlin-config.php:38
msgid "Later"
msgstr ""

#: inc/import/merlin/merlin-config.php:58
msgid "Learn about child themes"
msgstr ""

#: inc/import/merlin/merlin-config.php:56
msgid ""
"Let's build & activate a child theme so you may easily make theme changes."
msgstr ""

#: inc/import/merlin/merlin-config.php:67
msgid ""
"Let's import content to your website, to help you get familiar with the "
"theme."
msgstr ""

#: inc/import/merlin/merlin-config.php:63
msgid ""
"Let's install some essential WordPress plugins to get your site up to speed."
msgstr ""

#: inc/import/merlin/merlin-config.php:45
msgid "License key"
msgstr ""

#: template-parts/preloader/preloader.php:6
msgid "Loading..."
msgstr ""

#: inc/navigation/register_menus.php:11
msgid "Main Menu"
msgstr ""

#: inc/config-plugins/elementor/add_custom_icons_elementor_library.php:11
msgid "Material Icons"
msgstr ""

#: template-parts/header/top-bar/partials/burger.php:4
msgid "Menu"
msgstr ""

#: inc/helpers/get_page_titles.php:44
msgid "Month archive"
msgstr ""

#: inc/comments/comment_form_default_fields.php:43
#: inc/config-plugins/acf/fields.php:109
msgid "Name"
msgstr ""

#: inc/import/merlin/merlin-config.php:48
msgid "Need help?"
msgstr ""

#: inc/import/merlin/merlin-config.php:30
msgid "Next"
msgstr ""

#: template-parts/blog/post/partials/meta.php:45
msgid "No Comments"
msgstr ""

#: inc/comments/comment_form_defaults.php:27
msgctxt "noun"
msgid "Comment"
msgstr ""

#: template-parts/header/top-bar/partials/burger.php:5
msgid "Open"
msgstr ""

#: inc/constants.php:157
msgid "Oval"
msgstr ""

#: page.php:35 template-parts/blog/post/post-single.php:8
msgid "Pages:"
msgstr ""

#: inc/blog/password_form.php:27
msgid "Password"
msgstr ""

#: inc/comments/class-arts-walker-comment.php:23
msgid "Pingback:"
msgstr ""

#: searchform.php:9
msgctxt "placeholder"
msgid "Search &hellip;"
msgstr ""

#: inc/comments/comment_form_default_fields.php:52
msgid "Please enter a valid e-mail"
msgstr ""

#: inc/comments/comment_form_default_fields.php:62
msgid "Please enter a valid URL"
msgstr ""

#: inc/comments/comment_form_default_fields.php:52
msgid "Please enter your e-mail"
msgstr ""

#: inc/comments/comment_form_default_fields.php:42
msgid "Please enter your name"
msgstr ""

#: inc/comments/comment_form_defaults.php:27
msgid "Please type your comment text"
msgstr ""

#: inc/comments/ajax_post_comment.php:21
msgid "Please type your comment text."
msgstr ""

#: inc/helpers/get_page_titles.php:29
msgid "Posts by author"
msgstr ""

#: inc/helpers/get_page_titles.php:24
msgid "Posts in category"
msgstr ""

#: inc/pagination/get_posts_pagination.php:16
msgid "Posts navigation"
msgstr ""

#: inc/helpers/get_page_titles.php:34
msgid "Posts with tag"
msgstr ""

#: template-parts/footer/footer.php:27
msgid "Powered by Asli WordPress theme."
msgstr ""

#: inc/tgm/load_plugins.php:92
msgid "PRO Elements"
msgstr ""

#: template-parts/blog/post/post.php:21
msgid "Read More"
msgstr ""

#: template-parts/blog/post/content/content-none.php:10
msgid "Ready to publish your first post?"
msgstr ""

#: inc/import/merlin/merlin-config.php:27
msgid "Return to the dashboard"
msgstr ""

#: inc/constants.php:156
msgid "Rounded Rectangle"
msgstr ""

#: inc/tgm/load_plugins.php:78
msgid "Safe SVG"
msgstr ""

#: template-parts/indicators/rotating-button.php:4
msgid "Scroll Down"
msgstr ""

#: inc/helpers/get_page_titles.php:57
msgid "Search"
msgstr ""

#: inc/config-plugins/acf/fields.php:176
msgid "Secondary Featured Image"
msgstr ""

#: inc/config-plugins/acf/fields.php:87
msgid "Service Properties"
msgstr ""

#: inc/config-plugins/acf/fields.php:203
msgid "Set featured video"
msgstr ""

#: inc/import/merlin/merlin-config.php:29
msgid "Skip"
msgstr ""

#: template-parts/blog/post/content/content-none.php:12
msgid ""
"Sorry, but nothing matched your search terms. Please try again with some "
"different keywords."
msgstr ""

#: inc/import/merlin/merlin-config.php:31
msgid "Start"
msgstr ""

#: inc/import/merlin/merlin-config.php:76
msgid "Start Customizing"
msgstr ""

#: inc/config-plugins/acf/fields.php:12
msgid "Subheading"
msgstr ""

#: 404.php:47 404.php:48
msgid "Take Me Home"
msgstr ""

#: inc/blog/password_form.php:28
msgid "The password you entered is incorrect"
msgstr ""

#: inc/import/merlin/merlin-config.php:64
msgid ""
"The required WordPress plugins are all installed and up to date. Press "
"\"Next\" to continue the setup wizard."
msgstr ""

#: inc/import/merlin/merlin-config.php:46
msgid "The theme is already registered, so you can go to the next step!"
msgstr ""

#: inc/import/merlin/merlin-config.php:24
msgid "Theme Setup"
msgstr ""

#: inc/blog/password_form.php:26
msgid ""
"This content is password protected. To view it please enter your password "
"below:"
msgstr ""

#: inc/import/merlin/merlin-config.php:52
msgid ""
"This wizard will set up your theme, install plugins, and import content. It "
"is optional & should take only a few minutes."
msgstr ""

#: inc/constants.php:158
msgid "Top Arc"
msgstr ""

#: inc/config-plugins/acf/fields.php:128
msgid "Value"
msgstr ""

#: inc/import/merlin/merlin-config.php:73
msgid "View your website"
msgstr ""

#: inc/comments/comment_form_default_fields.php:63
msgid "Website"
msgstr ""

#. Theme Name
#: inc/import/merlin/merlin-config.php:50
#, php-format
msgid "Welcome to %s"
msgstr ""

#: inc/helpers/get_page_titles.php:49
msgid "Year archive"
msgstr ""

#: inc/import/merlin/merlin-config.php:53
msgid ""
"You may have already run this theme setup wizard. If you would like to "
"proceed anyway, click on the \"Start\" button below."
msgstr ""

#: inc/import/merlin/merlin-config.php:55
msgid "You're good to go!"
msgstr ""

#: inc/import/merlin/merlin-config.php:62
msgid "You're up to speed!"
msgstr ""

#: inc/import/merlin/merlin-config.php:57
msgid ""
"Your child theme has already been installed and is now activated, if it "
"wasn't already."
msgstr ""

#: inc/comments/class-arts-walker-comment.php:103
msgid "Your comment is awaiting moderation."
msgstr ""

#. Theme Author
#: inc/import/merlin/merlin-config.php:71
#, php-format
msgid "Your theme has been all set up. Enjoy your new theme by %s."
msgstr ""

#: inc/import/merlin/merlin-config.php:47
msgid "Your theme is activated! Remote updates and theme support are enabled."
msgstr ""
