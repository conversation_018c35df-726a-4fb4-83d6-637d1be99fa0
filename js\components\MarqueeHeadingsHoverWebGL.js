function _0x54dd(_0x3c2658,_0x8abce6){const _0x56ae21=_0x56ae();return _0x54dd=function(_0x54ddf2,_0x51ebeb){_0x54ddf2=_0x54ddf2-0x1f4;let _0xe70325=_0x56ae21[_0x54ddf2];return _0xe70325;},_0x54dd(_0x3c2658,_0x8abce6);}function _0x56ae(){const _0x507fa9=['componentsManager','_initCurtains','\x0a\x09\x09\x09precision\x20mediump\x20float;\x0a\x0a\x09\x09\x09varying\x20vec3\x20vVertexPosition;\x0a\x09\x09\x09varying\x20vec2\x20vTextureCoord;\x0a\x0a\x09\x09\x09uniform\x20float\x20uOpacity;\x0a\x09\x09\x09uniform\x20float\x20uTransition;\x0a\x09\x09\x09uniform\x20sampler2D\x20uSampler0;\x0a\x0a\x09\x09\x09void\x20main()\x20{\x0a\x09\x09\x09\x09//\x20Apply\x20texture\x0a\x09\x09\x09\x09vec4\x20finalColor\x20=\x20texture2D(uSampler0,\x20vTextureCoord);\x0a\x0a\x09\x09\x09\x09//\x20Apply\x20opacity\x0a\x09\x09\x09\x09finalColor.a\x20=\x20uOpacity;\x0a\x0a\x09\x09\x09\x09//\x20Fake\x20shadows\x20based\x20on\x20vertex\x20position\x20along\x20Z\x20axis\x0a\x09\x09\x09\x09finalColor.rgb\x20+=\x20clamp(vVertexPosition.z,\x20-1.0,\x200.0)\x20*\x200.75\x20*\x20uTransition;\x0a\x0a\x09\x09\x09\x09//\x20Fake\x20lights\x20based\x20on\x20vertex\x20position\x20along\x20Z\x20axis\x0a\x09\x09\x09\x09finalColor.rgb\x20+=\x20clamp(vVertexPosition.z,\x200.0,\x201.0)\x20*\x200.75\x20*\x20uTransition;\x0a\x0a\x09\x09\x09\x09//\x20Display\x20texture\x0a\x09\x09\x09\x09gl_FragColor\x20=\x20finalColor;\x0a\x09\x09\x09}\x0a\x09\x09','resize','getComponentByName','default','_getPlaneFragmentShader','clearResize','components','start','options','bind','element','159719uFSKnt','1246473eMnUGU','number','forEach','uniforms','timeline','30JMysIs','finally','96LFvUqr','getDebounceTime','1923042YzjSYm','CurtainsBase','opacity','textures','enableDrawing','disableDrawing','60jXATkj','value','power3.inOut','vertices','visibleUpdate','curtains','length','infiniteListHeadings','_attachEvents','push','39768FPKVch','17259CSdnvJ','utilities','planes','webGL','init','visible','canvasWrapper','itemIdAttribute','\x0a\x09\x09\x09#define\x20PI\x203.1415926535897932384626433832795\x0a\x0a\x09\x09\x09precision\x20mediump\x20float;\x0a\x0a\x09\x09\x09//\x20Default\x20mandatory\x20variables\x0a\x09\x09\x09attribute\x20vec3\x20aVertexPosition;\x0a\x09\x09\x09attribute\x20vec2\x20aTextureCoord;\x0a\x0a\x09\x09\x09uniform\x20mat4\x20uMVMatrix;\x0a\x09\x09\x09uniform\x20mat4\x20uPMatrix;\x0a\x0a\x09\x09\x09uniform\x20mat4\x20uTextureMatrix0;\x0a\x09\x09\x09uniform\x20vec2\x20uPlaneSizes;\x0a\x0a\x09\x09\x09//\x20Custom\x20variables\x0a\x09\x09\x09varying\x20vec3\x20vVertexPosition;\x0a\x09\x09\x09varying\x20vec2\x20vTextureCoord;\x0a\x0a\x09\x09\x09//\x20Custom\x20uniforms\x0a\x09\x09\x09uniform\x20vec2\x20uMousePosition;\x0a\x09\x09\x09uniform\x20vec2\x20uViewportSizes;\x0a\x09\x09\x09uniform\x20float\x20uVelocityX;\x0a\x09\x09\x09uniform\x20float\x20uVelocityY;\x0a\x09\x09\x09uniform\x20float\x20uOpacity;\x0a\x09\x09\x09uniform\x20float\x20uTime;\x0a\x09\x09\x09uniform\x20float\x20uHoverAmplitude;\x0a\x09\x09\x09uniform\x20float\x20uHoverSpeed;\x0a\x09\x09\x09uniform\x20float\x20uHoverSegments;\x0a\x09\x09\x09uniform\x20float\x20uHovered;\x0a\x09\x09\x09uniform\x20float\x20uTransition;\x0a\x09\x09\x09uniform\x20float\x20uElasticEffect;\x0a\x0a\x09\x09\x09void\x20main()\x20{\x0a\x09\x09\x09\x09vec3\x20vertexPosition\x20=\x20aVertexPosition;\x0a\x0a\x09\x09\x09\x09//\x201.\x20Speed\x20Effect\x0a\x09\x09\x09\x09//\x20vertexPosition.y\x20-=\x20sin(vertexPosition.x\x20*\x202.\x20/\x20(uViewportSizes.y)\x20*\x20PI\x20+\x20PI\x20/\x202.0)\x20*\x20uVelocityY\x20*\x20(uPlaneSizes.y\x20/\x202.)\x20*\x20uElasticEffect;\x0a\x09\x09\x09\x09//\x20vertexPosition.x\x20+=\x20sin(vertexPosition.y\x20*\x202.\x20/\x20(uViewportSizes.x)\x20*\x20PI\x20+\x20PI\x20/\x202.0)\x20*\x20uVelocityX\x20*\x20(uPlaneSizes.x\x20/\x202.)\x20*\x20uElasticEffect;\x0a\x0a\x09\x09\x09\x09//\x202.\x20Hover\x20Effect\x0a\x09\x09\x09\x09vertexPosition.z\x20+=\x20sin(vertexPosition.x\x20*\x20(uHoverSegments)\x20+\x20(uTime\x20*\x200.03)\x20+\x20uHoverSpeed)\x20*\x20uHoverAmplitude\x20*\x200.005;\x0a\x09\x09\x09\x09vertexPosition.x\x20+=\x20sin(vertexPosition.y\x20*\x20(uHoverSegments)\x20+\x20(uTime\x20*\x200.03)\x20+\x20uHoverSpeed)\x20*\x20uHoverAmplitude\x20*\x200.005;\x0a\x0a\x09\x09\x09\x09//\x203.\x20Transition\x0a\x09\x09\x09\x09//\x20convert\x20uTransition\x20from\x20[0,1]\x20to\x20[0,1,0]\x0a\x09\x09\x09\x09float\x20transition\x20=\x201.0\x20-\x20abs((uTransition\x20*\x202.0)\x20-\x201.0);\x0a\x0a\x09\x09\x09\x09//\x20Get\x20the\x20distance\x20between\x20our\x20vertex\x20and\x20the\x20mouse\x20position\x0a\x09\x09\x09\x09float\x20distanceFromMouse\x20=\x20distance(uMousePosition,\x20vec2(vertexPosition.x,\x20vertexPosition.y));\x0a\x0a\x09\x09\x09\x09//\x20Calculate\x20our\x20wave\x20effect\x0a\x09\x09\x09\x09float\x20waveSinusoid\x20=\x20cos(6.\x20*\x20(distanceFromMouse\x20-\x20(uTime\x20*\x200.02)));\x0a\x0a\x09\x09\x09\x09//\x20Attenuate\x20the\x20effect\x20based\x20on\x20mouse\x20distance\x0a\x09\x09\x09\x09float\x20distanceStrength\x20=\x20(0.4\x20/\x20(distanceFromMouse\x20+\x200.4));\x0a\x0a\x09\x09\x09\x09//\x20Calculate\x20our\x20distortion\x20effect\x0a\x09\x09\x09\x09float\x20distortionEffect\x20=\x20distanceStrength\x20*\x20waveSinusoid\x20*\x200.33;\x0a\x0a\x09\x09\x09\x09//\x20Apply\x20it\x20to\x20our\x20vertex\x20position\x0a\x09\x09\x09\x09vertexPosition.z\x20+=\x20\x20distortionEffect\x20*\x20-transition;\x0a\x09\x09\x09\x09vertexPosition.x\x20+=\x20\x20distortionEffect\x20*\x20transition\x20*\x20(uMousePosition.x\x20-\x20vertexPosition.x);\x0a\x09\x09\x09\x09vertexPosition.y\x20+=\x20\x20distortionEffect\x20*\x20transition\x20*\x20(uMousePosition.y\x20-\x20vertexPosition.y);\x0a\x0a\x09\x09\x09\x09gl_Position\x20=\x20uPMatrix\x20*\x20uMVMatrix\x20*\x20vec4(vertexPosition,\x201.0);\x0a\x0a\x09\x09\x09\x09//\x20Varyings\x0a\x09\x09\x09\x09vVertexPosition\x20=\x20vertexPosition;\x0a\x09\x09\x09\x09vTextureCoord\x20=\x20(uTextureMatrix0\x20*\x20vec4(aTextureCoord,\x200.0,\x201.0)).xy;\x0a\x09\x09\x09}\x0a\x09\x09','1879262bymtHY','userData','running','debounce','hidePlanes','uOpacity','object','_getCurtainsOptions','postTask','instance','widthSegments','clear','destroy','_onTransitionEnd','heightSegments','_detachEvents','elements','animatePlane','559ooionu','20COfAOI','_getPlaneVertexShader','_onResize','then','postId','scale','attachResponsiveResize','controller','_onVisibleUpdate','firstTextureReady','_handlers','end','1568772XSEdjN','AJAX','445hoUyUr','transitionEnd','off'];_0x56ae=function(){return _0x507fa9;};return _0x56ae();}const _0x32d79c=_0x54dd;(function(_0x98c86b,_0x2719f6){const _0x224feb=_0x54dd,_0x17c1a4=_0x98c86b();while(!![]){try{const _0x4c11b8=-parseInt(_0x224feb(0x232))/0x1*(parseInt(_0x224feb(0x214))/0x2)+-parseInt(_0x224feb(0x1f8))/0x3*(parseInt(_0x224feb(0x242))/0x4)+parseInt(_0x224feb(0x222))/0x5*(-parseInt(_0x224feb(0x1f7))/0x6)+-parseInt(_0x224feb(0x201))/0x7+parseInt(_0x224feb(0x23a))/0x8*(-parseInt(_0x224feb(0x233))/0x9)+-parseInt(_0x224feb(0x238))/0xa*(parseInt(_0x224feb(0x23c))/0xb)+-parseInt(_0x224feb(0x220))/0xc*(-parseInt(_0x224feb(0x213))/0xd);if(_0x4c11b8===_0x2719f6)break;else _0x17c1a4['push'](_0x17c1a4['shift']());}catch(_0xdb9167){_0x17c1a4['push'](_0x17c1a4['shift']());}}}(_0x56ae,0xda0e8));export default class MarqueeHeadingsHoverWebGL{constructor({element:_0x3b658b,elements:_0x2276e6,options:_0x2d3873,infiniteListHeadings:_0x5e81ee}){const _0x2fb35c=_0x54dd;this[_0x2fb35c(0x231)]=_0x3b658b,this[_0x2fb35c(0x211)]=_0x2276e6,this[_0x2fb35c(0x22f)]=_0x2d3873,this[_0x2fb35c(0x1f4)]=_0x5e81ee,this[_0x2fb35c(0x21e)]={'transitionEnd':this[_0x2fb35c(0x20e)][_0x2fb35c(0x230)](this),'visibleUpdate':this[_0x2fb35c(0x21c)][_0x2fb35c(0x230)](this),'resize':app['utilities'][_0x2fb35c(0x204)](this['_onResize'][_0x2fb35c(0x230)](this),app[_0x2fb35c(0x1f9)][_0x2fb35c(0x23b)]())},this['curtainsLoader']=app[_0x2fb35c(0x225)]['load']({'properties':app[_0x2fb35c(0x22d)][_0x2fb35c(0x23d)]});}[_0x32d79c(0x1fc)](){return new Promise(_0x5da90a=>{const _0x1fa6bd=_0x54dd;this['curtainsLoader'][_0x1fa6bd(0x217)](_0x11df2c=>this[_0x1fa6bd(0x226)](_0x11df2c))[_0x1fa6bd(0x239)](()=>_0x5da90a(!![]));});}['update'](){const _0x4b6c97=_0x32d79c;this[_0x4b6c97(0x247)]&&this[_0x4b6c97(0x247)]['instance']&&this[_0x4b6c97(0x247)][_0x4b6c97(0x20a)][_0x4b6c97(0x228)]();}['destroy'](){return new Promise(_0x465446=>{const _0x415dc1=_0x54dd,_0x3a49e7=app[_0x415dc1(0x225)][_0x415dc1(0x229)](_0x415dc1(0x221));this[_0x415dc1(0x210)](),_0x3a49e7&&_0x3a49e7[_0x415dc1(0x203)]?_0x3a49e7['scheduleLateTask'](this[_0x415dc1(0x21e)][_0x415dc1(0x223)],_0x415dc1(0x21f)):this[_0x415dc1(0x247)]&&this['curtains'][_0x415dc1(0x20d)]();});}[_0x32d79c(0x212)](_0x2553e9){const _0xbf866e=_0x32d79c,_0x21b471=gsap[_0xbf866e(0x237)]();return this[_0xbf866e(0x247)][_0xbf866e(0x20a)][_0xbf866e(0x1fa)][_0xbf866e(0x235)](_0x544327=>{const _0x5ae814=_0xbf866e,_0x36bc5b={'opacity':_0x544327[_0x5ae814(0x236)][_0x5ae814(0x23e)]['value']};_0x544327[_0x5ae814(0x202)]&&_0x544327[_0x5ae814(0x202)][_0x5ae814(0x218)]===_0x2553e9?(_0x544327['setRenderOrder'](0x1),_0x21b471['to'](_0x36bc5b,{'opacity':0x1,'duration':0.6,'ease':'power3.inOut','onStart':()=>{const _0x3cea3a=_0x5ae814;_0x544327[_0x3cea3a(0x1fd)]=!![];},'onUpdate':()=>{const _0x24d256=_0x5ae814;_0x544327[_0x24d256(0x236)][_0x24d256(0x23e)][_0x24d256(0x243)]=_0x36bc5b[_0x24d256(0x23e)];}},_0x5ae814(0x22e))):(_0x544327['setRenderOrder'](0x0),_0x21b471['to'](_0x36bc5b,{'opacity':0x0,'duration':0.6,'ease':_0x5ae814(0x244),'onComplete':()=>{const _0x3c27f6=_0x5ae814;_0x544327[_0x3c27f6(0x1fd)]=![];},'onUpdate':()=>{const _0x2515a5=_0x5ae814;_0x544327[_0x2515a5(0x236)][_0x2515a5(0x23e)][_0x2515a5(0x243)]=_0x36bc5b['opacity'];}},'start'));}),_0x21b471;}[_0x32d79c(0x205)](){const _0x32e315=_0x32d79c,_0xcdcaae=gsap[_0x32e315(0x237)]();return this['curtains'][_0x32e315(0x20a)][_0x32e315(0x1fa)][_0x32e315(0x235)](_0xb2c24f=>{const _0x500bf8=_0x32e315,_0x5d10cd={'opacity':_0xb2c24f[_0x500bf8(0x236)][_0x500bf8(0x23e)]['value']};_0xcdcaae['to'](_0x5d10cd,{'opacity':0x0,'duration':0.6,'ease':_0x500bf8(0x244),'onComplete':()=>{const _0x319909=_0x500bf8;_0xb2c24f[_0x319909(0x1fd)]=![];},'onUpdate':()=>{const _0x1bb366=_0x500bf8;_0xb2c24f[_0x1bb366(0x236)][_0x1bb366(0x23e)]['value']=_0x5d10cd[_0x1bb366(0x23e)];}},_0x500bf8(0x22e));}),_0xcdcaae;}[_0x32d79c(0x1f5)](){const _0x39adbc=_0x32d79c;this[_0x39adbc(0x22c)]=app['utilities'][_0x39adbc(0x21a)]({'callback':this['_handlers']['resize'],'immediateCall':![]}),this[_0x39adbc(0x1f4)][_0x39adbc(0x21b)]['on'](_0x39adbc(0x246),this['_handlers'][_0x39adbc(0x246)]);}[_0x32d79c(0x210)](){const _0x161e6d=_0x32d79c;scheduler['postTask'](()=>{const _0x1ff7af=_0x54dd;this[_0x1ff7af(0x22c)]&&typeof this['clearResize'][_0x1ff7af(0x20c)]==='function'&&this['clearResize'][_0x1ff7af(0x20c)]();}),scheduler[_0x161e6d(0x209)](()=>{const _0x57cdc3=_0x161e6d;this[_0x57cdc3(0x1f4)][_0x57cdc3(0x21b)][_0x57cdc3(0x224)]('visibleUpdate',this[_0x57cdc3(0x21e)][_0x57cdc3(0x246)]);});}[_0x32d79c(0x226)](_0x233abd){return new Promise(_0x2e3209=>{const _0x1d4c10=_0x54dd,_0x59755d=this[_0x1d4c10(0x208)]();this[_0x1d4c10(0x247)]=new _0x233abd[(_0x1d4c10(0x22a))]({'element':this[_0x1d4c10(0x231)],'container':this['elements'][_0x1d4c10(0x1fe)][0x0],'lanes':[this[_0x1d4c10(0x231)]],'options':_0x59755d}),this[_0x1d4c10(0x247)][_0x1d4c10(0x21d)][_0x1d4c10(0x217)](()=>this[_0x1d4c10(0x216)]())[_0x1d4c10(0x239)](()=>{const _0x30e286=_0x1d4c10;this[_0x30e286(0x1f5)](),_0x2e3209(!![]);});});}[_0x32d79c(0x21c)](_0x291be1){const _0x5521fc=_0x32d79c;this[_0x5521fc(0x247)]&&this[_0x5521fc(0x247)][_0x5521fc(0x20a)]&&(_0x291be1?this['curtains']['instance'][_0x5521fc(0x240)]():this[_0x5521fc(0x247)][_0x5521fc(0x20a)][_0x5521fc(0x241)]());}[_0x32d79c(0x208)](){const _0x5173f3=_0x32d79c;let _0x491656={'planes':{'widthSegments':0x10,'heightSegments':0x10,'uniforms':{'opacity':{'name':_0x5173f3(0x206),'type':'1f','value':0x0}},'vertexShader':this[_0x5173f3(0x215)](),'fragmentShader':this['_getPlaneFragmentShader']()},'itemIdAttribute':this[_0x5173f3(0x22f)][_0x5173f3(0x1ff)],'onContextLost':this[_0x5173f3(0x21e)][_0x5173f3(0x228)]};return typeof this[_0x5173f3(0x22f)][_0x5173f3(0x1fb)]===_0x5173f3(0x207)&&(_0x491656=deepmerge(this['options'][_0x5173f3(0x1fb)],_0x491656),typeof this[_0x5173f3(0x22f)]['webGL'][_0x5173f3(0x245)]===_0x5173f3(0x234)&&(_0x491656[_0x5173f3(0x1fa)][_0x5173f3(0x20b)]=this['options']['webGL'][_0x5173f3(0x245)],_0x491656[_0x5173f3(0x1fa)][_0x5173f3(0x20f)]=this[_0x5173f3(0x22f)][_0x5173f3(0x1fb)][_0x5173f3(0x245)])),_0x491656;}[_0x32d79c(0x20e)](){return new Promise(_0x291bf6=>{const _0x363b5b=_0x54dd;this[_0x363b5b(0x247)]&&this[_0x363b5b(0x247)][_0x363b5b(0x20d)](),_0x291bf6(!![]);});}[_0x32d79c(0x215)](){const _0x296341=_0x32d79c;return _0x296341(0x200);}[_0x32d79c(0x22b)](){const _0x4206f3=_0x32d79c;return _0x4206f3(0x227);}['_onResize'](){return new Promise(_0x62b0b9=>{const _0x11a776=_0x54dd,_0x3ef217=[];this[_0x11a776(0x247)][_0x11a776(0x20a)][_0x11a776(0x1fa)][_0x11a776(0x235)]((_0x96cda2,_0x79d4c5)=>{const _0x12d84b=_0x11a776,_0x4930cf=scheduler[_0x12d84b(0x209)](()=>{_0x96cda2['scale']['x']=0x1,_0x96cda2['scale']['y']=0x1;});_0x3ef217['push'](_0x4930cf),_0x96cda2[_0x12d84b(0x23f)][_0x12d84b(0x248)]&&_0x96cda2['textures'][_0x12d84b(0x235)](_0x71b437=>{const _0x59d49e=_0x12d84b,_0x5b344a=scheduler['postTask'](()=>{const _0xf2d70e=_0x54dd;_0x71b437[_0xf2d70e(0x219)]['x']=0x1,_0x71b437[_0xf2d70e(0x219)]['y']=0x1;});_0x3ef217[_0x59d49e(0x1f6)](_0x5b344a);});}),Promise['all'](_0x3ef217)[_0x11a776(0x239)](()=>{const _0x2adb33=_0x11a776;this[_0x2adb33(0x247)]&&this[_0x2adb33(0x247)][_0x2adb33(0x20a)]&&this[_0x2adb33(0x247)][_0x2adb33(0x20a)][_0x2adb33(0x228)](),_0x62b0b9(!![]);});});}}