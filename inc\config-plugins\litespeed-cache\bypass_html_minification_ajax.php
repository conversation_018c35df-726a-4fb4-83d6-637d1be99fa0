<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_filter( 'litespeed_html_min', 'arts_bypass_html_minification_ajax' );
if ( ! function_exists( 'arts_bypass_html_minification_ajax' ) ) {
	function arts_bypass_html_minification_ajax( $value ) {
		/**
		 * Force disable HTML minification for LiteSpeed Cache plugin
		 * if AJAX transitions are enabled to avoid incorrect
		 * page rendering.
		 *
		 * @param bool $value The current value of HTML minification setting.
		 * @return bool The modified value of HTML minification setting.
		 */
		$ajax_enabled = Utilities::get_kit_settings( 'ajax_enabled', false );

		if ( $ajax_enabled ) {
			$value = false;
		}

		return $value;
	}
}
