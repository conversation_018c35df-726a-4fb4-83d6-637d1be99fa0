<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_action( 'wp_enqueue_scripts', 'arts_enqueue_polyfills' );
if ( ! function_exists( 'arts_enqueue_polyfills' ) ) {
	/**
	 * Enqueue polyfill scripts for the theme.
	 *
	 * @return void
	 */
	function arts_enqueue_polyfills() {
		$ajax_enabled = Utilities::get_kit_settings( 'ajax_enabled', false );

		if ( $ajax_enabled ) {
			wp_enqueue_script(
				'query-monitor-ajax',
				esc_url( ARTS_THEME_URL . '/js/vendor/query-monitor-ajax.js' ),
				array( 'query-monitor' ),
				ARTS_THEME_VERSION,
				true
			);
		}

		wp_enqueue_script(
			'scheduler-polyfill',
			esc_url( ARTS_THEME_URL . '/js/vendor/scheduler-polyfill.min.js' ),
			array(),
			'1.3.0',
			true
		);
	}
}
