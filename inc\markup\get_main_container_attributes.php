<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_main_container_attributes' ) ) {
	/**
	 * Get the <main> page container attributes.
	 *
	 * @return array The attributes for the main container element.
	 */
	function arts_get_main_container_attributes() {
		$ajax_enabled = Utilities::get_kit_settings( 'ajax_enabled', false );

		$attributes = array(
			'id'    => 'page-wrapper',
			'class' => array( 'page-wrapper' ),
		);

		// AJAX namespaces
		if ( $ajax_enabled ) {
			$attributes['data-barba'] = 'container';

			// Is archive
			if ( Utilities::is_archive() ) {
				$attributes['data-barba-namespace'] = 'archive';
			} elseif ( is_singular( 'post' ) ) { // Is blog post
				$attributes['data-barba-namespace'] = 'post';
			} elseif ( Utilities::is_built_with_elementor() ) { // is Elementor post/page
				$attributes['data-barba-namespace'] = 'elementor';
			}
		}

		$attributes = apply_filters( 'arts/page_container/attributes', $attributes );

		return $attributes;
	}
}
