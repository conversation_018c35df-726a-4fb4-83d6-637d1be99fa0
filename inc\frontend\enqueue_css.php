<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_action( 'wp_enqueue_scripts', 'arts_enqueue_css', 50 );
if ( ! function_exists( 'arts_enqueue_css' ) ) {
	/**
	 * Enqueue the theme styles
	 *
	 * @return void
	 */
	function arts_enqueue_css() {
		$ajax_enabled          = Utilities::get_kit_settings( 'ajax_enabled', false );
		$cf_7_modals_enabled   = Utilities::get_kit_settings( 'cf_7_modals_enabled', true );
		$theme_main_style_deps = array(
			'bootstrap-reboot',
			'bootstrap-grid',
			'bootstrap-utilities',
			'elementor-icons-material-icons',
		);

		/**
		 * Force load Elementor assets
		 * on non-Elementor pages with AJAX turned on
		 */
		if ( class_exists( '\Elementor\Frontend' ) && ! Utilities::is_built_with_elementor() && $ajax_enabled ) {
			/**
			 * @var \Elementor\Core\Base\Module::instance
			 */
			$instance = \Elementor\Frontend::instance();
			$instance->enqueue_styles();
		}

		/**
		 * Load fallback font if Elementor fonts are not set
		 */
		if ( ! class_exists( '\Elementor\Plugin' ) ) {
			wp_enqueue_style( 'asli-google-fonts', '//fonts.googleapis.com/css2?family=Noto+Serif+Display:wght@200;300;700&family=Nunito+Sans:ital,wght@0,400;0,700;1,400;1,700&display=swap', array(), ARTS_THEME_VERSION );
		}

		// Hide default Contact Form 7 response boxes if custom modals are enabled
		if ( $cf_7_modals_enabled ) {
			wp_add_inline_style( 'contact-form-7', wp_strip_all_tags( '.wpcf7-mail-sent-ok, .wpcf7 form.sent .wpcf7-response-output, .wpcf7-mail-sent-ng, .wpcf7 form.failed .wpcf7-response-output, .wpcf7 form.invalid .wpcf7-response-output { display: none !important; }' ) );
		}

		wp_enqueue_style( 'bootstrap-reboot', esc_url( ARTS_THEME_URL . '/css/bootstrap-reboot.min.css' ), array(), ARTS_THEME_VERSION );
		wp_enqueue_style( 'bootstrap-grid', esc_url( ARTS_THEME_URL . '/css/bootstrap-grid.min.css' ), array(), ARTS_THEME_VERSION );
		wp_enqueue_style( 'bootstrap-utilities', esc_url( ARTS_THEME_URL . '/css/bootstrap-utilities.min.css' ), array(), ARTS_THEME_VERSION );
		wp_enqueue_style( 'elementor-icons-material-icons', esc_url( ARTS_THEME_URL . '/css/material-icons.min.css' ), array(), ARTS_THEME_VERSION );

		wp_enqueue_style(
			'asli-main-style',
			esc_url( ARTS_THEME_URL . '/css/main.css' ),
			$theme_main_style_deps,
			ARTS_THEME_VERSION
		);
		wp_enqueue_style( 'asli-theme-style', esc_url( ARTS_THEME_URL . '/style.css' ), array(), ARTS_THEME_VERSION );

		if ( class_exists( 'WooCommerce' ) ) {
			wp_enqueue_style(
				'asli-woocommerce-style',
				esc_url( ARTS_THEME_URL . '/css/woocommerce.css' ),
				array(
					'asli-main-style',
				),
				ARTS_THEME_VERSION
			);
		}
	}
}

add_action( 'elementor/editor/before_enqueue_styles', 'arts_enqueue_css_elementor_editor' );
if ( ! function_exists( 'arts_enqueue_css_elementor_editor' ) ) {
	/**
	 * Enqueue the theme styles for Elementor editor
	 *
	 * @return void
	 */
	function arts_enqueue_css_elementor_editor() {
		wp_enqueue_style( 'asli-elementor-editor', esc_url( ARTS_THEME_URL . '/css/elementor-editor.min.css' ), array(), ARTS_THEME_VERSION );
	}
}
