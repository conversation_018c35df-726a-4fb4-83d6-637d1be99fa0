<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! defined( 'ARTS_ASLI_CORE_PLUGIN_PATH' ) || ! class_exists( '\Elementor\Plugin' ) ) {
	add_action( 'wp_enqueue_scripts', 'arts_enqueue_no_elementor_css', 50 );
	if ( ! function_exists( 'arts_enqueue_no_elementor_css' ) ) {
		/**
		 * Enqueue the styles when Elementor or the theme core plugin is not active
		 *
		 * @return void
		 */
		function arts_enqueue_no_elementor_css() {
			wp_enqueue_style( 'no-elementor', esc_url( ARTS_THEME_URL . '/css/no-elementor.min.css' ), array(), ARTS_THEME_VERSION );
		}
	}
}
