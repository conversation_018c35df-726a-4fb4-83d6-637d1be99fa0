<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_loading_spinner_attributes' ) ) {
	/**
	 * Retrieves the HTML attributes for the loading indicator
	 * which is an animated spinning icon.
	 *
	 * @return array The attributes for the loading spinner.
	 */
	function arts_get_loading_spinner_attributes() {
		$ajax_enabled = Utilities::get_kit_settings( 'ajax_enabled', false );

		if ( ! $ajax_enabled ) {
			return array();
		}

		$ajax_spinner_desktop_enabled = Utilities::get_kit_settings( 'ajax_spinner_desktop_enabled', false );
		$ajax_spinner_mobile_enabled  = Utilities::get_kit_settings( 'ajax_spinner_mobile_enabled', true );
		$attributes                   = array(
			'size'  => 50,
			'outer' => array(
				'id'    => 'loading-indicator',
				'class' => array( 'spinner' ),
			),
		);

		if ( $ajax_spinner_desktop_enabled ) {
			$spinner_args['outer']['class'][] = 'd-lg-block';
		} else {
			$spinner_args['outer']['class'][] = 'd-lg-none';
		}

		if ( $ajax_spinner_mobile_enabled ) {
			$spinner_args['outer']['class'][] = 'd-block';
		} else {
			$spinner_args['outer']['class'][] = 'd-none';
		}

		return $attributes;
	}
}
