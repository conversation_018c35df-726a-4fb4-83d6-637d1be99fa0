var Merlin=function(n){var e,i="merlin__drawer--open";function t(e){var i=n(n.parseHTML("<div>"+e+"</div>",null,!1));return i.find("*").each(function(){var e,i;e=this,n.each(e.attributes,function(){var i=this.name,t=this.value;i&&(0===i.indexOf("on")||t&&t.includes("javascript:"))&&n(e).removeAttr(i)}),(i=e.getAttribute("href"))&&i.includes("javascript:")&&n(e).removeAttr("href")}),i.html()}var s={install_child:function(n){new a().init(n)},activate_license:function(n){new r().init(n)},install_plugins:function(n){new l().init(n)},install_content:function(n){new c().init(n)}};function o(){var e=n(".merlin__body"),o=(n(".merlin__body--loading"),n(".merlin__body--exiting"),n("#merlin__drawer-trigger")),a=n("form.merlin__content--license-key"),r=n(".js-license-key");a.on("submit",function(e){e.preventDefault(),a.get(0).checkValidity()&&n(".js-merlin-license-activate-button").click()}),r.on("change",function(n){n.target.validity.valid&&r.closest(".merlin__content--license-key").removeClass("has-error")}),setTimeout(function(){e.addClass("loaded")},100),o.on("click",function(){e.toggleClass(i)}),n(".merlin__button--proceed:not(.merlin__button--closer)").click(function(n){n.preventDefault();var i=this.getAttribute("href");e.addClass("exiting"),setTimeout(function(){window.location=i},400)}),n(".merlin__button--closer").on("click",function(n){e.removeClass(i),n.preventDefault();var t=this.getAttribute("href");setTimeout(function(){e.addClass("exiting")},600),setTimeout(function(){window.location=t},1100)}),n(".button-next").on("click",function(e){if(e.preventDefault(),!function n(e){var i=jQuery(e);if("yes"==i.data("done-loading"))return!1;var t=!1;return i.is("input")||i.is("button"),i.data("done-loading","yes"),i.addClass("merlin__button--loading"),{done:function(){t=!0,i.attr("disabled",!1)}}}(this))return!1;var i=n(this).data("callback");return!i||void 0===s[i]||(s[i](this),!1)}),n(document).on("change",".js-merlin-demo-import-select",function(){var e=n(this).val(),i=n(this).children(":selected");i.data("img-src"),i.data("notice"),i.data("preview-url"),n.post(merlin_params.ajaxurl,{action:"merlin_update_selected_import_data_info",wpnonce:merlin_params.wpnonce,selected_index:e},function(e){e.success?n(".js-merlin-drawer-import-content").html(t(e.data)):alert(merlin_params.texts.something_went_wrong)}).fail(function(){alert(merlin_params.texts.something_went_wrong)})})}function a(){var e,s=n(".merlin__body"),o=n("#child-theme-text");function a(n){void 0!==n.done?(setTimeout(function(){o.addClass("lead")},0),setTimeout(function(){o.addClass("success"),o.html(t(n.message))},600),e()):(o.addClass("lead error"),o.html(t(n.error)))}return{init:function(t){e=function(){setTimeout(function(){n(".merlin__body").addClass("js--finished")},1500),s.removeClass(i),setTimeout(function(){n(".merlin__body").addClass("exiting")},3500),setTimeout(function(){window.location.href=t.href},4e3)},jQuery.post(merlin_params.ajaxurl,{action:"merlin_child_theme",wpnonce:merlin_params.wpnonce},a).fail(a)}}}function r(){var e,s=n(".merlin__body"),o=n(".merlin__content--license-key"),a=n("#license-text");function r(i){void 0!==i.success&&i.success?(a.siblings(".error-message").remove(),setTimeout(function(){a.addClass("lead")},0),setTimeout(function(){a.addClass("success"),a.html(t(i.message))},600),e()):(n(".js-merlin-license-activate-button").removeClass("merlin__button--loading").data("done-loading","no"),a.siblings(".error-message").remove(),o.addClass("has-error"),a.html(t(i.message)),a.siblings(".error-message").addClass("lead error"),n(".js-license-key").val(""))}return{init:function(t){var a;e=function(){setTimeout(function(){n(".merlin__body").addClass("js--finished")},1500),s.removeClass(i),setTimeout(function(){n(".merlin__body").addClass("exiting")},3500),setTimeout(function(){window.location.href=t.href},4e3)},(a=n("form.merlin__content--license-key").get(0))&&a.checkValidity()?(o.removeClass("has-error"),jQuery.post(merlin_params.ajaxurl,{action:"merlin_activate_license",wpnonce:merlin_params.wpnonce,license_key:n(".js-license-key").val()},r).fail(r)):r({message:"Invalid license key"})}}}function l(){var e,t,s=n(".merlin__body"),o=0,a="",r="";function l(n){var e=t.find("label");"object"==typeof n&&void 0!==n.message?(e.removeClass("installing success error").addClass(n.message.toLowerCase()),void 0!==n.done&&n.done?d():void 0!==n.url?n.hash==r?(e.removeClass("installing success").addClass("error"),d()):(r=n.hash,jQuery.post(n.url,n,l).fail(l)):d()):c()}function c(){a&&(t.find("input:checkbox").is(":checked")?jQuery.post(merlin_params.ajaxurl,{action:"merlin_plugins",wpnonce:merlin_params.wpnonce,slug:a},l).fail(l):(t.addClass("skipping"),setTimeout(d,300)))}function d(){t&&(t.data("done_item")||(o++,t.data("done_item",1)),t.find(".spinner").css("visibility","hidden"));var i=n(".merlin__drawer--install-plugins li");i.each(function(){var e=n(this);return!!e.data("done_item")||(a=e.data("slug"),t=e,c(),!1)}),o>=i.length&&e()}return{init:function(t){n(".merlin__drawer--install-plugins").addClass("installing"),n(".merlin__drawer--install-plugins").find("input").prop("disabled",!0),e=function(){setTimeout(function(){n(".merlin__body").addClass("js--finished")},1e3),s.removeClass(i),setTimeout(function(){n(".merlin__body").addClass("exiting")},3e3),setTimeout(function(){window.location.href=t.href},3500)},d()}}}function c(){var e,s,o,a=n(".merlin__body"),r=0,l="",c="",d=1,u=0;function m(e){var i=s.find("label");"object"==typeof e&&void 0!==e.message?(i.addClass(e.message.toLowerCase()),void 0!==e.num_of_imported_posts&&0<u&&(d="all"===e.num_of_imported_posts?u:e.num_of_imported_posts,$()),void 0!==e.url?e.hash===c?(i.addClass("status--failed"),f()):(c=e.hash,void 0===e.selected_index&&(e.selected_index=n(".js-merlin-demo-import-select").val()||0),jQuery.post(e.url,e,m).fail(m)):(e.done,f())):(console.log(e),i.addClass("status--error"),f())}function f(){var i=!1;s&&(s.data("done_item")||(r++,s.data("done_item",1)),s.find(".spinner").css("visibility","hidden"));var t=n(".merlin__drawer--import-content__list-item");n(".merlin__drawer--import-content__list-item input:checked"),t.each(function(){""==l||i?(l=n(this).data("content"),s=n(this),l&&(s.find("input:checkbox").is(":checked")?jQuery.post(merlin_params.ajaxurl,{action:"merlin_content",wpnonce:merlin_params.wpnonce,content:l,selected_index:n(".js-merlin-demo-import-select").val()||0},m).fail(m):(s.addClass("skipping"),setTimeout(f,300))),i=!1):n(this).data("content")==l&&(i=!0)}),r>=t.length&&e()}function $(){n(".js-merlin-progress-bar").css("width",d/u*100+"%");var e,i=Math.min(99,Math.max(0,e=d/u*100));n(".js-merlin-progress-bar-percentage").html(t(Math.round(i)+"%")),1==d/u&&clearInterval(o)}return{init:function(t){n(".merlin__drawer--import-content").addClass("installing"),n(".merlin__drawer--import-content").find("input").prop("disabled",!0),e=function(){n.post(merlin_params.ajaxurl,{action:"merlin_import_finished",wpnonce:merlin_params.wpnonce,selected_index:n(".js-merlin-demo-import-select").val()||0}),setTimeout(function(){n(".js-merlin-progress-bar-percentage").html("100%")},100),setTimeout(function(){a.removeClass(i)},500),setTimeout(function(){n(".merlin__body").addClass("js--finished")},1500),setTimeout(function(){n(".merlin__body").addClass("exiting")},3400),setTimeout(function(){window.location.href=t.href},4e3)},function e(){if(!n(".merlin__drawer--import-content__list-item .checkbox-content").is(":checked"))return!1;jQuery.post(merlin_params.ajaxurl,{action:"merlin_get_total_content_import_items",wpnonce:merlin_params.wpnonce,selected_index:n(".js-merlin-demo-import-select").val()||0},function(n){0<(u=n.data)&&($(),o=setInterval(function(){d+=u/500,$()},1e3))})}(),f()}}}return{init:function(){e=this,n(o)},callback:function(n){console.log(n),console.log(this)}}}(jQuery);Merlin.init();