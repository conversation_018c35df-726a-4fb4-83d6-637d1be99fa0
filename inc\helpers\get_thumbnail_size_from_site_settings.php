<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_thumbnail_size_from_site_settings' ) ) {
	/**
	 * Retrieves the thumbnail size from site settings.
	 *
	 * @param array  $image                The image data.
	 * @param string $group_control_prefix The prefix for group control settings. Default 'image'.
	 *
	 * @return array|false The thumbnail size settings or false if no image is provided.
	 * @deprecated 2.0.0
	 */
	function arts_get_thumbnail_size_from_site_settings( $image, $group_control_prefix = 'image' ) {
		if ( ! $image ) {
			return false;
		}

		$settings = array(
			"{$group_control_prefix}_size"             => Utilities::get_kit_settings( "{$group_control_prefix}_size", 'full', false ),
			"{$group_control_prefix}_custom_dimension" => Utilities::get_kit_settings( "{$group_control_prefix}_custom_dimension", false, false ),
		);

		return Utilities::get_settings_thumbnail_size( $settings, $image, $group_control_prefix );
	}
}

if ( ! function_exists( 'arts_get_image_custom_thumbnail_size' ) ) {
	/**
	 * Get the thumbnail size settings for an image.
	 *
	 * @param array  $settings            The settings array.
	 * @param mixed  $image_id            The image ID or array containing the image ID.
	 * @param string $group_control_prefix The prefix for the group control.
	 *
	 * @return mixed The thumbnail size settings or 'full' if no custom size is set.
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::get_settings_thumbnail_size()` method instead.
	 */
	function arts_get_image_custom_thumbnail_size( $settings, $group_control_prefix, $image_id ) {
		return Utilities::get_settings_thumbnail_size( $settings, $image_id, $group_control_prefix, 'custom_size' );
	}
}

if ( ! function_exists( 'arts_get_image_id' ) ) {
	/**
	 * Retrieve the image ID from an array or return the image itself.
	 *
	 * @param mixed $image Image array with 'id' key or image ID.
	 * @return mixed Image ID or original image.
	 */
	function arts_get_image_id( $image ) {
		if ( is_array( $image ) && array_key_exists( 'id', $image ) ) {
			return $image['id'];
		}

		return $image;
	}
}

if ( ! function_exists( 'arts_get_image_control_value' ) ) {
	/**
	 * Retrieve image control value from settings.
	 *
	 * @param array  $settings           The settings array.
	 * @param string $group_control_prefix The prefix for the group control.
	 * @param string $type               The type of control value to retrieve. Default 'size'.
	 * @param mixed  $fallback           The fallback value if control is not found. Default ''.
	 *
	 * @return mixed The control value or fallback.
	 *
	 * @deprecated 2.0.0
	 */
	function arts_get_image_control_value( $settings, $group_control_prefix, $type = 'size', $fallback = '' ) {
		$control_name = "{$group_control_prefix}_{$type}";

		if ( array_key_exists( $control_name, $settings ) ) {
			return $settings[ $control_name ];
		}

		return $fallback;
	}
}

if ( ! function_exists( 'arts_get_image_aspect_ratio' ) ) {
	/**
	 * Get the aspect ratio of an image.
	 *
	 * Calculates the aspect ratio of an image based on its width and height.
	 *
	 * @param int $image_id The ID of the image attachment.
	 *
	 * @return float The aspect ratio of the image.
	 * @deprecated 2.0.0 Use `Arts\Utilities\Utilities::get_image_aspect_ratio` method instead.
	 */
	function arts_get_image_aspect_ratio( $image_id ) {
		return Utilities::get_image_aspect_ratio( $image_id );
	}
}
