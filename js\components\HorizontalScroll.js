const _0x59c27d=_0x34e4;function _0x34e4(_0x56cd78,_0x21e2a1){const _0x1870a6=_0x1870();return _0x34e4=function(_0x34e44f,_0x273781){_0x34e44f=_0x34e44f-0x123;let _0x150484=_0x1870a6[_0x34e44f];return _0x150484;},_0x34e4(_0x56cd78,_0x21e2a1);}(function(_0x51902,_0x477d89){const _0x527fdb=_0x34e4,_0x34a0d0=_0x51902();while(!![]){try{const _0xace07b=-parseInt(_0x527fdb(0x14e))/0x1+-parseInt(_0x527fdb(0x124))/0x2*(-parseInt(_0x527fdb(0x156))/0x3)+-parseInt(_0x527fdb(0x15d))/0x4+-parseInt(_0x527fdb(0x15a))/0x5*(-parseInt(_0x527fdb(0x161))/0x6)+parseInt(_0x527fdb(0x141))/0x7*(parseInt(_0x527fdb(0x155))/0x8)+parseInt(_0x527fdb(0x147))/0x9*(-parseInt(_0x527fdb(0x130))/0xa)+parseInt(_0x527fdb(0x13f))/0xb;if(_0xace07b===_0x477d89)break;else _0x34a0d0['push'](_0x34a0d0['shift']());}catch(_0x97e60d){_0x34a0d0['push'](_0x34a0d0['shift']());}}}(_0x1870,0x7e03e));function _0x1870(){const _0x5453ae=['push','wrapper','setup','_attachEvents','end','2680414itZHYI','isActive','827043MxEhtu','dataReady','headerRef','scheduleLateTask','bind','matches','1431RvfYLm','update','getComponentByName','mount','lockSticky','kill','lockHeaderSticky','333368LDZhva','_handlers','mounted','data-arts-component-options','innerSelectors','matchMedia','getScrubAnimation','64ZGHLIM','319893yMqfvW','_createHorizontalScroll','utilities','run','314385BdkQfH','data-arts-component-name','mode','2968628GKYmXc','all','_onMQChange','finally','30ahwLaq','enabled','in-view','_onTransitionEnd','element','running','AJAX','10JqRdkB','modular','resizeObserver','Header','(min-width:\x20992px)\x20and\x20(hover:\x20hover)\x20and\x20(pointer:\x20fine)','top\x20top','postTask','transitionEnd','toggleInViewClass','sections','init','horizontalScroll','27970VoVqFE','toggleHidden','removeMediaQueryListener','destroy','.js-horizontal-scroll__section','options','toggleHeaderVisibility','componentsManager','_initSplitText','then'];_0x1870=function(){return _0x5453ae;};return _0x1870();}export default class HorizontalScroll extends BaseComponent{constructor({name:_0x29c77b,loadInnerComponents:_0x2dfa8a,loadAfterSyncStyles:_0x566ce1,parent:_0x4c6ae6,element:_0x1bb6bf}){const _0x242f87=_0x34e4;super({'name':_0x29c77b,'loadInnerComponents':_0x2dfa8a,'loadAfterSyncStyles':_0x566ce1,'parent':_0x4c6ae6,'element':_0x1bb6bf,'defaults':{'mode':_0x242f87(0x125),'toggleInViewClass':_0x242f87(0x163),'matchMedia':_0x242f87(0x128),'toggleHeaderVisibility':!![],'lockHeaderSticky':![]},'innerElements':{'wrapper':'.js-horizontal-scroll__wrapper','sections':_0x242f87(0x134)}}),this[_0x242f87(0x14f)]={'mqChange':this[_0x242f87(0x15f)][_0x242f87(0x145)](this),'transitionEnd':this[_0x242f87(0x164)][_0x242f87(0x145)](this)},this[_0x242f87(0x142)][_0x242f87(0x160)](()=>{const _0x3b681d=_0x242f87;this['mq']=null,typeof this[_0x3b681d(0x135)][_0x3b681d(0x153)]==='string'&&this['_createMatchMedia'](),this['_attachEvents'](),this[_0x3b681d(0x13c)]();});}[_0x59c27d(0x12e)](){return new Promise(_0x4c48f9=>{const _0x267b50=_0x34e4;this['updateRef'](_0x267b50(0x143),_0x267b50(0x127)),this[_0x267b50(0x143)]&&(this[_0x267b50(0x143)][_0x267b50(0x131)](![]),this[_0x267b50(0x143)][_0x267b50(0x14b)](![])),_0x4c48f9(!![]);});}[_0x59c27d(0x148)](){const _0xac8abe=_0x59c27d;this[_0xac8abe(0x12f)]&&this['horizontalScroll'][_0xac8abe(0x162)]&&this[_0xac8abe(0x12f)]['update'](),app['refresher'][_0xac8abe(0x159)]();}[_0x59c27d(0x133)](){return new Promise(_0xb66294=>{const _0xb1ffb7=_0x34e4,_0x5d5f78=[];app[_0xb1ffb7(0x158)][_0xb1ffb7(0x132)](this['mq'],this['_handlers']['mqChange']);if(this['stScrub']){const _0x524341=scheduler[_0xb1ffb7(0x12a)](()=>{const _0x99ec9b=_0xb1ffb7;this['stScrub'][_0x99ec9b(0x14c)]();});_0x5d5f78[_0xb1ffb7(0x13a)](_0x524341);}if(this[_0xb1ffb7(0x126)]){const _0xc11b5f=scheduler[_0xb1ffb7(0x12a)](()=>{const _0x199779=_0xb1ffb7;this[_0x199779(0x126)]['disconnect']();});_0x5d5f78['push'](_0xc11b5f);}const _0x394c64=app[_0xb1ffb7(0x137)][_0xb1ffb7(0x149)](_0xb1ffb7(0x123));_0x394c64&&_0x394c64[_0xb1ffb7(0x166)]?_0x394c64[_0xb1ffb7(0x144)](()=>new Promise(_0x11a83d=>{const _0x1e70ab=_0xb1ffb7;scheduler[_0x1e70ab(0x12a)](()=>{const _0x4f4952=_0x1e70ab;this['horizontalScroll']&&(this['horizontalScroll'][_0x4f4952(0x133)](),this[_0x4f4952(0x12f)]=null);})['finally'](()=>_0x11a83d(!![]));}),_0xb1ffb7(0x13e)):scheduler[_0xb1ffb7(0x12a)](()=>{const _0x3b57e8=_0xb1ffb7;this[_0x3b57e8(0x12f)]&&(this[_0x3b57e8(0x12f)][_0x3b57e8(0x133)](),this['horizontalScroll']=null);}),Promise['all'](_0x5d5f78)[_0xb1ffb7(0x160)](()=>_0xb66294(!![]));});}[_0x59c27d(0x14a)](){return new Promise(_0x591735=>{const _0x181ac8=_0x34e4;this[_0x181ac8(0x157)](),this['mounted']||!this['loadInnerComponents']?(this[_0x181ac8(0x150)]=!![],this['update'](),_0x591735(!![])):Promise[_0x181ac8(0x15e)](app[_0x181ac8(0x137)][_0x181ac8(0x12e)]({'storage':this['components'],'scope':this[_0x181ac8(0x165)],'parent':this,'nameAttribute':_0x181ac8(0x15b),'optionsAttribute':_0x181ac8(0x151)}))[_0x181ac8(0x139)](()=>this[_0x181ac8(0x138)]())[_0x181ac8(0x139)](()=>this['_initLazyMedia']())[_0x181ac8(0x160)](()=>{const _0x35ba10=_0x181ac8;this[_0x35ba10(0x150)]=!![],_0x591735(!![]);});});}[_0x59c27d(0x154)](){const _0x27d735=_0x59c27d,_0x43cced={'trigger':this['element'],'start':()=>_0x27d735(0x129),'end':()=>'bottom\x20bottom','scrub':!![],'matchMedia':this['options'][_0x27d735(0x153)],'onToggle':_0x4d930a=>{const _0x14eede=_0x27d735;this['updateRef'](_0x14eede(0x143),_0x14eede(0x127)),this[_0x14eede(0x143)]&&(!!this[_0x14eede(0x135)][_0x14eede(0x136)]&&this['headerRef']['toggleHidden'](_0x4d930a[_0x14eede(0x140)]),!!this[_0x14eede(0x135)][_0x14eede(0x14d)]&&this[_0x14eede(0x143)][_0x14eede(0x14b)](_0x4d930a['isActive']));}};return _0x43cced;}['_createHorizontalScroll'](){const _0x496e83=_0x59c27d;this[_0x496e83(0x12f)]=new ArtsHorizontalScroll(this[_0x496e83(0x165)],{'mode':this[_0x496e83(0x135)][_0x496e83(0x15c)],'wrapperElementSelector':this['innerSelectors'][_0x496e83(0x13b)],'sectionElementsSelector':this[_0x496e83(0x152)][_0x496e83(0x12d)],'matchMedia':this[_0x496e83(0x135)][_0x496e83(0x153)],'toggleInViewClass':this['options'][_0x496e83(0x12c)]});}['_createMatchMedia'](){const _0x38a66e=_0x59c27d;this['mq']=window[_0x38a66e(0x153)](this[_0x38a66e(0x135)][_0x38a66e(0x153)]),app[_0x38a66e(0x158)]['addMediaQueryListener'](this['mq'],this[_0x38a66e(0x14f)]['mqChange']);}[_0x59c27d(0x15f)](_0x3119fc){const _0x2a3959=_0x59c27d;!_0x3119fc[_0x2a3959(0x146)]?this[_0x2a3959(0x143)]&&(!!this[_0x2a3959(0x135)][_0x2a3959(0x136)]&&this[_0x2a3959(0x143)]['toggleHidden'](![]),!!this['options']['lockHeaderSticky']&&this['headerRef'][_0x2a3959(0x14b)](![])):app['refresher']['run']();}[_0x59c27d(0x13d)](){const _0x31567f=_0x59c27d;app[_0x31567f(0x158)]['addAJAXEndEventListener'](this[_0x31567f(0x14f)][_0x31567f(0x12b)]);}[_0x59c27d(0x164)](){return new Promise(_0x17fcd0=>{const _0x26594c=_0x34e4;this[_0x26594c(0x12f)]&&this[_0x26594c(0x12f)][_0x26594c(0x162)]&&this[_0x26594c(0x12f)][_0x26594c(0x148)](),_0x17fcd0(!![]);});}}