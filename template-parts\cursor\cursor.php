<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

$spinner_args = array(
	'size' => 50,
);

?>

<div class="arts-cursor js-arts-cursor" data-arts-cursor-follower="cursor" id="js-arts-cursor">
	<div class="arts-cursor__wrapper" data-arts-cursor-follower-element="wrapper">
		<!-- Follower element-->
		<div class="arts-cursor__follower" data-arts-cursor-follower-element="follower"></div>
		<!-- Arrows-->
		<div class="arts-cursor__arrows">
			<div class="arts-cursor__arrow arts-cursor__arrow_up material-icons keyboard_arrow_up" data-arts-cursor-follower-element="arrowUp"></div>
			<div class="arts-cursor__arrow arts-cursor__arrow_right material-icons keyboard_arrow_right" data-arts-cursor-follower-element="arrowRight"></div>
			<div class="arts-cursor__arrow arts-cursor__arrow_down material-icons keyboard_arrow_down" data-arts-cursor-follower-element="arrowDown"></div>
			<div class="arts-cursor__arrow arts-cursor__arrow_left material-icons keyboard_arrow_left" data-arts-cursor-follower-element="arrowLeft"></div>
		</div>
		<!-- Label holder-->
		<div class="arts-cursor__wrapper-label">
			<div class="arts-cursor__label" data-arts-cursor-follower-element="toggleLabel"></div>
		</div>
		<!-- Icon holder-->
		<div class="arts-cursor__wrapper-icon">
			<div class="arts-cursor__icon" data-arts-cursor-follower-element="toggleClass"></div>
		</div>
		<!-- Loading indicator -->
		<div class="arts-cursor__wrapper-loading" data-arts-cursor-follower-element="loading">
			<?php get_template_part( 'template-parts/svg/circle', '', $spinner_args ); ?>
		</div>
	</div>
</div>
