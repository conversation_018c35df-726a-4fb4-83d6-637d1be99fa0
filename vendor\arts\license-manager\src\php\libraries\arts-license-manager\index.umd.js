/*!
 * Arts License Manager v1.0.0
 * Copyright © 2025
 * Author: <PERSON><PERSON>
 * License: MIT
 * Website: https://artemsemkin.com
 * Generated on: 2025-04-25
 */
(function(root, factory) {if (typeof define === 'function' && define.amd) {define([], factory);} else if (typeof module === 'object' && module.exports) {module.exports = factory();} else {root.ArtsNoticeManager = factory();}}(typeof self !== 'undefined' ? self : this, function() {"use strict";var ArtsNoticeManager=(()=>{var f=Object.defineProperty;var g=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var L=Object.prototype.hasOwnProperty;var v=(n,e,t)=>e in n?f(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var b=(n,e)=>{for(var t in e)f(n,t,{get:e[t],enumerable:!0})},x=(n,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of p(e))!L.call(n,i)&&i!==t&&f(n,i,{get:()=>e[i],enumerable:!(s=g(e,i))||s.enumerable});return n};var w=n=>x(f({},"__esModule",{value:!0}),n);var r=(n,e,t)=>v(n,typeof e!="symbol"?e+"":e,t);var S={};b(S,{ArtsLicenseManager:()=>l});function h(n){try{let e=new URL(n,window.location.origin);if(e.origin!==window.location.origin)return"";let t=e.pathname.replace(/[^a-zA-Z0-9/_\-.]/g,""),s=e.search.replace(/[^a-zA-Z0-9&=_\-.?+]/g,""),i=e.hash.replace(/[^a-zA-Z0-9&=_\-.?+]/g,"");return`${window.location.origin}${t}${s}${i}`}catch(e){return""}}var u=class{async sendRequest(e,t,s){try{return await(await fetch(e,{method:t,body:s})).json()}catch(i){throw console.error("License API error:",i),i}}handleRedirect(e){if(!e.success&&e.data.location){let t=h(e.data.location);return t?(window.location.href=t,!0):(window.location.reload(),!0)}return!1}};var m=class{constructor(e){r(this,"config");this.config=e}toggleFetchPending(e,t=!1){e.classList.toggle(this.config.classes.disabled,t),e.classList.toggle(this.config.classes.updating,t)}updateLicenseInfo(e,t){e.forEach((s,i)=>{switch(s.classList.add(this.config.classes.fadeOut),s.id){case"license-status":s.textContent=t.status||"";break;case"license-key":s.textContent=t.key||"";break;case"license-expires":s.textContent=t.expires||"";break;case"license-activations-left":s.textContent=t.activations_left||"";break;case"license-site-count":s.textContent=t.site_count||"";break;case"license-limit":s.textContent=t.license_limit||"";break;case"license-date-purchased":s.textContent=t.date_purchased||"";break;case"license-date-updates-provided-until":s.textContent=t.date_updates_provided_until||"";break}setTimeout(()=>{s.id!=="license-support-forum"&&s.id!=="license-renew-support"&&s.id!=="license-date-supported-until"&&s.classList.remove(this.config.classes.fadeOut)},this.config.animation.duration*1e3+i*(this.config.animation.stagger*1e3))})}updateSupportInfo(e,t,s,i,c){!e||!t||!s||setTimeout(()=>{i.is_support_provided?(t.classList.add(this.config.classes.hidden),e.classList.remove(this.config.classes.hidden),s.classList.add(this.config.classes.colorActive),s.classList.remove(this.config.classes.colorExpired)):(e.classList.add(this.config.classes.hidden),t.classList.remove(this.config.classes.hidden),s.classList.remove(this.config.classes.colorActive),s.classList.add(this.config.classes.colorExpired)),s.textContent=i.date_supported_until||"",e.classList.remove(this.config.classes.fadeOut),t.classList.remove(this.config.classes.fadeOut),s.classList.remove(this.config.classes.fadeOut)},this.config.animation.duration*1e3+c*(this.config.animation.stagger*1e3))}updateAdminNotice(e,t,s,i="success"){!e||!t||!s||(t.classList.add(this.config.classes.fadeOut),setTimeout(()=>{switch(e.classList.remove(this.config.classes.noticeSuccess,this.config.classes.noticeError,this.config.classes.noticeWarning,this.config.classes.noticeInfo),i){case"success":e.classList.add(this.config.classes.noticeSuccess);break;case"error":e.classList.add(this.config.classes.noticeError);break;case"warning":e.classList.add(this.config.classes.noticeWarning);break;case"info":e.classList.add(this.config.classes.noticeInfo);break}t.textContent=s,t.classList.remove(this.config.classes.fadeOut)},this.config.animation.duration*1e3))}updateLicenseInputs(e,t,s){e.forEach((i,c)=>{i.classList.add(this.config.classes.fadeOut),setTimeout(()=>{let a=i.querySelector("input");a&&(a.value=s.key||""),(!s.status||s.status==="deactivated")&&(t.classList.add(this.config.classes.fadeOut),setTimeout(()=>{t.classList.add(this.config.classes.hidden),this.toggleFetchPending(t,!1)},this.config.animation.duration*1e3)),i.classList.remove(this.config.classes.fadeOut)},this.config.animation.duration*1e3+c*(this.config.animation.stagger*1e3))})}};var l=class{constructor(){r(this,"config");r(this,"elements");r(this,"licenseService");r(this,"uiService");r(this,"formAction","");r(this,"formMethod","POST");this.config={selectors:{form:"#arts-license-form",clearButton:'[data-ajax-action="clear_license"]',adminNotice:".notice",noticeText:".notice__text"},classes:{fadeOut:"license-info-fade-out",hidden:"license-hidden",colorActive:"license-color-active",colorExpired:"license-color-expired",noticeSuccess:"notice-success",noticeError:"notice-error",noticeWarning:"notice-warning",noticeInfo:"notice-info",disabled:"disabled",updating:"updating"},animation:{duration:.2,stagger:.02}},this.licenseService=new u,this.uiService=new m(this.config),this.elements={form:document.querySelector(this.config.selectors.form),clearButton:null,notice:document.querySelector(this.config.selectors.adminNotice),noticeText:null},this.init()}init(){this.elements.notice&&(this.elements.noticeText=this.elements.notice.querySelector(this.config.selectors.noticeText),this.elements.noticeText&&this.elements.noticeText.classList.add("license-notice-text")),this.elements.form&&(this.elements.clearButton=this.elements.form.querySelector(this.config.selectors.clearButton),this.formAction=this.elements.form.getAttribute("data-action-ajax")||"",this.formMethod=this.elements.form.getAttribute("method")||"POST",this.setupFormListeners())}setupFormListeners(){this.elements.form&&(this.elements.form.addEventListener("submit",e=>{let t=e.submitter,s=new FormData(this.elements.form);if(t){let i=t.getAttribute("data-ajax-action"),c=t.getAttribute("name");c&&s.set("action",c),i==="refresh_license"?(e.preventDefault(),this.refreshLicense(t,s)):i==="clear_license"&&(e.preventDefault(),this.clearLicense(t,s))}}),this.elements.clearButton&&this.elements.clearButton.addEventListener("click",e=>{var s;e.preventDefault();let t=new CustomEvent("submit",{bubbles:!0,cancelable:!0});Object.defineProperty(t,"submitter",{value:this.elements.clearButton,writable:!1}),(s=this.elements.form)==null||s.dispatchEvent(t)}))}async refreshLicense(e,t){if(!this.elements.form)return;let s=[...this.elements.form.querySelectorAll(".license-info")],i=s.find(o=>o.id==="license-support-forum"),c=s.find(o=>o.id==="license-renew-support"),a=s.find(o=>o.id==="license-date-supported-until");try{this.uiService.toggleFetchPending(e,!0);let o=await this.licenseService.sendRequest(this.formAction,this.formMethod,t);if(this.licenseService.handleRedirect(o))return;if(o.success){let{data:d}=o;this.uiService.updateLicenseInfo(s,d),this.uiService.updateSupportInfo(i||null,c||null,a||null,d,s.length),this.elements.notice&&this.elements.noticeText&&d.message&&this.uiService.updateAdminNotice(this.elements.notice,this.elements.noticeText,d.message)}}catch(o){console.error(o),alert("An error occurred while refreshing the license.")}finally{this.uiService.toggleFetchPending(e,!1)}}async clearLicense(e,t){if(!this.elements.form)return;let s=[...this.elements.form.querySelectorAll(".license-input-wrapper")];try{this.uiService.toggleFetchPending(e,!0);let i=await this.licenseService.sendRequest(this.formAction,this.formMethod,t);if(this.licenseService.handleRedirect(i))return;if(i.success){let{data:c}=i;this.uiService.updateLicenseInputs(s,e,c),this.elements.notice&&this.elements.noticeText&&c.message&&this.uiService.updateAdminNotice(this.elements.notice,this.elements.noticeText,c.message)}}catch(i){console.error(i),alert("An error occurred while clearing the license.")}finally{this.uiService.toggleFetchPending(e,!1)}}};document.addEventListener("DOMContentLoaded",()=>{new l});return w(S);})();
return ArtsNoticeManager;}));