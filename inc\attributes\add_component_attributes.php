<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_add_component_attributes' ) ) {
	/**
	 * Get component attributes as an array.
	 *
	 * This method modifies the given attributes array by adding component-specific
	 * attributes based on the provided arguments. It ensures that the component
	 * name, options, and animation status are included in the attributes array.
	 *
	 * @param array        $attributes Existing attributes.
	 * @param array        $args {
	 *  Optional. Arguments to modify component attributes.
	 *
	 *  @type string $name         Component name. Default 'MyComponent'.
	 *  @type array  $options      Component options. Default empty array.
	 *  @type bool   $hasAnimation Whether the component has animation. Default false.
	 * }
	 * @param array|string $exclude_names Names to exclude from adding to attributes. Default empty array.
	 *
	 * @return array Modified attributes.
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::get_component_attributes()` method instead.
	 */
	function arts_add_component_attributes( $attributes = array(), $args = array() ) {
		return Utilities::get_component_attributes( $attributes, $args );
	}
}
