<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_filter( 'autoptimize_filter_html_noptimize', 'arts_ao_disable_html_minification_ajax' );
if ( ! function_exists( 'arts_ao_disable_html_minification_ajax' ) ) {
	/**
	 * Force disable HTML minification for Autoptimize plugin
	 * if AJAX transitions are enabled to avoid incorrect
	 * page rendering.
	 *
	 * @param bool $value The current value of HTML minification setting.
	 * @return bool The modified value of HTML minification setting.
	 */
	function arts_ao_disable_html_minification_ajax( $value ) {
		$ajax_enabled = Utilities::get_kit_settings( 'ajax_enabled', false );

		if ( $ajax_enabled ) {
			$value = true;
		}

		return $value;
	}
}
