<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_uploaded_font_files' ) ) {
	/**
	 * Retrieve all uploaded font attachments.
	 *
	 * @return array List of uploaded font files with their ID, permalink, and filetype.
	 * @deprecated 2.0.0 Use `Utilities::get_uploaded_fonts()` method instead.
	 */
	function arts_get_uploaded_font_files() {
		return Utilities::get_uploaded_fonts();
	}
}
