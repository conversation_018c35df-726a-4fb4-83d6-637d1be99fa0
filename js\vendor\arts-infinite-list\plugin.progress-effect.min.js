"use strict";(self.webpackChunkArtsInfiniteList=self.webpackChunkArtsInfiniteList||[]).push([[190],{105:(t,e,i)=>{i.r(e),i.d(e,{default:()=>o});var r=i(199);const n={preset:"arc",intensity:.25,multiLane:!1};class o extends r.v{constructor({autoLoad:t=!1,container:e,options:i,controller:r,config:o}){super({autoLoad:t,container:e,options:i,controller:r,config:o,defaults:n}),this.init()}init(){if(!this.enabled)if(this.config.multiLane)for(let t=0;t<this.controller.lanes.length;t++)"preset"in this.config.multiLane[t]&&this._registerTransformHook(this.config.multiLane[t].preset,t);else{const t=this.getConfigOption("preset");this._registerTransformHook(t)}}destroy(){this.enabled&&this.controller.removeTransformHooks()}_registerTransformHook(t,e){switch(t){case"arc":this.controller.addTransformHook(this._transformerArc.bind(this),e);break;case"snake":this.controller.addTransformHook(this._transformerSnake.bind(this),e);break;case"horizontalToVertical":this.controller.addTransformHook(this._transformerHorizontalToVertical.bind(this),e)}}_transformerArc({indexLane:t,indexItem:e,progressItem:i,translateItem:r,laneGeometry:n}){const o=this.getConfigOption("intensity",t);let s,a=i*o*50;return"horizontal"===this.options.direction&&(s=o<0?"center top":"center bottom",r.y+=i*i*o*n.visibleArea.height),"vertical"===this.options.direction&&(s=o<0?"right center":"left center",r.x-=i*i*o*n.visibleArea.width),{translate:r,rotate:a,origin:s}}_transformerSnake({indexLane:t,indexItem:e,progressItem:i,translateItem:r,laneGeometry:n}){const o=this.getConfigOption("intensity",t),s=r,a={x:0,y:0,z:i*o*100};return i>0?(a.z=-a.z,"horizontal"===this.options.direction&&(s.y-=i*i*o*n.visibleArea.height),"vertical"===this.options.direction&&(s.x+=i*i*o*n.visibleArea.width)):("horizontal"===this.options.direction&&(s.y+=i*i*o*n.visibleArea.height),"vertical"===this.options.direction&&(s.x-=i*i*o*n.visibleArea.width)),{translate:s,rotate:a,origin:"center center"}}_transformerHorizontalToVertical({indexLane:t,indexItem:e,progressItem:i,translateItem:r,laneGeometry:n}){const o=r;return o.y=-o.x,{translate:o}}}}}]);