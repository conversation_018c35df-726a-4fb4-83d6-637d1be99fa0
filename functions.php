<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

require_once __DIR__ . '/vendor/autoload.php';

/**
 * Theme Constants
 */
if ( ! defined( 'ARTS_THEME_SLUG' ) ) {
	define( 'ARTS_THEME_SLUG', 'asli' );
}

if ( ! defined( 'ARTS_THEME_PATH' ) ) {
	define( 'ARTS_THEME_PATH', get_template_directory() );
}

if ( ! defined( 'ARTS_THEME_URL' ) ) {
	define( 'ARTS_THEME_URL', get_template_directory_uri() );
}

if ( ! defined( 'ARTS_THEME_VERSION' ) ) {
	$theme_version = \Arts\Utilities\Utilities::get_parent_theme_version();

	define( 'ARTS_THEME_VERSION', $theme_version );
}

require_once ARTS_THEME_PATH . '/inc/constants.php';

/**
 * Polyfills
 */
require_once ARTS_THEME_PATH . '/inc/polyfills/get_page_by_title.php';

/**
 * Plugin config: Elementor
 */
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/add_custom_icons_elementor_library.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/is_built_with_elementor.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/is_elementor_editor_active.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/is_feature_active.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/get_document_option.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/integrate_canvas_template.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/register_locations.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/set_custom_fonts_display.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/update_kit_settings.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/get_global_color_value.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/get_kit_settings.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/elementor/get_body_document_option.php';

/**
 * Customizer
 */
require_once ARTS_THEME_PATH . '/inc/customize/customize_register.php';

/**
 * Plugin config: Intuitive Custom Posts Order
 */
require_once ARTS_THEME_PATH . '/inc/config-plugins/intuitive-cpo/fix_capabilities.php';

/**
* Plugin config: Advanced Custom Fields
*/
require_once ARTS_THEME_PATH . '/inc/config-plugins/acf/fields.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/acf/helpers.php';

/**
 * Plugin config: Autoptimize
 */
require_once ARTS_THEME_PATH . '/inc/config-plugins/autoptimize/disable_css_aggregation_ajax.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/autoptimize/disable_html_minification_ajax.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/autoptimize/js_exclude.php';

/**
 * Plugin config: Contact Form 7
 */
require_once ARTS_THEME_PATH . '/inc/config-plugins/contact-form-7/add_sc_attributes.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/contact-form-7/custom_modal_windows.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/contact-form-7/unwrap_form_fields.php';

/**
 * Plugin config: TranslatePress Multilingual
 */
require_once ARTS_THEME_PATH . '/inc/config-plugins/translatepress-multilingual/add_cursor_follower_translation.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/translatepress-multilingual/disable_dynamic_translation.php';

/**
 * Plugin config: WooCommerce
 */
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/ajax-add-product-review.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/ajax-add-to-cart-grouped.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/ajax-add-to-cart-variable.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/filters-form-field.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/filters-quantity-input.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/filters-sitewide-notice.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/fragments.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/helpers.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/hooks-add-to-cart.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/hooks-main.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/hooks-shop-loop.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/hooks-shop-loop-item.php';
require_once ARTS_THEME_PATH . '/inc/config-plugins/woocommerce/hooks-single-product.php';

/**
 * Plugin config: WP Forms
 */
require_once ARTS_THEME_PATH . '/inc/config-plugins/wpforms/wpforms.php';

/**
 * Plugin config: LiteSpeed Cache
 */
require_once ARTS_THEME_PATH . '/inc/config-plugins/litespeed-cache/bypass_html_minification_ajax.php';

/**
 * Options
 */
require_once ARTS_THEME_PATH . '/inc/options/get_ajax_transitions_options.php';
require_once ARTS_THEME_PATH . '/inc/options/get_animations_options.php';
require_once ARTS_THEME_PATH . '/inc/options/get_contact_form_7_options.php';
require_once ARTS_THEME_PATH . '/inc/options/get_cursor_follower_options.php';
require_once ARTS_THEME_PATH . '/inc/options/get_drawing_circle_template.php';
require_once ARTS_THEME_PATH . '/inc/options/get_lightbox_gallery_options.php';
require_once ARTS_THEME_PATH . '/inc/options/get_marquee_options.php';
require_once ARTS_THEME_PATH . '/inc/options/get_preloader_options.php';
require_once ARTS_THEME_PATH . '/inc/options/get_smooth_scroll_options.php';
require_once ARTS_THEME_PATH . '/inc/options/get_virtual_scroll_options.php';

/**
 * Comments
 */
require_once ARTS_THEME_PATH . '/inc/comments/class-arts-walker-comment.php';
require_once ARTS_THEME_PATH . '/inc/comments/add_ajax_comment_class.php';
require_once ARTS_THEME_PATH . '/inc/comments/ajax_post_comment.php';
require_once ARTS_THEME_PATH . '/inc/comments/comment_form_default_fields.php';
require_once ARTS_THEME_PATH . '/inc/comments/comment_form_defaults.php';
require_once ARTS_THEME_PATH . '/inc/comments/comment_form_nounce.php';

/**
 * Pagination
 */
require_once ARTS_THEME_PATH . '/inc/pagination/modify_pagination_links_attributes.php';
require_once ARTS_THEME_PATH . '/inc/pagination/change_base_pagenum_link.php';
require_once ARTS_THEME_PATH . '/inc/pagination/get_posts_pagination.php';

/**
 * AJAX password protected post
 */
require_once ARTS_THEME_PATH . '/inc/password/post_password.php';

/**
 * Blog
 */
require_once ARTS_THEME_PATH . '/inc/blog/add_pingback_url.php';
require_once ARTS_THEME_PATH . '/inc/blog/get_post_author.php';
require_once ARTS_THEME_PATH . '/inc/blog/password_form.php';
require_once ARTS_THEME_PATH . '/inc/blog/wrap_category_archive_count.php';

/**
 * Attributes renderers
 */
require_once ARTS_THEME_PATH . '/inc/attributes/add_component_attributes.php';

/**
 * Theme Helpers & Enhancements
 */
require_once ARTS_THEME_PATH . '/inc/markup/get_body_styles_model.php';
require_once ARTS_THEME_PATH . '/inc/markup/get_loading_spinner_attributes.php';
require_once ARTS_THEME_PATH . '/inc/markup/get_main_container_attributes.php';
require_once ARTS_THEME_PATH . '/inc/markup/get_main_content_attributes.php';

require_once ARTS_THEME_PATH . '/inc/helpers/get_license_args_url.php';
require_once ARTS_THEME_PATH . '/inc/helpers/camel_to_kebab_case.php';
require_once ARTS_THEME_PATH . '/inc/helpers/print_attributes.php';
require_once ARTS_THEME_PATH . '/inc/helpers/parse_args_recursive.php';
require_once ARTS_THEME_PATH . '/inc/helpers/get_lazy_image_attributes.php';
require_once ARTS_THEME_PATH . '/inc/helpers/get_woocommerce_urls.php';
require_once ARTS_THEME_PATH . '/inc/helpers/get_ajax_prevent_rules.php';
require_once ARTS_THEME_PATH . '/inc/helpers/get_ajax_update_head_nodes.php';
require_once ARTS_THEME_PATH . '/inc/helpers/is_referer_from_same_domain.php';
require_once ARTS_THEME_PATH . '/inc/helpers/get_preloader_loading_steps.php';
require_once ARTS_THEME_PATH . '/inc/helpers/get_thumbnail_size_from_site_settings.php';
require_once ARTS_THEME_PATH . '/inc/helpers/is_preloader_enabled.php';
require_once ARTS_THEME_PATH . '/inc/helpers/is_cursor_follower_enabled.php';
require_once ARTS_THEME_PATH . '/inc/helpers/get_page_titles.php';
require_once ARTS_THEME_PATH . '/inc/helpers/get_uploaded_font_files.php';

/**
 * Additional <body> classes
 */
require_once ARTS_THEME_PATH . '/inc/core/add_body_classes.php';
require_once ARTS_THEME_PATH . '/inc/core/body_open.php';

/**
 * Remove Gutenberg "Duotone" filters
 */
require_once ARTS_THEME_PATH . '/inc/core/remove_duotone_filters.php';

/**
 * Frontend Styles & Scripts
 */
require_once ARTS_THEME_PATH . '/inc/frontend/add_preload_prefetch_links.php';
require_once ARTS_THEME_PATH . '/inc/frontend/enqueue_css_no_elementor.php';
require_once ARTS_THEME_PATH . '/inc/frontend/enqueue_css.php';
require_once ARTS_THEME_PATH . '/inc/frontend/enqueue_elementor_preview_js.php';
require_once ARTS_THEME_PATH . '/inc/frontend/enqueue_js.php';
require_once ARTS_THEME_PATH . '/inc/frontend/enqueue_polyfills.php';
require_once ARTS_THEME_PATH . '/inc/frontend/inject_lcp_image_script.php';
require_once ARTS_THEME_PATH . '/inc/frontend/inject_noscript_styles.php';
require_once ARTS_THEME_PATH . '/inc/frontend/inline_theme_options.php';
require_once ARTS_THEME_PATH . '/inc/frontend/inline_theme_assets.php';
require_once ARTS_THEME_PATH . '/inc/frontend/inline_theme_components.php';

/**
 * Load Required Plugins
 */
require_once ARTS_THEME_PATH . '/inc/tgm/load_plugins.php';

/**
 * Nav Menu
 */
require_once ARTS_THEME_PATH . '/inc/navigation/class-walker-nav-menu-overlay.php';
require_once ARTS_THEME_PATH . '/inc/navigation/register_menus.php';

/**
 * Supported Theme Features
 */
require_once ARTS_THEME_PATH . '/inc/core/theme_support.php';

/**
 * Widget Areas
 */
require_once ARTS_THEME_PATH . '/inc/core/widget_areas.php';

/**
 * Template parts accessible via AJAX calls
 */
require_once ARTS_THEME_PATH . '/inc/core/ajax_partials.php';

/**
 * Demo Import
 */
require_once ARTS_THEME_PATH . '/inc/importer/importer.php';

/**
 * Theme License & Updates Manager
 */
require_once ARTS_THEME_PATH . '/inc/updater/updater.php';
