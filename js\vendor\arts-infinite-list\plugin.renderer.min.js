/*! For license information please see plugin.renderer.min.js.LICENSE.txt */
"use strict";(self.webpackChunkArtsInfiniteList=self.webpackChunkArtsInfiniteList||[]).push([[324],{170:(t,e,n)=>{function i(t){if(t&&t.constructor===Array){var e=t.filter((function(t){return"number"==typeof t})).filter((function(t){return!isNaN(t)}));if(6===t.length&&6===e.length){var n=r();return n[0]=e[0],n[1]=e[1],n[4]=e[2],n[5]=e[3],n[12]=e[4],n[13]=e[5],n}if(16===t.length&&16===e.length)return t}throw new TypeError("Expected a `number[]` with length 6 or 16.")}function r(){for(var t=[],e=0;e<16;e++)e%5==0?t.push(1):t.push(0);return t}function s(t,e){for(var n=i(t),r=i(e),s=[],a=0;a<4;a++)for(var o=[n[a],n[a+4],n[a+8],n[a+12]],l=0;l<4;l++){var h=4*l,c=[r[h],r[h+1],r[h+2],r[h+3]],u=o[0]*c[0]+o[1]*c[1]+o[2]*c[2]+o[3]*c[3];s[a+h]=u}return s}function a(t){var e=Math.PI/180*t,n=r();return n[0]=n[5]=Math.cos(e),n[1]=n[4]=Math.sin(e),n[4]*=-1,n}function o(t,e){var n=r();return n[0]=t,n[5]="number"==typeof e?e:t,n}n.r(e),n.d(e,{default:()=>c});var l=n(199);const h={};class c extends l.v{constructor({autoLoad:t=!1,options:e,view:n,config:i}){super({autoLoad:t,options:e,view:n,config:i,defaults:h}),this._handlers={updateView:this._onUpdateView.bind(this),renderAll:this._renderAll.bind(this)},this.init()}init(){this.enabled||(this._attachEvents(),this._attachToggleViewEvents(),this._renderAll(),this.enabled=!0)}enable(){this.enabled||(this._attachEvents(),this._renderAll(),this.enabled=!0)}disable(){this.enabled&&(this._detachEvents(),this.enabled=!1)}destroy(){this.enabled&&(this._detachEvents(),this._detachToggleViewEvents(),this.enabled=!1)}update(){this.render()}render(t,e){t&&t in this.view.current?e?e in this.view.current[t].items&&this.renderItem(t,e):this.renderLane(t):this._renderAll()}renderLane(t){Object.keys(this.view.current[t]).forEach((e=>{this.renderItem(t,e)}))}renderItem(t,e){this._applyStyles(this.view.current[t].items[e])}_onUpdateView({indexLane:t,indexItem:e}){this.renderItem(t,e)}_attachEvents(){this.view.on("update",this._handlers.updateView)}_detachEvents(){this.view.off("update",this._handlers.updateView)}_renderAll(){Object.keys(this.view.current).forEach((t=>{Object.keys(this.view.current[t].items).forEach((e=>{this._applyStyles(this.view.current[t].items[e])}))}))}_applyStyles(t){"visible"in t&&c.setElementsVisible({elements:[t.element],visible:t.visible}),"opacity"in t&&null!==t.opacity&&c.setElementsOpacity({elements:[t.element],opacity:t.opacity}),"transform"in t&&c.setElementsTransform({elements:[t.element],transform:t.transform})}static setElementsVisible({elements:t,visible:e=!0}){if(t.length)for(const n of t)n&&(e?(n.style.visibility="visible",n.style.opacity=null):(n.style.visibility="hidden",n.style.opacity="0"))}static setElementsOpacity({elements:t,opacity:e}){if(t.length)for(const n of t)n&&(n.style.opacity=`${e}`)}static setElementsTransform({elements:t,transform:e}){if(t.length){let n=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],i=null;"translate"in e&&("number"==typeof e.translate&&(n=s(n,function(t,e){var n=r();return n[12]=t,n}(e.translate))),"object"==typeof e.translate&&(n=s(n,function(t,e,n){var i=r();return void 0!==t&&void 0!==e&&void 0!==n&&(i[12]=t,i[13]=e,i[14]=n),i}(e.translate.x,e.translate.y,e.translate.z)))),"scale"in e&&("number"==typeof e.scale&&(n=s(n,o(e.scale))),"object"==typeof e.scale&&(n=o(e.scale.x,e.scale.y))),"rotate"in e&&("number"==typeof e.rotate&&(n=s(n,a(e.rotate))),"object"==typeof e.rotate&&("x"in e.rotate&&(n=s(n,function(t){var e=Math.PI/180*t,n=r();return n[5]=n[10]=Math.cos(e),n[6]=n[9]=Math.sin(e),n[9]*=-1,n}(e.rotate.x))),"y"in e.rotate&&(n=s(n,function(t){var e=Math.PI/180*t,n=r();return n[0]=n[10]=Math.cos(e),n[2]=n[8]=Math.sin(e),n[2]*=-1,n}(e.rotate.y))),"z"in e.rotate&&(n=s(n,a(e.rotate.z))))),"skew"in e&&("number"==typeof e.skew&&(n=s(n,function(t,e){var n=Math.PI/180*t,i=r();return i[4]=Math.tan(n),i}(e.skew))),"object"==typeof e.skew&&("x"in e.skew&&(n=s(n,function(t){var e=Math.PI/180*t,n=r();return n[4]=Math.tan(e),n}(e.skew.x))),"y"in e.skew&&(n=s(n,function(t){var e=Math.PI/180*t,n=r();return n[1]=Math.tan(e),n}(e.skew.y))))),"origin"in e&&"string"==typeof e.origin&&(i=e.origin);for(const e of t)e&&(i&&(e.style.transformOrigin=i),e.style.transform=`matrix3d(${n.toString()})`)}}}}}]);