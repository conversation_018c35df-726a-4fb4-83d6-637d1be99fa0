<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$titles                        = Utilities::get_page_titles();
$page_title                    = $titles['title'];
$page_subtitle                 = $titles['subtitle'];
$page_description              = $titles['description'];
$has_post_thumbnail            = has_post_thumbnail();
$has_animation                 = true;
$blog_masthead_marquee_enabled = true;

$section_attributes = array(
	'class' => array(
		'masthead',
		'masthead_archive',
		'pt-header-height',
		'text-center',
	),
	'id'    => 'page-masthead',
);

$section_attributes = Utilities::get_component_attributes(
	$section_attributes,
	array(
		'name'         => 'Masthead',
		'options'      => array(),
		'hasAnimation' => false,
	),
);

$marquee_container_attributes = array(
	'class' => array(
		'marquee-header',
		'overflow-hidden',
		'py-2',
		'js-marquee-header',
		'js-masthead__ajax-updated-content',
	),
);

$heading_attributes = array(
	'class' => array(
		'entry-title',
		'masthead__heading',
		'my-0',
		'h1',
	),
);

$marquee_container_attributes = Utilities::get_component_attributes(
	$marquee_container_attributes,
	array(
		'name'         => 'MarqueeHeader',
		'options'      => arts_get_marquee_options(),
		'hasAnimation' => true,
	)
);

$heading_attributes['class'][] = 'js-marquee-header__label';

$image_wrapper = array(
	'class' => array(
		'overflow-hidden',
		'h-900',
		'w-100',
		'position-relative',
		'js-ajax-transition-element',
		'js-masthead__animation-mask',
	),
);

$image_wrapper = Utilities::get_component_attributes(
	$image_wrapper,
	array(
		'name'    => 'Parallax',
		'options' => array(
			'inner' => array(
				'factor' => array(
					'x' => 0,
					'y' => 0.15,
				),
			),
		),
	)
);

$thumbnail_args = array(
	'id'           => get_post_thumbnail_id(),
	'video'        => Utilities::acf_get_field( 'featured_video' ),
	'type'         => 'full',
	'lazy_wrapper' => array(
		'class' => array(
			'js-masthead__animation-mask-wrapper',
			'overflow-hidden',
			'w-100',
			'h-100',
			'js-ajax-transition-element__mask',
		),
	),
	'image'        => array(
		'class' => array(
			'lazy',
			'of-cover-absolute',
			'js-ajax-transition-element__media',
			'js-parallax__inner',
		),
	),
);
?>

<div <?php Utilities::print_attributes( $section_attributes ); ?>>
	<div class="js-masthead__ajax-updated-wrapper overflow-hidden position-relative">
		<?php if ( $blog_masthead_marquee_enabled ) : ?>
			<!-- Marquee header -->
			<div <?php Utilities::print_attributes( $marquee_container_attributes ); ?>>
				<div class="marquee-header__inner js-marquee-header__wrapper">
					<div class="overflow-hidden">
						<div class="marquee-header__lane js-marquee-header__list-lane">
							<div class="marquee-header__item d-inline-block js-marquee-header__list-item">
								<h1 <?php Utilities::print_attributes( $heading_attributes ); ?>><?php echo esc_html( $page_title ); ?></h1>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- - Marquee header -->
		<?php else : ?>
			<div class="container py-2 js-masthead__ajax-updated-content">
				<h1 <?php Utilities::print_attributes( $heading_attributes ); ?>><?php echo esc_html( $page_title ); ?></h1>
			</div>
		<?php endif; ?>
	</div>
	<div class="w-100 bb-auto-opacity-solid"></div>

	<?php if ( $has_post_thumbnail ) : ?>
		<div class="masthead__media mt-gutter-vertical px-gutters">
			<!-- Featured media -->
			<div <?php Utilities::print_attributes( $image_wrapper ); ?>>
				<?php get_template_part( 'template-parts/lazy/lazy', 'media', $thumbnail_args ); ?>
			</div>
			<!-- Featured media -->
		</div>
	<?php endif; ?>
</div>
