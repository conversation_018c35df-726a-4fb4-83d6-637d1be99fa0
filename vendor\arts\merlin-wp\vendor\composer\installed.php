<?php return array(
    'root' => array(
        'name' => 'arts/merlin-wp',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '13e2ec00fde37e4932d4e70fddb16dc27c9afac8',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'arts/merlin-wp' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '13e2ec00fde37e4932d4e70fddb16dc27c9afac8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arts/utilities' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '282313d41a292caa3ae623a9c5ff5fb6485f3669',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/utilities',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arts/wp-content-importer-v2' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '3ebe9604477edc78487cf40b0be66b71aae9ac31',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arts/wp-content-importer-v2',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.x-dev',
            'version' => '2.9999999.9999999.9999999-dev',
            'reference' => 'cd82b5069148dd811ef54b4b92ce1b3aad84209b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
            ),
        ),
    ),
);
