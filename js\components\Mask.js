function _0x4b21(_0x2225e0,_0x322381){const _0x499a4f=_0x499a();return _0x4b21=function(_0x4b21d9,_0xd2ef19){_0x4b21d9=_0x4b21d9-0x1b1;let _0x63e20=_0x499a4f[_0x4b21d9];return _0x63e20;},_0x4b21(_0x2225e0,_0x322381);}const _0x42effd=_0x4b21;(function(_0x2e5aa9,_0x5a2c7d){const _0x5c0c50=_0x4b21,_0xcd68d4=_0x2e5aa9();while(!![]){try{const _0x580878=-parseInt(_0x5c0c50(0x1e9))/0x1*(-parseInt(_0x5c0c50(0x1c1))/0x2)+-parseInt(_0x5c0c50(0x213))/0x3+-parseInt(_0x5c0c50(0x1b9))/0x4+-parseInt(_0x5c0c50(0x1ca))/0x5*(parseInt(_0x5c0c50(0x1d0))/0x6)+parseInt(_0x5c0c50(0x1de))/0x7+-parseInt(_0x5c0c50(0x1c9))/0x8+parseInt(_0x5c0c50(0x1e6))/0x9*(parseInt(_0x5c0c50(0x1e2))/0xa);if(_0x580878===_0x5a2c7d)break;else _0xcd68d4['push'](_0xcd68d4['shift']());}catch(_0x227e65){_0xcd68d4['push'](_0xcd68d4['shift']());}}}(_0x499a,0x89521));function _0x499a(){const _0x5c7e27=['6250944aFbuMq','loaded','absolute','innerSelectors','10612030kjnmUW','none','dataReady','offsetTop','9lwEhKL','add','bottom','34159ZlKqYp','element','scaleX','animation/end','preloaderRef','_repaintMask','_handlers','ellipse(','_isShapeOval','postTask','offsetLeft','_updateRect','_clean','resizeInstance','update','left','target','_initSplitText','_detachEvents','right','addAJAXStartEventListener','_updateMaskClipPath','getBoundingClientRect','updateMask','observe','before','utilities','\x20###\x20-\x20Mask\x20Clone\x20###\x20','borderRadius','bind','clone','push','px\x20round\x20','px)','props','position','parentElement','js-mask__clone','disconnect','all','px\x20at\x20','animation/start','410205dAOXSc','_cloneMask','clipPath','.js-mask__source','options','createComment','color','set','_cloneTarget','top,left,width,height,margin,zIndex,pointerEvents,clipPath,transform','removeEventListener','Preloader','setMask','parent','319856PDqdHj','init','assign','top','source','repaintMask','addEventListener','rect','6RdFskQ','px\x20','.js-mask__target','then','after','destroy','addAJAXEndEventListener','_updateProps','8041544cgtYDi','12725dieLUL','maskedEl','_attachEvents','relative','string','getDebounceTime','642kRIGBF','borderRadiusPixels','width','\x20###\x20Mask\x20Clone\x20###\x20','finally','debounce','replace','destroySplitText','classList','zIndex','elements','scaleY','var(--color-accent-dark-theme)','getProperty'];_0x499a=function(){return _0x5c7e27;};return _0x499a();}export default class Mask extends BaseComponent{constructor({name:_0x3346e3,loadInnerComponents:_0x264f1a,loadAfterSyncStyles:_0x20ee6c,parent:_0x34b4de,element:_0x169b21}){const _0x529ad3=_0x4b21;super({'name':_0x3346e3,'loadInnerComponents':_0x264f1a,'loadAfterSyncStyles':_0x20ee6c,'parent':_0x34b4de,'element':_0x169b21,'defaults':{'scaleX':0x1,'scaleY':0x1,'autoParentRelative':!![],'clone':!![],'color':_0x529ad3(0x1dc)},'innerElements':{'target':_0x529ad3(0x1c3),'source':_0x529ad3(0x216)}}),this[_0x529ad3(0x1c0)]={'source':{'width':0x0,'height':0x0,'top':0x0,'right':0x0,'bottom':0x0,'left':0x0},'target':{'width':0x0,'height':0x0,'top':0x0,'right':0x0,'bottom':0x0,'left':0x0}},this[_0x529ad3(0x20b)]={'offsetLeft':0x0,'offsetTop':0x0,'borderRadius':0x0,'zIndex':0x0},this['_handlers']={'updateMask':app['utilities'][_0x529ad3(0x1d5)](this[_0x529ad3(0x1fe)][_0x529ad3(0x206)](this),app[_0x529ad3(0x203)][_0x529ad3(0x1cf)](0x320)),'repaintMask':this['_repaintMask'][_0x529ad3(0x206)](this)},this[_0x529ad3(0x1e4)][_0x529ad3(0x1d4)](()=>{const _0x4a9bff=_0x529ad3;this['maskedEl']=null,this[_0x4a9bff(0x1eb)]=this['options'][_0x4a9bff(0x1eb)]||0x1,this[_0x4a9bff(0x1db)]=this[_0x4a9bff(0x217)][_0x4a9bff(0x1db)]||0x1,this['setup']();});}[_0x42effd(0x1ba)](){return new Promise(_0x871d1a=>{const _0xd02bea=_0x4b21;this[_0xd02bea(0x1da)][_0xd02bea(0x1bd)][0x0]&&this[_0xd02bea(0x1da)]['target'][0x0]?(this['updateRef'](_0xd02bea(0x1ed),_0xd02bea(0x1b6)),!!this[_0xd02bea(0x217)][_0xd02bea(0x207)]?this[_0xd02bea(0x1b3)]()[_0xd02bea(0x1d4)](()=>{const _0x2e0696=_0xd02bea;this['_attachEvents'](),this[_0x2e0696(0x1fe)](),_0x871d1a(!![]);}):(this[_0xd02bea(0x1cb)]=this[_0xd02bea(0x1da)]['target'][0x0],this['_attachEvents'](),this[_0xd02bea(0x1fe)](),_0x871d1a(!![]))):_0x871d1a(!![]);});}[_0x42effd(0x1c6)](){return new Promise(_0x6b796e=>{const _0x5a2b6b=_0x4b21;this['_detachEvents'](),this[_0x5a2b6b(0x1f5)]()[_0x5a2b6b(0x1d4)](()=>_0x6b796e(!![]));});}[_0x42effd(0x1f7)](){const _0x3cbdcb=_0x42effd;this[_0x3cbdcb(0x1da)][_0x3cbdcb(0x1bd)][0x0]&&this[_0x3cbdcb(0x1da)]['target'][0x0]&&this[_0x3cbdcb(0x1fe)]();}[_0x42effd(0x1cc)](){const _0x5e84c5=_0x42effd;this[_0x5e84c5(0x1f6)]=new ResizeObserver(this[_0x5e84c5(0x1ef)]['updateMask']),this[_0x5e84c5(0x1f6)][_0x5e84c5(0x201)](this['element']),this[_0x5e84c5(0x1f6)][_0x5e84c5(0x201)](this[_0x5e84c5(0x1da)]['source'][0x0]),this[_0x5e84c5(0x1b8)]&&this[_0x5e84c5(0x1b8)][_0x5e84c5(0x1ea)]&&(this[_0x5e84c5(0x1b8)][_0x5e84c5(0x1ea)][_0x5e84c5(0x1bf)](_0x5e84c5(0x1ec),this[_0x5e84c5(0x1ef)][_0x5e84c5(0x1be)]),this[_0x5e84c5(0x1b8)][_0x5e84c5(0x1ea)][_0x5e84c5(0x1bf)]('animation/update',this[_0x5e84c5(0x1ef)][_0x5e84c5(0x1be)]),this[_0x5e84c5(0x1b8)]['element']['addEventListener'](_0x5e84c5(0x212),this[_0x5e84c5(0x1ef)][_0x5e84c5(0x200)])),this['preloaderRef']&&this[_0x5e84c5(0x1ed)][_0x5e84c5(0x1df)][_0x5e84c5(0x1d4)](this[_0x5e84c5(0x1ef)][_0x5e84c5(0x200)]),app[_0x5e84c5(0x203)][_0x5e84c5(0x1fd)](this[_0x5e84c5(0x1fb)]['bind'](this)),app[_0x5e84c5(0x203)][_0x5e84c5(0x1c7)](this['_handlers'][_0x5e84c5(0x200)]);}['_detachEvents'](){const _0x31b9a7=_0x42effd;this[_0x31b9a7(0x1f6)]&&(this['resizeInstance'][_0x31b9a7(0x20f)](),this[_0x31b9a7(0x1f6)]=null),this['parent']&&this['parent'][_0x31b9a7(0x1ea)]&&(this[_0x31b9a7(0x1b8)][_0x31b9a7(0x1ea)][_0x31b9a7(0x1b5)]('animation/end',this['_handlers'][_0x31b9a7(0x1be)]),this[_0x31b9a7(0x1b8)][_0x31b9a7(0x1ea)]['removeEventListener']('animation/update',this[_0x31b9a7(0x1ef)][_0x31b9a7(0x1be)]),this['parent']['element'][_0x31b9a7(0x1b5)]('animation/start',this[_0x31b9a7(0x1ef)][_0x31b9a7(0x200)]));}[_0x42effd(0x1b3)](){return new Promise(_0x410b22=>{const _0x525e16=_0x4b21;this[_0x525e16(0x1d7)]()[_0x525e16(0x1c4)](()=>this['_cloneMask']())[_0x525e16(0x1c4)](()=>this[_0x525e16(0x1fa)]())['finally'](()=>_0x410b22(!![]));});}[_0x42effd(0x214)](){return new Promise(_0x5ebc57=>{const _0xbe2bea=_0x4b21,_0x2466e3=[],_0x109726=this[_0xbe2bea(0x1da)]['target'][0x0][_0xbe2bea(0x20d)],_0x23ba85=document['createComment'](_0xbe2bea(0x1d3)),_0x5c6a5f=document[_0xbe2bea(0x218)](_0xbe2bea(0x204)),_0x4f7fe8=scheduler['postTask'](()=>{const _0x2a5f48=_0xbe2bea;this[_0x2a5f48(0x1cb)]=this[_0x2a5f48(0x1da)][_0x2a5f48(0x1f9)][0x0]['cloneNode'](!![]);});_0x2466e3[_0xbe2bea(0x208)](_0x4f7fe8);const _0x8dfa02=scheduler[_0xbe2bea(0x1f2)](()=>{const _0x48af1f=_0xbe2bea;this[_0x48af1f(0x1cb)][_0x48af1f(0x1d8)]['add'](_0x48af1f(0x20e));});_0x2466e3[_0xbe2bea(0x208)](_0x8dfa02);const _0x3567ed=scheduler[_0xbe2bea(0x1f2)](()=>{const _0x387365=_0xbe2bea;this[_0x387365(0x1cb)][_0x387365(0x1d8)]['remove'](this[_0x387365(0x1e1)][_0x387365(0x1f9)][_0x387365(0x1d6)](/\./g,''));});_0x2466e3[_0xbe2bea(0x208)](_0x3567ed);const _0x28de5c=scheduler[_0xbe2bea(0x1f2)](()=>{const _0x76e756=_0xbe2bea;this[_0x76e756(0x1cb)]['style'][_0x76e756(0x20c)]=_0x76e756(0x1e0);});_0x2466e3['push'](_0x28de5c);if(typeof this[_0xbe2bea(0x217)][_0xbe2bea(0x1b1)]===_0xbe2bea(0x1ce)){const _0x120e41=scheduler[_0xbe2bea(0x1f2)](()=>{const _0x591bad=_0xbe2bea;this[_0x591bad(0x1cb)]['style']['color']=this[_0x591bad(0x217)]['color'],this['maskedEl'][_0x591bad(0x1d8)][_0x591bad(0x1e7)]('js-mask__clone_has-color');});_0x2466e3[_0xbe2bea(0x208)](_0x120e41);}if(!!this[_0xbe2bea(0x217)]['autoParentRelative']){const _0x3e1ab6=scheduler[_0xbe2bea(0x1f2)](()=>{const _0x4b61c4=_0xbe2bea;_0x109726&&window['getComputedStyle'](_0x109726)[_0x4b61c4(0x20c)]!=='relative'&&(_0x109726['style'][_0x4b61c4(0x20c)]=_0x4b61c4(0x1cd));});_0x2466e3[_0xbe2bea(0x208)](_0x3e1ab6);}const _0x2acb56=scheduler['postTask'](()=>{const _0x34db72=_0xbe2bea;this[_0x34db72(0x1da)][_0x34db72(0x1f9)][0x0][_0x34db72(0x1c5)](this['maskedEl']);});_0x2466e3[_0xbe2bea(0x208)](_0x2acb56);const _0x28ab25=scheduler[_0xbe2bea(0x1f2)](()=>{const _0x70d49b=_0xbe2bea;this[_0x70d49b(0x1cb)][_0x70d49b(0x202)](_0x23ba85);});_0x2466e3['push'](_0x28ab25);const _0xf32d8d=scheduler[_0xbe2bea(0x1f2)](()=>{this['maskedEl']['after'](_0x5c6a5f);});_0x2466e3[_0xbe2bea(0x208)](_0xf32d8d),Promise[_0xbe2bea(0x210)](_0x2466e3)[_0xbe2bea(0x1d4)](()=>_0x5ebc57(!![]));});}[_0x42effd(0x1fe)](){return new Promise(_0x38afbe=>{const _0x150b06=_0x4b21;this[_0x150b06(0x1f5)]()['then'](()=>this[_0x150b06(0x1f4)]())['then'](()=>this[_0x150b06(0x1c8)]())['then'](()=>this[_0x150b06(0x1b7)]())[_0x150b06(0x1d4)](()=>{_0x38afbe(!![]);});});}[_0x42effd(0x1c8)](){return new Promise(_0x57d237=>{const _0x21cfb5=_0x4b21;scheduler[_0x21cfb5(0x1f2)](()=>{const _0x5e017c=_0x21cfb5,{offsetLeft:_0x4339b3,offsetTop:_0x4c07aa}=this['elements'][_0x5e017c(0x1f9)][0x0];Object['assign'](this[_0x5e017c(0x20b)],{'offsetLeft':_0x4339b3,'offsetTop':_0x4c07aa});})[_0x21cfb5(0x1d4)](()=>{const _0x3c1a9e=_0x21cfb5;Object[_0x3c1a9e(0x1bb)](this[_0x3c1a9e(0x20b)],{'borderRadiusPercent':gsap[_0x3c1a9e(0x1dd)](this[_0x3c1a9e(0x1da)][_0x3c1a9e(0x1bd)][0x0],_0x3c1a9e(0x205)),'borderRadiusPixels':gsap[_0x3c1a9e(0x1dd)](this['elements'][_0x3c1a9e(0x1bd)][0x0],'borderRadius','px'),'zIndex':gsap[_0x3c1a9e(0x1dd)](this['elements'][_0x3c1a9e(0x1f9)][0x0],_0x3c1a9e(0x1d9))+0x1}),_0x57d237(!![]);});});}[_0x42effd(0x1f5)](){return new Promise(_0x542511=>{const _0x42b6ca=_0x4b21;gsap[_0x42b6ca(0x1b2)](this['maskedEl'],{'clearProps':_0x42b6ca(0x1b4),'onComplete':()=>_0x542511(!![])});});}[_0x42effd(0x1f4)](){return new Promise(_0xd3adbd=>{const _0x5cc047=_0x4b21;Object[_0x5cc047(0x1bb)](this[_0x5cc047(0x1c0)],{'target':this[_0x5cc047(0x1da)]['target'][0x0][_0x5cc047(0x1ff)](),'source':this[_0x5cc047(0x1da)][_0x5cc047(0x1bd)][0x0][_0x5cc047(0x1ff)]()}),_0xd3adbd(!![]);});}[_0x42effd(0x1ee)](){return new Promise(_0x237e3a=>{const _0x3c72d1=_0x4b21;scheduler[_0x3c72d1(0x1f2)](()=>{const _0x479a97=_0x3c72d1;if(this['maskedEl']){const _0x281ea7=this[_0x479a97(0x1cb)]['style'][_0x479a97(0x215)];this['maskedEl']['style'][_0x479a97(0x215)]=_0x479a97(0x1e3),this[_0x479a97(0x1cb)]['offsetWidth'],this['maskedEl']['style']['clipPath']=_0x281ea7;}})[_0x3c72d1(0x1d4)](()=>_0x237e3a(!![]));});}['_isShapeOval'](){const _0x3a9302=_0x42effd;return this[_0x3a9302(0x20b)]['borderRadiusPercent']===0x64;}[_0x42effd(0x1b7)](){const _0x2dfc3b=_0x42effd;let _0x26b2b9={'position':_0x2dfc3b(0x1e0),'top':this['props'][_0x2dfc3b(0x1e5)],'left':this['props'][_0x2dfc3b(0x1f3)],'width':this['rect']['target'][_0x2dfc3b(0x1d2)],'height':this[_0x2dfc3b(0x1c0)][_0x2dfc3b(0x1f9)]['height'],'margin':0x0,'pointerEvents':_0x2dfc3b(0x1e3),'zIndex':this['props'][_0x2dfc3b(0x1d9)]};const _0x139111=this['rect']['source']['width']/0x2,_0x4e28c3=this['rect'][_0x2dfc3b(0x1bd)]['height']/0x2;if(this[_0x2dfc3b(0x1f1)]()){const _0x141218=this[_0x2dfc3b(0x1c0)][_0x2dfc3b(0x1bd)][_0x2dfc3b(0x1f8)]-this[_0x2dfc3b(0x1c0)]['target'][_0x2dfc3b(0x1f8)]+_0x139111,_0x224ada=this[_0x2dfc3b(0x1c0)][_0x2dfc3b(0x1bd)][_0x2dfc3b(0x1bc)]-this[_0x2dfc3b(0x1c0)][_0x2dfc3b(0x1f9)]['top']+_0x4e28c3;_0x26b2b9[_0x2dfc3b(0x215)]=_0x2dfc3b(0x1f0)+_0x139111+_0x2dfc3b(0x1c2)+_0x4e28c3+_0x2dfc3b(0x211)+_0x141218+_0x2dfc3b(0x1c2)+_0x224ada+_0x2dfc3b(0x20a);}else{const _0x1b2906=_0x139111-_0x139111*this['scaleX'],_0xeca334=_0x4e28c3-_0x4e28c3*this['scaleY'],_0xdf0dad=this['rect'][_0x2dfc3b(0x1bd)]['top']-this[_0x2dfc3b(0x1c0)][_0x2dfc3b(0x1f9)][_0x2dfc3b(0x1bc)]+_0xeca334,_0x25663=this['rect'][_0x2dfc3b(0x1f9)][_0x2dfc3b(0x1fc)]-this[_0x2dfc3b(0x1c0)]['source'][_0x2dfc3b(0x1fc)]+_0x1b2906,_0x66cb64=this['rect'][_0x2dfc3b(0x1f9)][_0x2dfc3b(0x1e8)]-this['rect']['source'][_0x2dfc3b(0x1e8)]+_0xeca334,_0xcab98a=this['rect'][_0x2dfc3b(0x1bd)]['left']-this[_0x2dfc3b(0x1c0)][_0x2dfc3b(0x1f9)][_0x2dfc3b(0x1f8)]+_0x1b2906;_0x26b2b9[_0x2dfc3b(0x215)]='inset('+_0xdf0dad+_0x2dfc3b(0x1c2)+_0x25663+_0x2dfc3b(0x1c2)+_0x66cb64+_0x2dfc3b(0x1c2)+_0xcab98a+_0x2dfc3b(0x209)+this[_0x2dfc3b(0x20b)][_0x2dfc3b(0x1d1)]+')';}gsap[_0x2dfc3b(0x1b2)](this[_0x2dfc3b(0x1cb)],_0x26b2b9);}}