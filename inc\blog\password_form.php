<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_filter( 'the_password_form', 'arts_password_form' );
if ( ! function_exists( 'arts_password_form' ) ) {
	/**
	 * Generates a password-protected form for posts.
	 *
	 * @global WP_Post $post The current post object.
	 *
	 * @return string The HTML output of the password form.
	 */
	function arts_password_form() {
		global $post;

		$post  = get_post( $post );
		$label = 'pwbox-' . ( empty( $post->ID ) ? rand() : $post->ID );

		$form_wrapper_attributes = array(
			'class' => array( 'post-password-form-wrapper' ),
		);

		if ( Utilities::is_built_with_elementor() ) {
			$form_wrapper_attributes['class'][] = 'post-password-form-wrapper_elementor';
		}

		$ajax_enabled = apply_filters( 'arts/password_protected_posts/ajax_enabled', true );

		if ( $ajax_enabled ) {
			$form_wrapper_attributes = Utilities::get_component_attributes(
				$form_wrapper_attributes,
				array(
					'name' => 'FormAJAXPassword',
				)
			);
		}

		$output_wrapper_start = '<div ' . Utilities::print_attributes( $form_wrapper_attributes, false ) . '>';
		$output_wrapper_end   = '</div>';

		$string_protected   = esc_html__( 'This content is password protected. To view it please enter your password below:', 'asli' );
		$string_field_label = esc_html__( 'Password', 'asli' );
		$string_incorrect   = esc_html__( 'The password you entered is incorrect', 'asli' );

		$output_form  = '<form action="' . esc_url( site_url( 'wp-login.php?action=postpass', 'login_post' ) ) . '" class="post-password-form" method="post">';
		$output_form .= '<p class="post-password-form-message">';
		$output_form .= '<i class="post-password-form-message-icon material-icons">lock</i>';
		$output_form .= esc_html( $string_protected );
		$output_form .= '</p>';
		$output_form .= '<div class="input-float input-search">';
		$output_form .= '<input class="input-float__input input-search__input" name="post_password" id="' . esc_attr( $label ) . '" type="password" size="20" required="required"/>';
		$output_form .= '<span class="input-float__label">';
		$output_form .= esc_html( $string_field_label );
		$output_form .= '</span>';
		$output_form .= '<input type="hidden" name="post_id" value="' . esc_attr( $post->ID ) . '"/>';
		$output_form .= wp_nonce_field( 'post_password', '_wpnonce', true, false );
		$output_form .= '<button class="input-search__submit" type="submit" name="Submit"><i class="material-icons">keyboard_arrow_right</i></button>';
		$output_form .= '</div>';
		$output_form .= '</form>';

		// No cookie, the user has not sent anything until now.
		if ( ! isset( $_COOKIE[ 'wp-postpass_' . COOKIEHASH ] ) ) {
			return $output_wrapper_start . $output_form . $output_wrapper_end;
		}

		// The refresh came from a different page, the user has not sent anything until now.
		if ( wp_get_raw_referer() !== get_permalink() ) {
			return $output_wrapper_start . $output_form . $output_wrapper_end;
		}

		$error_message_html  = '<div class="post-password-form-error">';
		$error_message_html .= '<i class="post-password-form-message-icon material-icons">error</i>';
		$error_message_html .= '<strong>' . esc_html( $string_incorrect ) . '</strong>';
		$error_message_html .= '</div>';

		// We have a cookie, but it doesn’t match the password. Output form with error message.
		return $output_wrapper_start . $output_form . $error_message_html . $output_wrapper_end;
	}
}
