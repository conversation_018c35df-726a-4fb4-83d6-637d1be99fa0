<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! function_exists( 'arts_add_ajax_comment_class' ) ) {
	/**
	 * Adds custom CSS classes to comments based on their status.
	 *
	 * @param array      $classes Array of comment classes.
	 * @param string     $class Current comment class.
	 * @param int        $comment_id Comment ID.
	 * @param WP_Comment $comment Comment object.
	 * @param int        $post_id Post ID.
	 *
	 * @return array Modified array of comment classes.
	 */
	function arts_add_ajax_comment_class( $classes, $class, $comment_id, $comment, $post_id ) {
		if ( $GLOBALS['comment_id'] === $comment_id ) {
			$classes[] = 'comment_ajax-added';
		}

		if ( $comment->comment_approved === '0' ) {
			$classes[] = 'comment_ajax-unapproved';
		}

		return $classes;
	}
}
