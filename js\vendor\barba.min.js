!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(t||self).barba=n()}(this,function(){function t(t,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function n(n,r,i){return r&&t(n.prototype,r),i&&t(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n}function r(){return r=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},r.apply(this,arguments)}function i(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,o(t,n)}function e(t){return e=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},e(t)}function o(t,n){return o=Object.setPrototypeOf||function(t,n){return t.__proto__=n,t},o(t,n)}function u(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}function f(t,n,r){return f=u()?Reflect.construct:function(t,n,r){var i=[null];i.push.apply(i,n);var e=new(Function.bind.apply(t,i));return r&&o(e,r.prototype),e},f.apply(null,arguments)}function s(t){var n="function"==typeof Map?new Map:void 0;return s=function(t){if(null===t||-1===Function.toString.call(t).indexOf("[native code]"))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(t))return n.get(t);n.set(t,r)}function r(){return f(t,arguments,e(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),o(r,t)},s(t)}function c(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function a(t,n){(null==n||n>t.length)&&(n=t.length);for(var r=0,i=new Array(n);r<n;r++)i[r]=t[r];return i}var h,v=function(){};!function(t){t[t.off=0]="off",t[t.error=1]="error",t[t.warning=2]="warning",t[t.info=3]="info",t[t.debug=4]="debug"}(h||(h={}));var l=h.off,d=/*#__PURE__*/function(){function t(t){this.t=t}t.getLevel=function(){return l},t.setLevel=function(t){return l=h[t]};var n=t.prototype;return n.error=function(){this.i(console.error,h.error,[].slice.call(arguments))},n.warn=function(){this.i(console.warn,h.warning,[].slice.call(arguments))},n.info=function(){this.i(console.info,h.info,[].slice.call(arguments))},n.debug=function(){this.i(console.log,h.debug,[].slice.call(arguments))},n.i=function(n,r,i){r<=t.getLevel()&&n.apply(console,["["+this.t+"] "].concat(i))},t}(),p=j,m=E,w=g,b=x,y=R,P=new RegExp(["(\\\\.)","(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?"].join("|"),"g");function g(t,n){for(var r,i=[],e=0,o=0,u="",f=n&&n.delimiter||"/",s=n&&n.whitelist||void 0,c=!1;null!==(r=P.exec(t));){var a=r[0],h=r[1],v=r.index;if(u+=t.slice(o,v),o=v+a.length,h)u+=h[1],c=!0;else{var l="",d=r[2],p=r[3],m=r[4],w=r[5];if(!c&&u.length){var b=u.length-1,y=u[b];(!s||s.indexOf(y)>-1)&&(l=y,u=u.slice(0,b))}u&&(i.push(u),u="",c=!1);var g=p||m,E=l||f;i.push({name:d||e++,prefix:l,delimiter:E,optional:"?"===w||"*"===w,repeat:"+"===w||"*"===w,pattern:g?T(g):"[^"+k(E===f?E:E+f)+"]+?"})}}return(u||o<t.length)&&i.push(u+t.substr(o)),i}function E(t,n){return function(r,i){var e=t.exec(r);if(!e)return!1;for(var o=e[0],u=e.index,f={},s=i&&i.decode||decodeURIComponent,c=1;c<e.length;c++)if(void 0!==e[c]){var a=n[c-1];f[a.name]=a.repeat?e[c].split(a.delimiter).map(function(t){return s(t,a)}):s(e[c],a)}return{path:o,index:u,params:f}}}function x(t,n){for(var r=new Array(t.length),i=0;i<t.length;i++)"object"==typeof t[i]&&(r[i]=new RegExp("^(?:"+t[i].pattern+")$",O(n)));return function(n,i){for(var e="",o=i&&i.encode||encodeURIComponent,u=!i||!1!==i.validate,f=0;f<t.length;f++){var s=t[f];if("string"!=typeof s){var c,a=n?n[s.name]:void 0;if(Array.isArray(a)){if(!s.repeat)throw new TypeError('Expected "'+s.name+'" to not repeat, but got array');if(0===a.length){if(s.optional)continue;throw new TypeError('Expected "'+s.name+'" to not be empty')}for(var h=0;h<a.length;h++){if(c=o(a[h],s),u&&!r[f].test(c))throw new TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'"');e+=(0===h?s.prefix:s.delimiter)+c}}else if("string"!=typeof a&&"number"!=typeof a&&"boolean"!=typeof a){if(!s.optional)throw new TypeError('Expected "'+s.name+'" to be '+(s.repeat?"an array":"a string"))}else{if(c=o(String(a),s),u&&!r[f].test(c))throw new TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+c+'"');e+=s.prefix+c}}else e+=s}return e}}function k(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function T(t){return t.replace(/([=!:$/()])/g,"\\$1")}function O(t){return t&&t.sensitive?"":"i"}function R(t,n,r){for(var i=(r=r||{}).strict,e=!1!==r.start,o=!1!==r.end,u=r.delimiter||"/",f=[].concat(r.endsWith||[]).map(k).concat("$").join("|"),s=e?"^":"",c=0;c<t.length;c++){var a=t[c];if("string"==typeof a)s+=k(a);else{var h=a.repeat?"(?:"+a.pattern+")(?:"+k(a.delimiter)+"(?:"+a.pattern+"))*":a.pattern;n&&n.push(a),s+=a.optional?a.prefix?"(?:"+k(a.prefix)+"("+h+"))?":"("+h+")?":k(a.prefix)+"("+h+")"}}if(o)i||(s+="(?:"+k(u)+")?"),s+="$"===f?"$":"(?="+f+")";else{var v=t[t.length-1],l="string"==typeof v?v[v.length-1]===u:void 0===v;i||(s+="(?:"+k(u)+"(?="+f+"))?"),l||(s+="(?="+k(u)+"|"+f+")")}return new RegExp(s,O(r))}function j(t,n,r){return t instanceof RegExp?function(t,n){if(!n)return t;var r=t.source.match(/\((?!\?)/g);if(r)for(var i=0;i<r.length;i++)n.push({name:i,prefix:null,delimiter:null,optional:!1,repeat:!1,pattern:null});return t}(t,n):Array.isArray(t)?function(t,n,r){for(var i=[],e=0;e<t.length;e++)i.push(j(t[e],n,r).source);return new RegExp("(?:"+i.join("|")+")",O(r))}(t,n,r):function(t,n,r){return R(g(t,r),n,r)}(t,n,r)}p.match=function(t,n){var r=[];return E(j(t,r,n),r)},p.regexpToFunction=m,p.parse=w,p.compile=function(t,n){return x(g(t,n),n)},p.tokensToFunction=b,p.tokensToRegExp=y;var A={container:"container",history:"history",namespace:"namespace",prefix:"data-barba",prevent:"prevent",wrapper:"wrapper"},M=/*#__PURE__*/function(){function t(){this.o=A,this.u=new DOMParser}var n=t.prototype;return n.toString=function(t){return t.outerHTML},n.toDocument=function(t){return this.u.parseFromString(t,"text/html")},n.toElement=function(t){var n=document.createElement("div");return n.innerHTML=t,n},n.getHtml=function(t){return void 0===t&&(t=document),this.toString(t.documentElement)},n.getWrapper=function(t){return void 0===t&&(t=document),t.querySelector("["+this.o.prefix+'="'+this.o.wrapper+'"]')},n.getContainer=function(t){return void 0===t&&(t=document),t.querySelector("["+this.o.prefix+'="'+this.o.container+'"]')},n.removeContainer=function(t){document.body.contains(t)&&t.parentNode.removeChild(t)},n.addContainer=function(t,n){var r=this.getContainer();r?this.h(t,r):n.appendChild(t)},n.getNamespace=function(t){void 0===t&&(t=document);var n=t.querySelector("["+this.o.prefix+"-"+this.o.namespace+"]");return n?n.getAttribute(this.o.prefix+"-"+this.o.namespace):null},n.getHref=function(t){if(t.tagName&&"a"===t.tagName.toLowerCase()){if("string"==typeof t.href)return t.href;var n=t.getAttribute("href")||t.getAttribute("xlink:href");if(n)return this.resolveUrl(n.baseVal||n)}return null},n.resolveUrl=function(){var t=[].slice.call(arguments).length;if(0===t)throw new Error("resolveUrl requires at least one argument; got none.");var n=document.createElement("base");if(n.href=arguments[0],1===t)return n.href;var r=document.getElementsByTagName("head")[0];r.insertBefore(n,r.firstChild);for(var i,e=document.createElement("a"),o=1;o<t;o++)e.href=arguments[o],n.href=i=e.href;return r.removeChild(n),i},n.h=function(t,n){n.parentNode.insertBefore(t,n.nextSibling)},t}(),L=new M,S=/*#__PURE__*/function(){function t(){this.v=[],this.l=-1}var i=t.prototype;return i.init=function(t,n){this.p="barba";var r={ns:n,scroll:{x:window.scrollX,y:window.scrollY},url:t};this.v.push(r),this.l=0;var i={from:this.p,index:0,states:[].concat(this.v)};window.history&&window.history.replaceState(i,"",t)},i.change=function(t,n,r){if(r&&r.state){var i=r.state,e=i.index;n=this.m(this.l-e),this.replace(i.states),this.l=e}else this.add(t,n);return n},i.add=function(t,n){var r=this.size,i=this.P(n),e={ns:"tmp",scroll:{x:window.scrollX,y:window.scrollY},url:t};this.v.push(e),this.l=r;var o={from:this.p,index:r,states:[].concat(this.v)};switch(i){case"push":window.history&&window.history.pushState(o,"",t);break;case"replace":window.history&&window.history.replaceState(o,"",t)}},i.update=function(t,n){var i=n||this.l,e=r({},this.get(i),t);this.set(i,e)},i.remove=function(t){t?this.v.splice(t,1):this.v.pop(),this.l--},i.clear=function(){this.v=[],this.l=-1},i.replace=function(t){this.v=t},i.get=function(t){return this.v[t]},i.set=function(t,n){return this.v[t]=n},i.P=function(t){var n="push",r=t,i=A.prefix+"-"+A.history;return r.hasAttribute&&r.hasAttribute(i)&&(n=r.getAttribute(i)),n},i.m=function(t){return Math.abs(t)>1?t>0?"forward":"back":0===t?"popstate":t>0?"back":"forward"},n(t,[{key:"current",get:function(){return this.v[this.l]}},{key:"state",get:function(){return this.v[this.v.length-1]}},{key:"previous",get:function(){return this.l<1?null:this.v[this.l-1]}},{key:"size",get:function(){return this.v.length}}]),t}(),$=new S,_=function(t,n){try{var r=function(){if(!n.next.html)return Promise.resolve(t).then(function(t){var r=n.next;if(t){var i=L.toElement(t);r.namespace=L.getNamespace(i),r.container=L.getContainer(i),r.html=t,$.update({ns:r.namespace});var e=L.toDocument(t);document.title=e.title}})}();return Promise.resolve(r&&r.then?r.then(function(){}):void 0)}catch(t){return Promise.reject(t)}},B=p,q={__proto__:null,update:_,nextTick:function(){return new Promise(function(t){window.requestAnimationFrame(t)})},pathToRegexp:B},I=function(){return window.location.origin},U=function(t){return void 0===t&&(t=window.location.href),C(t).port},C=function(t){var n,r=t.match(/:\d+/);if(null===r)/^http/.test(t)&&(n=80),/^https/.test(t)&&(n=443);else{var i=r[0].substring(1);n=parseInt(i,10)}var e,o=t.replace(I(),""),u={},f=o.indexOf("#");f>=0&&(e=o.slice(f+1),o=o.slice(0,f));var s=o.indexOf("?");return s>=0&&(u=F(o.slice(s+1)),o=o.slice(0,s)),{hash:e,path:o,port:n,query:u}},F=function(t){return t.split("&").reduce(function(t,n){var r=n.split("=");return t[r[0]]=r[1],t},{})},H=function(t){return void 0===t&&(t=window.location.href),t.replace(/(\/#.*|\/|#.*)$/,"")},N={__proto__:null,getHref:function(){return window.location.href},getOrigin:I,getPort:U,getPath:function(t){return void 0===t&&(t=window.location.href),C(t).path},parse:C,parseQuery:F,clean:H};function D(t,n,r,i){return void 0===n&&(n=2e3),new Promise(function(e,o){var u=new XMLHttpRequest;if(u.onreadystatechange=function(){if(u.readyState===XMLHttpRequest.DONE)if(200===u.status)e(u.responseText);else if(u.status){var n={status:u.status,statusText:u.statusText};r(t,n),o(n)}},u.ontimeout=function(){var i=new Error("Timeout error ["+n+"]");r(t,i),o(i)},u.onerror=function(){var n=new Error("Fetch error");r(t,n),o(n)},u.open("GET",t),u.timeout=n,u.setRequestHeader("Accept","text/html,application/xhtml+xml,application/xml"),u.setRequestHeader("x-barba","yes"),i){if("function"==typeof i){var f=i();f&&"name"in f&&"value"in f&&u.setRequestHeader(f.name,f.value)}if(Array.isArray(i)&&i.length)for(var s,c=function(t,n){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,n){if(t){if("string"==typeof t)return a(t,n);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(t,n):void 0}}(t))){r&&(t=r);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(i);!(s=c()).done;){var h=s.value;u.setRequestHeader(h.name,h.value)}}u.send()})}var X=function(t){return!!t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof t.then};function z(t,n){return void 0===n&&(n={}),function(){var r=arguments,i=!1,e=new Promise(function(e,o){n.async=function(){return i=!0,function(t,n){t?o(t):e(n)}};var u=t.apply(n,[].slice.call(r));i||(X(u)?u.then(e,o):e(u))});return e}}var G=/*#__PURE__*/function(t){function n(){var n;return(n=t.call(this)||this).logger=new d("@barba/core"),n.all=["ready","page","reset","currentAdded","currentRemoved","nextAdded","nextRemoved","beforeOnce","once","afterOnce","before","beforeLeave","leave","afterLeave","beforeEnter","enter","afterEnter","after"],n.registered=new Map,n.init(),n}i(n,t);var r=n.prototype;return r.init=function(){var t=this;this.registered.clear(),this.all.forEach(function(n){t[n]||(t[n]=function(r,i){t.registered.has(n)||t.registered.set(n,new Set),t.registered.get(n).add({ctx:i||{},fn:r})})})},r.do=function(t){var n=arguments,r=this;if(this.registered.has(t)){var i=Promise.resolve();return this.registered.get(t).forEach(function(t){i=i.then(function(){return z(t.fn,t.ctx).apply(void 0,[].slice.call(n,1))})}),i.catch(function(n){r.logger.debug("Hook error ["+t+"]"),r.logger.error(n)})}return Promise.resolve()},r.clear=function(){var t=this;this.all.forEach(function(n){delete t[n]}),this.init()},r.help=function(){this.logger.info("Available hooks: "+this.all.join(","));var t=[];this.registered.forEach(function(n,r){return t.push(r)}),this.logger.info("Registered hooks: "+t.join(","))},n}(v),Q=new G,W=/*#__PURE__*/function(){function t(t){if(this.g=[],"boolean"==typeof t)this.k=t;else{var n=Array.isArray(t)?t:[t];this.g=n.map(function(t){return B(t)})}}return t.prototype.checkHref=function(t){if("boolean"==typeof this.k)return this.k;var n=C(t).path;return this.g.some(function(t){return null!==t.exec(n)})},t}(),J=/*#__PURE__*/function(t){function n(n){var r;return(r=t.call(this,n)||this).T=new Map,r}i(n,t);var e=n.prototype;return e.set=function(t,n,r){return this.T.set(t,{action:r,request:n}),{action:r,request:n}},e.get=function(t){return this.T.get(t)},e.getRequest=function(t){return this.T.get(t).request},e.getAction=function(t){return this.T.get(t).action},e.has=function(t){return!this.checkHref(t)&&this.T.has(t)},e.delete=function(t){return this.T.delete(t)},e.update=function(t,n){var i=r({},this.T.get(t),n);return this.T.set(t,i),i},n}(W),K=function(){return!window.history.pushState},V=function(t){return!t.el||!t.href},Y=function(t){var n=t.event;return n.which>1||n.metaKey||n.ctrlKey||n.shiftKey||n.altKey},Z=function(t){var n=t.el;return n.hasAttribute("target")&&"_blank"===n.target},tt=function(t){var n=t.el;return void 0!==n.protocol&&window.location.protocol!==n.protocol||void 0!==n.hostname&&window.location.hostname!==n.hostname},nt=function(t){var n=t.el;return void 0!==n.port&&U()!==U(n.href)},rt=function(t){var n=t.el;return n.getAttribute&&"string"==typeof n.getAttribute("download")},it=function(t){return t.el.hasAttribute(A.prefix+"-"+A.prevent)},et=function(t){return Boolean(t.el.closest("["+A.prefix+"-"+A.prevent+'="all"]'))},ot=function(t){var n=t.href;return H(n)===H()&&U(n)===U()},ut=/*#__PURE__*/function(t){function n(n){var r;return(r=t.call(this,n)||this).suite=[],r.tests=new Map,r.init(),r}i(n,t);var r=n.prototype;return r.init=function(){this.add("pushState",K),this.add("exists",V),this.add("newTab",Y),this.add("blank",Z),this.add("corsDomain",tt),this.add("corsPort",nt),this.add("download",rt),this.add("preventSelf",it),this.add("preventAll",et),this.add("sameUrl",ot,!1)},r.add=function(t,n,r){void 0===r&&(r=!0),this.tests.set(t,n),r&&this.suite.push(t)},r.run=function(t,n,r,i){return this.tests.get(t)({el:n,event:r,href:i})},r.checkLink=function(t,n,r){var i=this;return this.suite.some(function(e){return i.run(e,t,n,r)})},n}(W),ft=/*#__PURE__*/function(t){function n(r,i){var e;return void 0===i&&(i="Barba error"),(e=t.call.apply(t,[this].concat([].slice.call(arguments,2)))||this).error=r,e.label=i,Error.captureStackTrace&&Error.captureStackTrace(c(e),n),e.name="BarbaError",e}return i(n,t),n}(/*#__PURE__*/s(Error)),st=/*#__PURE__*/function(){function t(t){void 0===t&&(t=[]),this.logger=new d("@barba/core"),this.all=[],this.page=[],this.once=[],this.O=[{name:"namespace",type:"strings"},{name:"custom",type:"function"}],t&&(this.all=this.all.concat(t)),this.update()}var n=t.prototype;return n.add=function(t,n){"rule"===t?this.O.splice(n.position||0,0,n.value):this.all.push(n),this.update()},n.resolve=function(t,n){var r=this;void 0===n&&(n={});var i=n.once?this.once:this.page;i=i.filter(n.self?function(t){return t.name&&"self"===t.name}:function(t){return!t.name||"self"!==t.name});var e=new Map,o=i.find(function(i){var o=!0,u={};return!(!n.self||"self"!==i.name)||(r.O.reverse().forEach(function(n){o&&(o=r.R(i,n,t,u),i.from&&i.to&&(o=r.R(i,n,t,u,"from")&&r.R(i,n,t,u,"to")),i.from&&!i.to&&(o=r.R(i,n,t,u,"from")),!i.from&&i.to&&(o=r.R(i,n,t,u,"to")))}),e.set(i,u),o)}),u=e.get(o),f=[];if(f.push(n.once?"once":"page"),n.self&&f.push("self"),u){var s,c=[o];Object.keys(u).length>0&&c.push(u),(s=this.logger).info.apply(s,["Transition found ["+f.join(",")+"]"].concat(c))}else this.logger.info("No transition found ["+f.join(",")+"]");return o},n.update=function(){var t=this;this.all=this.all.map(function(n){return t.j(n)}).sort(function(t,n){return t.priority-n.priority}).reverse().map(function(t){return delete t.priority,t}),this.page=this.all.filter(function(t){return void 0!==t.leave||void 0!==t.enter}),this.once=this.all.filter(function(t){return void 0!==t.once})},n.R=function(t,n,r,i,e){var o=!0,u=!1,f=t,s=n.name,c=s,a=s,h=s,v=e?f[e]:f,l="to"===e?r.next:r.current;if(e?v&&v[s]:v[s]){switch(n.type){case"strings":default:var d=Array.isArray(v[c])?v[c]:[v[c]];l[c]&&-1!==d.indexOf(l[c])&&(u=!0),-1===d.indexOf(l[c])&&(o=!1);break;case"object":var p=Array.isArray(v[a])?v[a]:[v[a]];l[a]?(l[a].name&&-1!==p.indexOf(l[a].name)&&(u=!0),-1===p.indexOf(l[a].name)&&(o=!1)):o=!1;break;case"function":v[h](r)?u=!0:o=!1}u&&(e?(i[e]=i[e]||{},i[e][s]=f[e][s]):i[s]=f[s])}return o},n.A=function(t,n,r){var i=0;return(t[n]||t.from&&t.from[n]||t.to&&t.to[n])&&(i+=Math.pow(10,r),t.from&&t.from[n]&&(i+=1),t.to&&t.to[n]&&(i+=2)),i},n.j=function(t){var n=this;t.priority=0;var r=0;return this.O.forEach(function(i,e){r+=n.A(t,i.name,e+1)}),t.priority=r,t},t}();function ct(t,n){try{var r=t()}catch(t){return n(t)}return r&&r.then?r.then(void 0,n):r}var at=/*#__PURE__*/function(){function t(t){void 0===t&&(t=[]),this.logger=new d("@barba/core"),this.M=!1,this.store=new st(t)}var r=t.prototype;return r.get=function(t,n){return this.store.resolve(t,n)},r.doOnce=function(t){var n=t.data,r=t.transition;try{var i=function(){e.M=!1},e=this,o=r||{};e.M=!0;var u=ct(function(){return Promise.resolve(e.L("beforeOnce",n,o)).then(function(){return Promise.resolve(e.once(n,o)).then(function(){return Promise.resolve(e.L("afterOnce",n,o)).then(function(){})})})},function(t){e.M=!1,e.logger.debug("Transition error [before/after/once]"),e.logger.error(t)});return Promise.resolve(u&&u.then?u.then(i):i())}catch(t){return Promise.reject(t)}},r.doPage=function(t){var n=t.data,r=t.transition,i=t.page,e=t.wrapper;try{var o=function(t){u.M=!1},u=this,f=r||{},s=!0===f.sync||!1;u.M=!0;var c=ct(function(){function t(){return Promise.resolve(u.L("before",n,f)).then(function(){function t(t){return Promise.resolve(u.remove(n)).then(function(){return Promise.resolve(u.L("after",n,f)).then(function(){})})}var r=function(){if(s)return ct(function(){return Promise.resolve(u.add(n,e)).then(function(){return Promise.resolve(u.L("beforeLeave",n,f)).then(function(){return Promise.resolve(u.L("beforeEnter",n,f)).then(function(){return Promise.resolve(Promise.all([u.leave(n,f),u.enter(n,f)])).then(function(){return Promise.resolve(u.L("afterLeave",n,f)).then(function(){return Promise.resolve(u.L("afterEnter",n,f)).then(function(){})})})})})})},function(t){if(u.S(t))throw new ft(t,"Transition error [sync]")});var t=function(t){return ct(function(){var t=function(){if(!1!==r)return Promise.resolve(u.add(n,e)).then(function(){return Promise.resolve(u.L("beforeEnter",n,f)).then(function(){return Promise.resolve(u.enter(n,f,r)).then(function(){return Promise.resolve(u.L("afterEnter",n,f)).then(function(){})})})})}();if(t&&t.then)return t.then(function(){})},function(t){if(u.S(t))throw new ft(t,"Transition error [before/after/enter]")})},r=!1,o=ct(function(){return Promise.resolve(u.L("beforeLeave",n,f)).then(function(){return Promise.resolve(Promise.all([u.leave(n,f),_(i,n)]).then(function(t){return t[0]})).then(function(t){return r=t,Promise.resolve(u.L("afterLeave",n,f)).then(function(){})})})},function(t){if(u.S(t))throw new ft(t,"Transition error [before/after/leave]")});return o&&o.then?o.then(t):t()}();return r&&r.then?r.then(t):t()})}var r=function(){if(s)return Promise.resolve(_(i,n)).then(function(){})}();return r&&r.then?r.then(t):t()},function(t){if(u.M=!1,t.name&&"BarbaError"===t.name)throw u.logger.debug(t.label),u.logger.error(t.error),t;throw u.logger.debug("Transition error [page]"),u.logger.error(t),t});return Promise.resolve(c&&c.then?c.then(o):o())}catch(t){return Promise.reject(t)}},r.once=function(t,n){try{return Promise.resolve(Q.do("once",t,n)).then(function(){return n.once?z(n.once,n)(t):Promise.resolve()})}catch(t){return Promise.reject(t)}},r.leave=function(t,n){try{return Promise.resolve(Q.do("leave",t,n)).then(function(){return n.leave?z(n.leave,n)(t):Promise.resolve()})}catch(t){return Promise.reject(t)}},r.enter=function(t,n,r){try{return Promise.resolve(Q.do("enter",t,n)).then(function(){return n.enter?z(n.enter,n)(t,r):Promise.resolve()})}catch(t){return Promise.reject(t)}},r.add=function(t,n){try{return L.addContainer(t.next.container,n),Q.do("nextAdded",t),Promise.resolve()}catch(t){return Promise.reject(t)}},r.remove=function(t){try{return L.removeContainer(t.current.container),Q.do("currentRemoved",t),Promise.resolve()}catch(t){return Promise.reject(t)}},r.S=function(t){return t.message?!/Timeout error|Fetch error/.test(t.message):!t.status},r.L=function(t,n,r){try{return Promise.resolve(Q.do(t,n,r)).then(function(){return r[t]?z(r[t],r)(n):Promise.resolve()})}catch(t){return Promise.reject(t)}},n(t,[{key:"isRunning",get:function(){return this.M},set:function(t){this.M=t}},{key:"hasOnce",get:function(){return this.store.once.length>0}},{key:"hasSelf",get:function(){return this.store.all.some(function(t){return"self"===t.name})}},{key:"shouldWait",get:function(){return this.store.all.some(function(t){return t.to&&!t.to.route||t.sync})}}]),t}(),ht=/*#__PURE__*/function(){function t(t){var n=this;this.names=["beforeLeave","afterLeave","beforeEnter","afterEnter"],this.byNamespace=new Map,0!==t.length&&(t.forEach(function(t){n.byNamespace.set(t.namespace,t)}),this.names.forEach(function(t){Q[t](n.$(t))}))}return t.prototype.$=function(t){var n=this;return function(r){var i=t.match(/enter/i)?r.next:r.current,e=n.byNamespace.get(i.namespace);return e&&e[t]?z(e[t],e)(r):Promise.resolve()}},t}();Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(t){var n=this;do{if(n.matches(t))return n;n=n.parentElement||n.parentNode}while(null!==n&&1===n.nodeType);return null});var vt={container:null,html:"",namespace:"",url:{hash:"",href:"",path:"",port:null,query:{}}},lt=/*#__PURE__*/function(){function t(){this.version="2.9.7",this.schemaPage=vt,this.Logger=d,this.logger=new d("@barba/core"),this.plugins=[],this.hooks=Q,this.dom=L,this.helpers=q,this.history=$,this.request=D,this.url=N}var i=t.prototype;return i.use=function(t,n){var r=this.plugins;r.indexOf(t)>-1?this.logger.warn("Plugin ["+t.name+"] already installed."):"function"==typeof t.install?(t.install(this,n),r.push(t)):this.logger.warn("Plugin ["+t.name+'] has no "install" method.')},i.init=function(t){var n=void 0===t?{}:t,i=n.transitions,e=void 0===i?[]:i,o=n.views,u=void 0===o?[]:o,f=n.schema,s=void 0===f?A:f,c=n.requestError,a=n.timeout,h=void 0===a?2e3:a,v=n.cacheIgnore,l=void 0!==v&&v,p=n.prefetchIgnore,m=void 0!==p&&p,w=n.preventRunning,b=void 0!==w&&w,y=n.prevent,P=void 0===y?null:y,g=n.debug,E=n.logLevel,x=n.customHeaders,k=void 0===x?[]:x;if(d.setLevel(!0===(void 0!==g&&g)?"debug":void 0===E?"off":E),this.logger.info(this.version),Object.keys(s).forEach(function(t){A[t]&&(A[t]=s[t])}),this._=c,this.timeout=h,this.cacheIgnore=l,this.prefetchIgnore=m,this.preventRunning=b,this.customHeaders=k,this.B=this.dom.getWrapper(),!this.B)throw new Error("[@barba/core] No Barba wrapper found");this.B.setAttribute("aria-live","polite"),this.q();var T=this.data.current;if(!T.container)throw new Error("[@barba/core] No Barba container found");if(this.cache=new J(l),this.prevent=new ut(m),this.transitions=new at(e),this.views=new ht(u),null!==P){if("function"!=typeof P)throw new Error("[@barba/core] Prevent should be a function");this.prevent.add("preventCustom",P)}this.history.init(T.url.href,T.namespace),this.I=this.I.bind(this),this.U=this.U.bind(this),this.C=this.C.bind(this),this.F(),this.plugins.forEach(function(t){return t.init()});var O=this.data;O.trigger="barba",O.next=O.current,O.current=r({},this.schemaPage),this.hooks.do("ready",O),this.once(O),this.q()},i.destroy=function(){this.q(),this.H(),this.history.clear(),this.hooks.clear(),this.plugins=[]},i.force=function(t){window.location.assign(t)},i.go=function(t,n,r){var i;if(void 0===n&&(n="barba"),this.transitions.isRunning)this.force(t);else if(!(i="popstate"===n?this.history.current&&this.url.getPath(this.history.current.url)===this.url.getPath(t):this.prevent.run("sameUrl",null,null,t))||this.transitions.hasSelf)return n=this.history.change(t,n,r),r&&(r.stopPropagation(),r.preventDefault()),this.page(t,n,i)},i.once=function(t){try{var n=this;return Promise.resolve(n.hooks.do("beforeEnter",t)).then(function(){function r(){return Promise.resolve(n.hooks.do("afterEnter",t)).then(function(){})}var i=function(){if(n.transitions.hasOnce){var r=n.transitions.get(t,{once:!0});return Promise.resolve(n.transitions.doOnce({transition:r,data:t})).then(function(){})}}();return i&&i.then?i.then(r):r()})}catch(t){return Promise.reject(t)}},i.page=function(t,n,i){try{var e=function(){var t=o.data;return Promise.resolve(o.hooks.do("page",t)).then(function(){var n=function(n,r){try{var e=(f=o.transitions.get(t,{once:!1,self:i}),Promise.resolve(o.transitions.doPage({data:t,page:u,transition:f,wrapper:o.B})).then(function(){o.q()}))}catch(t){return r()}var f;return e&&e.then?e.then(void 0,r):e}(0,function(){0===d.getLevel()&&o.force(t.current.url.href)});if(n&&n.then)return n.then(function(){})})},o=this;o.data.next.url=r({href:t},o.url.parse(t)),o.data.trigger=n;var u=o.cache.has(t)?o.cache.update(t,{action:"click"}).request:o.cache.set(t,o.request(t,o.timeout,o.onRequestError.bind(o,n),o.customHeaders),"click").request,f=function(){if(o.transitions.shouldWait)return Promise.resolve(_(u,o.data)).then(function(){})}();return Promise.resolve(f&&f.then?f.then(e):e())}catch(t){return Promise.reject(t)}},i.onRequestError=function(t){this.transitions.isRunning=!1;var n=[].slice.call(arguments,1),r=n[0],i=n[1],e=this.cache.getAction(r);return this.cache.delete(r),this._&&!1===this._(t,e,r,i)||"click"===e&&this.force(r),!1},i.prefetch=function(t){var n=this;this.cache.has(t)||this.cache.set(t,this.request(t,this.timeout,this.onRequestError.bind(this,"barba"),this.customHeaders).catch(function(t){n.logger.error(t)}),"prefetch")},i.F=function(){!0!==this.prefetchIgnore&&(document.addEventListener("mouseover",this.I),document.addEventListener("touchstart",this.I)),document.addEventListener("click",this.U),window.addEventListener("popstate",this.C)},i.H=function(){!0!==this.prefetchIgnore&&(document.removeEventListener("mouseover",this.I),document.removeEventListener("touchstart",this.I)),document.removeEventListener("click",this.U),window.removeEventListener("popstate",this.C)},i.I=function(t){var n=this,r=this.N(t);if(r){var i=this.dom.getHref(r);this.prevent.checkHref(i)||this.cache.has(i)||this.cache.set(i,this.request(i,this.timeout,this.onRequestError.bind(this,r),this.customHeaders).catch(function(t){n.logger.error(t)}),"enter")}},i.U=function(t){var n=this.N(t);if(n)return this.transitions.isRunning&&this.preventRunning?(t.preventDefault(),void t.stopPropagation()):void this.go(this.dom.getHref(n),n,t)},i.C=function(t){this.go(this.url.getHref(),"popstate",t)},i.N=function(t){for(var n=t.target;n&&!this.dom.getHref(n);)n=n.parentNode;if(n&&!this.prevent.checkLink(n,t,this.dom.getHref(n)))return n},i.q=function(){var t=this.url.getHref(),n={container:this.dom.getContainer(),html:this.dom.getHtml(),namespace:this.dom.getNamespace(),url:r({href:t},this.url.parse(t))};this.D={current:n,next:r({},this.schemaPage),trigger:void 0},this.hooks.do("reset",this.data)},n(t,[{key:"data",get:function(){return this.D}},{key:"wrapper",get:function(){return this.B}}]),t}();return new lt});
