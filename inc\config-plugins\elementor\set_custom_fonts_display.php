<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'elementor_pro/custom_fonts/font_display', 'arts_set_custom_fonts_display', 10, 3 );
if ( ! function_exists( 'arts_set_custom_fonts_display' ) ) {
	/**
	 * Sets the custom fonts display property to 'swap'.
	 *
	 * @param string $current_value The current value of the font display property.
	 * @param string $font_family The font family name.
	 * @param array  $data Additional data.
	 *
	 * @return string 'swap' to set the font display property.
	 */
	function arts_set_custom_fonts_display( $current_value, $font_family, $data ) {
		return 'swap';
	}
}
