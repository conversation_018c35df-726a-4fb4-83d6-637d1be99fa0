<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'wp_ajax_post_password', 'arts_ajax_post_password' );
add_action( 'wp_ajax_nopriv_post_password', 'arts_ajax_post_password' );
if ( ! function_exists( 'arts_ajax_post_password' ) ) {
	/**
	 * Handles AJAX request for post password authentication.
	 *
	 * This function checks the provided password for a specific post,
	 * sets a cookie if the password is correct, and redirects to the post.
	 * If the password is incorrect, it returns an error message.
	 *
	 * @return void
	 */
	function arts_ajax_post_password() {
		check_ajax_referer( 'post_password' );

		// Validate input parameters
		$post_id  = isset( $_POST['post_id'] ) ? absint( $_POST['post_id'] ) : 0;
		$password = isset( $_POST['post_password'] ) ? sanitize_text_field( $_POST['post_password'] ) : '';

		if ( ! $post_id || ! $password ) {
			wp_die(
				esc_html__( 'Invalid input parameters.', 'asli' ),
				esc_html__( 'Bad Request', 'asli' ),
				array( 'response' => 400 )
			);
		}

		// Include and setup password hasher
		require_once ABSPATH . WPINC . '/class-phpass.php';
		$hasher = new PasswordHash( 8, true );
		$hash   = $hasher->HashPassword( $password );

		// Set cookie for 10 days
		$cookie_path = COOKIEPATH ? COOKIEPATH : '/';
		setcookie(
			'wp-postpass_' . COOKIEHASH,
			$hash,
			time() + 864000,
			$cookie_path,
			COOKIE_DOMAIN,
			true,  // Secure
			true   // HTTPOnly
		);

		// Make cookie available for immediate use
		$_COOKIE[ 'wp-postpass_' . COOKIEHASH ] = $hash;

		// Check if post exists
		$post = get_post( $post_id );
		if ( ! $post ) {
			wp_die(
				esc_html__( 'It looks like nothing found here. Try to navigate the menu or return to the home page.', 'asli' ),
				esc_html__( 'Error 404', 'asli' ),
				array( 'response' => 404 )
			);
		}

		// Setup the post data for password check
		setup_postdata( $post );
		$is_authorized = ! post_password_required( $post );
		wp_reset_postdata();

		// If authorized, redirect to the post
		if ( $is_authorized ) {
			wp_safe_redirect( get_permalink( $post_id ) );
			exit;
		}

		// If not authorized, show error message
		wp_die(
			esc_html__( 'The password you entered is incorrect. Please try again.', 'asli' ),
			esc_html__( 'Unauthorized', 'asli' ),
			array( 'response' => 401 )
		);
	}
}
