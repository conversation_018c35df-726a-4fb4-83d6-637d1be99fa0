<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'woocommerce_after_shop_loop_item', 'arts_after_shop_loop_item_wrapper_buttons_start', 5 );
if ( ! function_exists( 'arts_after_shop_loop_item_wrapper_buttons_start' ) ) {
	function arts_after_shop_loop_item_wrapper_buttons_start() {
		?>
		<div class="product__wrapper-buttons">
		<?php
	}
}

add_action( 'woocommerce_after_shop_loop_item', 'arts_after_shop_loop_item_wrapper_buttons_end', 15 );
if ( ! function_exists( 'arts_after_shop_loop_item_wrapper_buttons_end' ) ) {
	function arts_after_shop_loop_item_wrapper_buttons_end() {
		?>
		</div>
		<?php
	}
}

add_action( 'woocommerce_after_shop_loop_item_title', 'arts_after_shop_loop_item_title_wrapper_rating_start', 4 );
if ( ! function_exists( 'arts_after_shop_loop_item_title_wrapper_rating_start' ) ) {
	function arts_after_shop_loop_item_title_wrapper_rating_start() {
		global $product;

		if ( ! wc_review_ratings_enabled() || $product->get_average_rating() <= 0 ) {
			return;
		}
		?>
		<div class="product__wrapper-rating">
		<?php
	}
}

add_action( 'woocommerce_after_shop_loop_item_title', 'arts_after_shop_loop_item_title_wrapper_rating_end', 6 );
if ( ! function_exists( 'arts_after_shop_loop_item_title_wrapper_rating_end' ) ) {
	function arts_after_shop_loop_item_title_wrapper_rating_end() {
		global $product;

		if ( ! wc_review_ratings_enabled() || $product->get_average_rating() <= 0 ) {
			return;
		}
		?>
		</div>
		<?php
	}
}
