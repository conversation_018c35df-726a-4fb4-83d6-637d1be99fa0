<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'elementor/theme/register_locations', 'arts_elementor_register_locations' );
if ( ! function_exists( 'arts_elementor_register_locations' ) ) {
	/**
	 * Register custom locations for Elementor Theme Builder.
	 *
	 * @param \ElementorPro\Modules\ThemeBuilder\Classes\Locations_Manager $elementor_theme_manager Elementor theme manager instance.
	 * @return void
	 */
	function arts_elementor_register_locations( $elementor_theme_manager ) {
		$elementor_theme_manager->register_location( 'header' );
		$elementor_theme_manager->register_location( 'footer' );
		$elementor_theme_manager->register_location( 'popup' );
		$elementor_theme_manager->register_location( 'single-post' );
		$elementor_theme_manager->register_location( 'single-page' );
	}
}
