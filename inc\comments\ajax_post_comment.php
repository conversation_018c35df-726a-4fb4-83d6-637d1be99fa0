<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'wp_ajax_post_comment', 'arts_ajax_post_comment' );
add_action( 'wp_ajax_nopriv_post_comment', 'arts_ajax_post_comment' );
if ( ! function_exists( 'arts_ajax_post_comment' ) ) {
	/**
	 * Handles AJAX request to post a comment.
	 *
	 * This function checks for nonce security, validates the comment data,
	 * handles comment submission, sets comment cookies, and updates the comments template.
	 *
	 * @return void
	 */
	function arts_ajax_post_comment() {
		check_ajax_referer( 'post_comment' );

		/**
		 * Filters whether an empty comment should be allowed.
		 *
		 * @since 5.1.0
		 *
		 * @param bool  $allow_empty_comment Whether to allow empty comments. Default false.
		 * @param array $commentdata         Array of comment data to be sent to wp_insert_comment().
		 */
		$allow_empty_comment = apply_filters( 'allow_empty_comment', false );

		// Check if the comment is empty
		if ( ! $_POST['comment'] && ! $allow_empty_comment ) {
			wp_die( esc_html__( 'Please type your comment text.', 'asli' ), '', array( 'response' => 409 ) );
		}

		// Post comment
		$comment = wp_handle_comment_submission( wp_unslash( $_POST ) );

		if ( is_wp_error( $comment ) ) {
			$data = (int) $comment->get_error_data();

			if ( ! empty( $data ) ) {
				wp_die( $comment->get_error_message(), '', array( 'response' => $data ) );
			} else {
				exit;
			}
		}

		$user            = wp_get_current_user();
		$cookies_consent = ( isset( $_POST['wp-comment-cookies-consent'] ) );

		do_action( 'set_comment_cookies', $comment, $user, $cookies_consent );

		$GLOBALS['comment']    = $comment;
		$GLOBALS['comment_id'] = get_comment_ID();

		// If user didn't consent to cookies, add specific query arguments to display the awaiting moderation message.
		if ( ! $cookies_consent && 'unapproved' === wp_get_comment_status( $comment ) && ! empty( $comment->comment_author_email ) ) {
			$_GET['unapproved']      = $comment->comment_ID;
			$_GET['moderation-hash'] = wp_hash( $comment->comment_date_gmt );
		}

		// Markup an added comment with class
		add_filter( 'comment_class', 'arts_add_ajax_comment_class', 10, 5 );

		$args = array(
			'p'              => $_POST['comment_post_ID'],
			'posts_per_page' => -1,
		);

		$loop = new WP_Query( $args );

		while ( $loop->have_posts() ) {
			$loop->the_post();

			global $withcomments;
			$withcomments = true;

			// Echo updated comments template
			comments_template();
		}

		wp_reset_postdata();

		wp_die();
	}
}
