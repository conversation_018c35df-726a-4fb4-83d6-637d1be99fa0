<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'customize_register', 'arts_customize_register' );
if ( ! function_exists( 'arts_customize_register' ) ) {
	/**
	 * Registers a custom setting and control for a secondary logo in the WordPress Customizer.
	 *
	 * @param WP_Customize_Manager $wp_customize The Customizer object.
	 */
	function arts_customize_register( $wp_customize ) {
		$wp_customize->add_setting(
			'custom_logo_secondary',
			array(
				'default'           => '',
				'theme_mod'         => 'image',
				'capability'        => 'edit_theme_options',
				'sanitize_callback' => 'esc_attr',
			)
		);

		$wp_customize->add_control(
			new WP_Customize_Media_Control(
				$wp_customize,
				'custom_logo_secondary',
				array(
					'label'     => esc_html__( 'Secondary Logo', 'asli' ),
					'settings'  => 'custom_logo_secondary',
					'priority'  => 8,
					'mime_type' => 'image',
					'section'   => 'title_tagline',
				)
			)
		);
	}
}
