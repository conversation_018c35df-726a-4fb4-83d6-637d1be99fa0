/*!
 * Arts Notice Manager v1.0.0
 * Copyright © 2025
 * Author: <PERSON><PERSON>
 * License: MIT
 * Website: https://artemsemkin.com
 * Repository: https://github.com/artkrsk/arts-notice-manager
 * Generated on: 2025-04-25
 */
(function(root, factory) {if (typeof define === 'function' && define.amd) {define([], factory);} else if (typeof module === 'object' && module.exports) {module.exports = factory();} else {root.ArtsNoticeManager = factory();}}(typeof self !== 'undefined' ? self : this, function() {"use strict";var ArtsNoticeManager=(()=>{var n=Object.defineProperty;var l=Object.getOwnPropertyDescriptor;var c=Object.getOwnPropertyNames;var h=Object.prototype.hasOwnProperty;var u=(s,i,t)=>i in s?n(s,i,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[i]=t;var d=(s,i)=>{for(var t in i)n(s,t,{get:i[t],enumerable:!0})},p=(s,i,t,e)=>{if(i&&typeof i=="object"||typeof i=="function")for(let o of c(i))!h.call(s,o)&&o!==t&&n(s,o,{get:()=>i[o],enumerable:!(e=l(i,o))||e.enumerable});return s};var m=s=>p(n({},"__esModule",{value:!0}),s);var r=(s,i,t)=>u(s,typeof i!="symbol"?i+"":i,t);var v={};d(v,{default:()=>b});var a=class{constructor(i,t){r(this,"options");r(this,"boundClickHandler",null);r(this,"processingUrls",new Set);r(this,"isInitialized",!1);r(this,"fetchImplementation");this.options={dismissButtonSelector:".notice-dismiss",noticeSelector:".notice.is-dismissible",dismissUrlAttribute:"data-dismiss-url",...i},this.fetchImplementation=t!=null&&t.fetch?t.fetch.bind(window):fetch.bind(window),(t==null?void 0:t.autoInit)!==!1&&this.init()}init(){this.isInitialized&&this.cleanup(),this.boundClickHandler=this.handleDismissClick.bind(this),document.addEventListener("click",this.boundClickHandler),this.isInitialized=!0}cleanup(){this.boundClickHandler&&(document.removeEventListener("click",this.boundClickHandler),this.boundClickHandler=null),this.processingUrls.clear(),this.isInitialized=!1}handleDismissClick(i){let t=i.target;if(!t||!this.elementMatches(t,this.options.dismissButtonSelector))return;let e=t.closest(this.options.noticeSelector);if(!e)return;let o=e.getAttribute(this.options.dismissUrlAttribute);o&&this.processDismissalWithDeduplication(o,e)}processDismissalWithDeduplication(i,t){this.processingUrls.has(i)||(this.processingUrls.add(i),this.dismissNotice(i,t).catch(()=>{}))}elementMatches(i,t){try{return i.matches(t)}catch(e){return i.classList.contains(t.replace(".",""))||i.id===t.replace("#","")||Array.from(document.querySelectorAll(t)).includes(i)}}async dismissNotice(i,t){try{let e=await this.fetchImplementation(i);if(!e.ok)throw new Error(`Failed to dismiss notice: ${e.status}`);await e.text(),t.parentNode&&t.parentNode.removeChild(t)}catch(e){console.error("Notice dismissal failed:",e)}finally{this.processingUrls.delete(i)}}isProcessingUrl(i){return this.processingUrls.has(i)}};var f=new a,b=f;return m(v);})();
return ArtsNoticeManager;}));