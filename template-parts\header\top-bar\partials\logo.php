<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$title           = get_bloginfo( 'name' );
$has_custom_logo = has_custom_logo();

$logo_attributes = array(
	'class' => array(
		'header__col',
		'header__col_logo',
		'header__col_left',
		'header__col_fluid-paddings',
		'logo',
	),
	'href'  => home_url( '/' ),
);

$logo_primary_args = array(
	'class'         => 'logo__img-primary',
	'alt'           => $title,
	'fetchpriority' => 'low',
);

$logo_secondary_args = array(
	'class'         => 'logo__img-secondary',
	'alt'           => $title,
	'fetchpriority' => 'low',
);

$logo_wrapper_attributes = array(
	'class' => array( 'logo__wrapper-img' ),
);

if ( $has_custom_logo && ! empty( $title ) ) {
	$logo_attributes['aria-label'] = $title;
}

?>

<a <?php Utilities::print_attributes( $logo_attributes ); ?>>
	<?php if ( $has_custom_logo ) : ?>
		<div class="logo__wrapper-img">
			<?php echo wp_get_attachment_image( get_theme_mod( 'custom_logo' ), 'full', '', $logo_primary_args ); // primary logo ?>
		</div>
	<?php endif; ?>
	<?php get_template_part( 'template-parts/header/top-bar/partials/logo-text' ); ?>
	<div class="header__border-vertical bg-br"></div>
</a>
