<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'woocommerce_quantity_input_classes', 'arts_custom_woocommerce_quantity_input_classes', 10, 2 );
if ( ! function_exists( 'arts_custom_woocommerce_quantity_input_classes' ) ) {
	function arts_custom_woocommerce_quantity_input_classes( $classes, $product ) {
		if ( is_array( $classes ) ) {
			$classes[] = 'product-quantity__input';
			$classes[] = 'js-product-quantity__input';
		}

		return $classes;
	}
}
