<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_marquee_options' ) ) {
	/**
	 * Get the global options for the marquee animation.
	 *
	 * @return array $options
	 */
	function arts_get_marquee_options() {
		$marquee_default_delimiter              = Utilities::get_kit_settings( 'marquee_default_delimiter', '&nbsp;&nbsp;/&nbsp;&nbsp;' );
		$marquee_default_speed                  = Utilities::get_kit_settings( 'marquee_default_speed', 0.5 );
		$marquee_default_hover_speed            = Utilities::get_kit_settings( 'marquee_default_hover_speed', 0.1 );
		$marquee_default_scroll_effects_enabled = Utilities::get_kit_settings( 'marquee_default_scroll_effects_enabled', false );

		$options = array(
			'loop'                     => true,
			'speed'                    => floatval( $marquee_default_speed ),
			'onHoverSpeed'             => floatVal( $marquee_default_hover_speed ),
			'onScrollSpeed'            => false,
			'onScrollInverseDirection' => false,
			'delimiter'                => esc_js( $marquee_default_delimiter ),
		);

		if ( $marquee_default_scroll_effects_enabled ) {
			$marquee_default_scroll_speed                     = Utilities::get_kit_settings( 'marquee_default_scroll_speed', 1.0 );
			$marquee_default_scroll_inverse_direction_enabled = Utilities::get_kit_settings( 'marquee_default_scroll_inverse_direction_enabled', false );

			$options['onScrollSpeed']            = floatval( $marquee_default_scroll_speed );
			$options['onScrollInverseDirection'] = boolval( $marquee_default_scroll_inverse_direction_enabled );
		}

		return $options;
	}
}
