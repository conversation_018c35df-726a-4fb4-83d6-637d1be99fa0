<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'arts/optimizer/preloads/prefetch_map', 'arts_add_prefetch_links' );
if ( ! function_exists( 'arts_add_prefetch_links' ) ) {
	/**
	 * Add additional prefetch links for improved performance.
	 *
	 * @param array $map An array of URLs to prefetch.
	 * @return array Modified array of URLs to prefetch.
	 */
	function arts_add_prefetch_links( $map ) {
		$map['AsliApp']       = esc_url( ARTS_THEME_URL . '/js/app.js' );
		$map['AsliFramework'] = esc_url( ARTS_THEME_URL . '/js/framework.js' );

		return $map;
	}
}

add_filter( 'arts/optimizer/preloads/assets_map', 'arts_add_preload_links' );
if ( ! function_exists( 'arts_add_preload_links' ) ) {
	/**
	 * Add additional preload links for improved performance.
	 *
	 * @param array $map An array of URLs to preload.
	 * @return array Modified array of URLs to preload.
	 */
	function arts_add_preload_links( $map ) {
		$map['Scroll'] = esc_url( ARTS_THEME_URL . '/js/components/Scroll.js' );

		return $map;
	}
}
