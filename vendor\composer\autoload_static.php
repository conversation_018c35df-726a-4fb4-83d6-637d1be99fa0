<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit16fb33c8f0de29529ece5d82e03b79a1
{
    public static $files = array (
        'bb31413de2f15b3a33244901ff2c882c' => __DIR__ . '/..' . '/arts/tgm-plugin-activation/src/class-tgm-plugin-activation.php',
    );

    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'ProteusThemes\\WPContentImporter2\\' => 33,
        ),
        'M' => 
        array (
            'Monolog\\' => 8,
        ),
        'A' => 
        array (
            'Asli\\Theme\\' => 11,
            'Arts\\WizardSetup\\' => 17,
            'Arts\\Utilities\\' => 15,
            'Arts\\NoticeManager\\' => 19,
            'Arts\\Merlin\\' => 12,
            'Arts\\LicenseManager\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/Psr/Log',
        ),
        'ProteusThemes\\WPContentImporter2\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/wp-content-importer-v2/src',
        ),
        'Monolog\\' => 
        array (
            0 => __DIR__ . '/..' . '/monolog/monolog/src/Monolog',
        ),
        'Asli\\Theme\\' => 
        array (
            0 => '/',
        ),
        'Arts\\WizardSetup\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/wizard-setup/src',
        ),
        'Arts\\Utilities\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/utilities/src/php',
        ),
        'Arts\\NoticeManager\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/notice-manager/src/php',
        ),
        'Arts\\Merlin\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/merlin-wp/src',
        ),
        'Arts\\LicenseManager\\' => 
        array (
            0 => __DIR__ . '/..' . '/arts/license-manager/src/php',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit16fb33c8f0de29529ece5d82e03b79a1::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit16fb33c8f0de29529ece5d82e03b79a1::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit16fb33c8f0de29529ece5d82e03b79a1::$classMap;

        }, null, ClassLoader::class);
    }
}
