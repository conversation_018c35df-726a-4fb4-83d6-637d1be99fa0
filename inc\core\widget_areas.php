<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'widgets_init', 'arts_register_widget_areas' );
if ( ! function_exists( 'arts_register_widget_areas' ) ) {
	/**
	 * Register Widget Areas
	 *
	 * @return void
	 */
	function arts_register_widget_areas() {
		$args = array(
			'name'          => esc_html__( 'Blog Sidebar', 'asli' ),
			'id'            => 'blog-sidebar',
			'description'   => esc_html__( 'Appears in Blog', 'asli' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget__title widget__title_underline">',
			'after_title'   => '</h2>',
		);
		register_sidebar( $args );

		if ( class_exists( 'WooCommerce' ) ) {
			$args = array(
				'name'          => esc_html__( 'Shop Sidebar', 'asli' ),
				'id'            => 'shop-sidebar',
				'description'   => esc_html__( 'Appears in Shop', 'asli' ),
				'before_widget' => '<section id="%1$s" class="widget %2$s">',
				'after_widget'  => '</section>',
				'before_title'  => '<h2 class="widget__title widget__title_underline">',
				'after_title'   => '</h2>',
			);
			register_sidebar( $args );
		}
	}
}
