<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! function_exists( 'arts_get_posts_pagination' ) ) {
	/**
	 * Creates a custom pagination markup for posts.
	 *
	 * This function removes the default h2 heading from pagination,
	 * hides the default previous/next links, and adds custom ones.
	 * It also adds a container for the pagination links.
	 *
	 * @param array  $args  Optional. Arguments for pagination. Default empty array.
	 * @param string $class Optional. CSS class for the pagination container. Default 'pagination'.
	 * @param bool   $echo  Optional. Whether to echo the pagination. Default true.
	 *
	 * @return void|string Void if $echo is true, otherwise the pagination HTML.
	 */
	function arts_get_posts_pagination( $args = array(), $class = 'pagination', $echo = true ) {
		$defaults = array(
			'prev_next'          => false, // hide default prev/next
			'prev_text'          => is_rtl() ? 'keyboard_arrow_right' : 'keyboard_arrow_left',
			'next_text'          => is_rtl() ? 'keyboard_arrow_left' : 'keyboard_arrow_right',
			'screen_reader_text' => esc_html__( 'Posts navigation', 'asli' ),
		);

		$args = wp_parse_args( $args, $defaults );

		$class .= ' typography-pagination';

		$links     = paginate_links( $args );
		$prev_link = get_previous_posts_link( $args['prev_text'] );
		$next_link = get_next_posts_link( $args['next_text'] );

		$template = apply_filters(
			'arts_navigation_markup_template',
			'<nav class="navigation %1$s">
				<div class="screen-reader-text d-none">%2$s</div>
				<div class="nav-links">
					%3$s<div class="nav-links__container">%4$s</div>%5$s
				</div>
			</nav>',
			$args,
			$class
		);

		if ( $echo ) {
			echo sprintf( $template, $class, $args['screen_reader_text'], $prev_link, $links, $next_link );
		} else {
			return sprintf( $template, $class, $args['screen_reader_text'], $prev_link, $links, $next_link );
		}
	}
}
