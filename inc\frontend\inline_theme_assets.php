<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'wp_enqueue_scripts', 'arts_inline_theme_assets', 60 );
if ( ! function_exists( 'arts_inline_theme_assets' ) ) {
	/**
	 * Localize the theme assets
	 *
	 * @return void
	 */
	function arts_inline_theme_assets() {
		wp_localize_script(
			'asli-app',
			'asli_theme_assets',
			array(
				'arts-header'            => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/arts-header.min.js' ),
						'id'   => 'arts-header-js',
					),
				),
				'arts-fullpage-slider'   => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/arts-fullpage-slider.min.js' ),
						'id'   => 'arts-fullpage-slider-js',
					),
				),
				'arts-infinite-list'     => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/arts-infinite-list/arts-infinite-list.min.js' ),
						'id'   => 'arts-infinite-list-js',
					),
				),
				'arts-horizontal-scroll' => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/arts-horizontal-scroll.min.js' ),
						'id'   => 'arts-horizontal-scroll-js',
					),
				),
				'arts-parallax'          => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/arts-parallax.min.js' ),
						'id'   => 'arts-parallax-js',
					),
				),
				'arts-cursor-follower'   => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/arts-cursor-follower.min.js' ),
						'id'   => 'arts-cursor-follower-js',
					),
				),
				'circle-type'            => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/circletype.min.js' ),
						'id'   => 'circle-type-js',
					),
				),
				'photoswipe'             => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/photoswipe.umd.min.js' ),
						'id'   => 'photoswipe-js',
					),
					array(
						'type' => 'style',
						'src'  => esc_url( ARTS_THEME_URL . '/css/photoswipe.min.css' ),
						'id'   => 'photoswipe-css',
					),
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/photoswipe-lightbox.umd.min.js' ),
						'id'   => 'photoswipe-lightbox-js',
					),
				),
				'lenis'                  => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/lenis.min.js' ),
						'id'   => 'lenis-js',
					),
				),
				'barba'                  => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/barba.min.js' ),
						'id'   => 'barba-js',
					),
				),
				'curtains'               => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/curtains.umd.custom.min.js' ),
						'id'   => 'curtains-js',
					),
				),
				'pristine'               => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/pristine.min.js' ),
						'id'   => 'pristine-js',
					),
				),
				'bootstrap-modal'        => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/bootstrap-modal.min.js' ),
						'id'   => 'bootstrap-modal-js',
					),
				),
				'isotope'                => array(
					array(
						'type' => 'script',
						'src'  => esc_url( ARTS_THEME_URL . '/js/vendor/isotope.pkgd.min.js' ),
						'id'   => 'isotope-js',
					),
				),
			),
		);
	}
}
