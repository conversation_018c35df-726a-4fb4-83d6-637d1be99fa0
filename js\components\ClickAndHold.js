const _0x2f5344=_0x38fe;function _0x38fe(_0x44ac56,_0x5301c8){const _0xc1d680=_0xc1d6();return _0x38fe=function(_0x38fe2f,_0x49b75d){_0x38fe2f=_0x38fe2f-0xba;let _0x269ee5=_0xc1d680[_0x38fe2f];return _0x269ee5;},_0x38fe(_0x44ac56,_0x5301c8);}(function(_0x2b8e19,_0x1e01df){const _0x1b49d5=_0x38fe,_0x25b668=_0x2b8e19();while(!![]){try{const _0x553b95=-parseInt(_0x1b49d5(0xcd))/0x1+parseInt(_0x1b49d5(0xe9))/0x2*(parseInt(_0x1b49d5(0xf7))/0x3)+-parseInt(_0x1b49d5(0xdb))/0x4+parseInt(_0x1b49d5(0xd3))/0x5+-parseInt(_0x1b49d5(0xf9))/0x6+-parseInt(_0x1b49d5(0xba))/0x7*(parseInt(_0x1b49d5(0xc9))/0x8)+parseInt(_0x1b49d5(0xf0))/0x9;if(_0x553b95===_0x1e01df)break;else _0x25b668['push'](_0x25b668['shift']());}catch(_0x2bb752){_0x25b668['push'](_0x25b668['shift']());}}}(_0xc1d6,0xe9bf2));function _0xc1d6(){const _0x207cce=['_onPress','timeline','bind','_attachEvents','elements','mouseup','_setHasClickAndHold','_onComplete','press','duration','_onStart','length','8cHUFgz','_onUpdate','addEventListener','finally','1185896HGKhDJ','preventDefault','kill','links','webkitmouseforcechanged','removeEventListener','4422070erQWLT','dispatchEvent','a[href]:not(a[href=\x22#\x22])','_handlers','string','button','clear','element','5492920bCRDoA','power1.in','touchend','release','_onRelease','activeClass','options','hoverEffect','touchstart','webkitmouseforcewillbegin','closest','start','forEach','function','68kAGAKH','has-click-and-hold','_detachEvents','postTask','destroy','complete','webkitmouseforceup','40941828ZOeVOm','pressed','progress','event','webkitmouseforcedown','click','update','116859zVaCWN','mouseleave','10107186UBUraP','target','toggle','init','ease','10897775jDPhUj','mousedown','push'];_0xc1d6=function(){return _0x207cce;};return _0xc1d6();}export default class ClickAndHold extends BaseComponent{constructor({name:_0x3ee772,loadInnerComponents:_0x4eb644,loadAfterSyncStyles:_0xb01a58,parent:_0x5b74f5,element:_0x1a7121}){const _0xa73fb1=_0x38fe;super({'name':_0x3ee772,'loadInnerComponents':_0x4eb644,'loadAfterSyncStyles':_0xb01a58,'parent':_0x5b74f5,'element':_0x1a7121,'defaults':{'activeClass':_0xa73fb1(0xea),'ease':_0xa73fb1(0xdc),'duration':3.6},'innerElements':{'links':_0xa73fb1(0xd5)}}),this['_handlers']={'press':this[_0xa73fb1(0xbd)][_0xa73fb1(0xbf)](this),'release':this['_onRelease']['bind'](this),'start':this[_0xa73fb1(0xc7)][_0xa73fb1(0xbf)](this),'update':this[_0xa73fb1(0xca)][_0xa73fb1(0xbf)](this),'complete':this[_0xa73fb1(0xc4)][_0xa73fb1(0xbf)](this)},this['dataReady'][_0xa73fb1(0xcc)](()=>{const _0x47d695=_0xa73fb1;this[_0x47d695(0xf1)]=![],this[_0x47d695(0xf3)]={},this['tl']={},this['setup']();});}[_0x2f5344(0xfc)](){return new Promise(_0xaa3138=>{const _0x27fe09=_0x38fe;typeof this[_0x27fe09(0xe1)][_0x27fe09(0xc6)]==='number'?(this['tl']=gsap[_0x27fe09(0xbe)]({'defaults':{'duration':this[_0x27fe09(0xe1)][_0x27fe09(0xc6)],'ease':typeof this[_0x27fe09(0xe1)]['ease']===_0x27fe09(0xd7)?this[_0x27fe09(0xe1)][_0x27fe09(0xfd)]:undefined}}),this[_0x27fe09(0xc3)](!![]),this[_0x27fe09(0xc0)](),_0xaa3138(!![])):_0xaa3138(!![]);});}[_0x2f5344(0xed)](){return new Promise(_0x4f32db=>{const _0x6bc3e8=_0x38fe,_0x176346=[];this[_0x6bc3e8(0xeb)]();if(this['tl']&&typeof this['tl'][_0x6bc3e8(0xcf)]===_0x6bc3e8(0xe8)){const _0x4120bc=scheduler['postTask'](()=>{this['tl']['kill']();});_0x176346[_0x6bc3e8(0xbc)](_0x4120bc);}Promise['all'](_0x176346)['finally'](()=>_0x4f32db(!![]));});}[_0x2f5344(0xc3)](_0x22a888=!![]){const _0x1f3935=_0x2f5344;this[_0x1f3935(0xda)]['classList'][_0x1f3935(0xfb)](''+this[_0x1f3935(0xe1)][_0x1f3935(0xe0)],_0x22a888);}[_0x2f5344(0xc0)](){const _0x4573e7=_0x2f5344;this[_0x4573e7(0xc1)][_0x4573e7(0xd0)][_0x4573e7(0xc8)]&&this[_0x4573e7(0xc1)][_0x4573e7(0xd0)][_0x4573e7(0xe7)](_0x295e4e=>{const _0x1836b8=_0x4573e7;scheduler[_0x1836b8(0xec)](()=>{const _0xae5a2e=_0x1836b8;_0x295e4e[_0xae5a2e(0xcb)]('webkitmouseforcewillbegin',app['hoverEffect']['preventDefault']),_0x295e4e[_0xae5a2e(0xcb)](_0xae5a2e(0xf4),app[_0xae5a2e(0xe2)]['preventDefault']),_0x295e4e[_0xae5a2e(0xcb)](_0xae5a2e(0xef),app['hoverEffect'][_0xae5a2e(0xce)]),_0x295e4e[_0xae5a2e(0xcb)](_0xae5a2e(0xd1),app[_0xae5a2e(0xe2)][_0xae5a2e(0xce)]),_0x295e4e[_0xae5a2e(0xcb)](_0xae5a2e(0xf5),app[_0xae5a2e(0xe2)][_0xae5a2e(0xce)]),_0x295e4e[_0xae5a2e(0xcb)](_0xae5a2e(0xbb),this[_0xae5a2e(0xd6)][_0xae5a2e(0xc5)]),_0x295e4e[_0xae5a2e(0xcb)](_0xae5a2e(0xe3),this['_handlers']['press']),_0x295e4e[_0xae5a2e(0xcb)](_0xae5a2e(0xc2),this['_handlers'][_0xae5a2e(0xde)]),_0x295e4e['addEventListener'](_0xae5a2e(0xf8),this[_0xae5a2e(0xd6)]['release']),_0x295e4e[_0xae5a2e(0xcb)](_0xae5a2e(0xdd),this[_0xae5a2e(0xd6)][_0xae5a2e(0xde)]);});});}[_0x2f5344(0xeb)](){const _0x402cad=_0x2f5344;this['elements'][_0x402cad(0xd0)][_0x402cad(0xc8)]&&this[_0x402cad(0xc1)]['links'][_0x402cad(0xe7)](_0x562622=>{const _0xf135f1=_0x402cad;scheduler[_0xf135f1(0xec)](()=>{const _0x4b9f8f=_0xf135f1;_0x562622['removeEventListener'](_0x4b9f8f(0xe4),app[_0x4b9f8f(0xe2)]['preventDefault']),_0x562622[_0x4b9f8f(0xd2)](_0x4b9f8f(0xf4),app[_0x4b9f8f(0xe2)]['preventDefault']),_0x562622[_0x4b9f8f(0xd2)]('webkitmouseforceup',app[_0x4b9f8f(0xe2)][_0x4b9f8f(0xce)]),_0x562622[_0x4b9f8f(0xd2)](_0x4b9f8f(0xd1),app['hoverEffect'][_0x4b9f8f(0xce)]),_0x562622[_0x4b9f8f(0xd2)](_0x4b9f8f(0xf5),app[_0x4b9f8f(0xe2)][_0x4b9f8f(0xce)]),_0x562622[_0x4b9f8f(0xd2)]('mousedown',this['_handlers'][_0x4b9f8f(0xc5)]),_0x562622['removeEventListener'](_0x4b9f8f(0xe3),this[_0x4b9f8f(0xd6)]['press']),_0x562622[_0x4b9f8f(0xd2)](_0x4b9f8f(0xc2),this['_handlers'][_0x4b9f8f(0xde)]),_0x562622[_0x4b9f8f(0xd2)](_0x4b9f8f(0xf8),this[_0x4b9f8f(0xd6)][_0x4b9f8f(0xde)]),_0x562622[_0x4b9f8f(0xd2)](_0x4b9f8f(0xdd),this['_handlers'][_0x4b9f8f(0xde)]);});});}[_0x2f5344(0xbd)](_0x5ee70a){const _0x1f6dc6=_0x2f5344;_0x5ee70a[_0x1f6dc6(0xce)]();if(_0x1f6dc6(0xd8)in _0x5ee70a&&_0x5ee70a['button']!==0x0)return;this['event']=_0x5ee70a,this[_0x1f6dc6(0xf1)]=!![],this['tl']['clear']()['to']({},{'onStart':this[_0x1f6dc6(0xd6)][_0x1f6dc6(0xe6)],'onUpdate':this['_handlers'][_0x1f6dc6(0xf6)],'onComplete':this[_0x1f6dc6(0xd6)][_0x1f6dc6(0xee)]},'<');}[_0x2f5344(0xdf)](){const _0x22c03a=_0x2f5344;if(this['pressed']){const _0x305620=new CustomEvent(_0x22c03a(0xde),{'detail':{'component':this}});this['tl'][_0x22c03a(0xd9)](),this[_0x22c03a(0xda)][_0x22c03a(0xd4)](_0x305620),this[_0x22c03a(0xf1)]=![];}}[_0x2f5344(0xc7)](){const _0x22e956=_0x2f5344,_0x507f92=new CustomEvent(_0x22e956(0xc5),{'detail':{'component':this,'progress':this['tl'][_0x22e956(0xf2)]()}});this['element']['dispatchEvent'](_0x507f92);}[_0x2f5344(0xca)](){const _0x50640a=_0x2f5344,_0x300035=new CustomEvent(_0x50640a(0xf2),{'detail':{'component':this,'progress':this['tl'][_0x50640a(0xf2)]()}});this['element'][_0x50640a(0xd4)](_0x300035);}[_0x2f5344(0xc4)](){const _0x184ee3=_0x2f5344;if(!!app[_0x184ee3(0xe1)]['isElementorEditor'])return;const _0x1b4139=this[_0x184ee3(0xf3)]['target'][_0x184ee3(0xe5)]('.'+this['options'][_0x184ee3(0xe0)]),_0x1fc8c0=this['event'][_0x184ee3(0xfa)][_0x184ee3(0xe5)]('a'),_0x2f045d=new CustomEvent(_0x184ee3(0xee));this['element'][_0x184ee3(0xd4)](_0x2f045d),_0x1b4139&&this['_setHasClickAndHold'](![]),_0x1fc8c0[_0x184ee3(0xd2)](_0x184ee3(0xe4),app[_0x184ee3(0xe2)][_0x184ee3(0xce)]),_0x1fc8c0[_0x184ee3(0xd2)]('webkitmouseforcedown',app['hoverEffect'][_0x184ee3(0xce)]),_0x1fc8c0[_0x184ee3(0xd2)](_0x184ee3(0xef),app[_0x184ee3(0xe2)][_0x184ee3(0xce)]),_0x1fc8c0['removeEventListener'](_0x184ee3(0xd1),app[_0x184ee3(0xe2)]['preventDefault']),_0x1fc8c0[_0x184ee3(0xd2)]('click',app[_0x184ee3(0xe2)][_0x184ee3(0xce)]),_0x1fc8c0[_0x184ee3(0xd2)](_0x184ee3(0xbb),this['_handlers']['press']),_0x1fc8c0['removeEventListener'](_0x184ee3(0xe3),this['_handlers']['press']),_0x1fc8c0['removeEventListener'](_0x184ee3(0xc2),this[_0x184ee3(0xd6)][_0x184ee3(0xde)]),_0x1fc8c0[_0x184ee3(0xd2)](_0x184ee3(0xf8),this['_handlers'][_0x184ee3(0xde)]),_0x1fc8c0[_0x184ee3(0xd2)]('touchend',this[_0x184ee3(0xd6)][_0x184ee3(0xde)]),_0x1fc8c0[_0x184ee3(0xf5)]();}}