<?php
/**
 * Edit account form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/form-edit-account.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.7.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * Hook - woocommerce_before_edit_account_form.
 *
 * @since 2.6.0
 */
do_action( 'woocommerce_before_edit_account_form' );
?>


<form class="woocommerce-EditAccountForm edit-account" action="" method="post" <?php do_action( 'woocommerce_edit_account_form_tag' ); ?> >

	<?php do_action( 'woocommerce_edit_account_form_start' ); ?>

	<p class="woocommerce-form-row woocommerce-form-row--first form-row form-row-first">
		<label class="input-float" for="account_first_name">
			<input type="text" class="woocommerce-Input woocommerce-Input--text input-text input-float__input" name="account_first_name" id="account_first_name" autocomplete="given-name" value="<?php echo esc_attr( $user->first_name ); ?>" aria-required="true" />
			<span class="input-float__label"><?php esc_html_e( 'First name', 'asli' ); ?>&nbsp;<span class="required" aria-hidden="true">*</span></span>
		</label>
	</p>
	<p class="woocommerce-form-row woocommerce-form-row--last form-row form-row-last">
		<label class="input-float" for="account_last_name">
			<input type="text" class="woocommerce-Input woocommerce-Input--text input-text input-float__input" name="account_last_name" id="account_last_name" autocomplete="family-name" value="<?php echo esc_attr( $user->last_name ); ?>" aria-required="true" />
			<span class="input-float__label"><?php esc_html_e( 'Last name', 'asli' ); ?>&nbsp;<span class="required" aria-hidden="true">*</span></span>
		</label>
	</p>
	<div class="clear"></div>

	<p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
		<label class="input-float" for="account_display_name">
			<input type="text" class="woocommerce-Input woocommerce-Input--text input-text input-float__input" name="account_display_name" id="account_display_name" aria-describedby="account_display_name_description" value="<?php echo esc_attr( $user->display_name ); ?>" aria-required="true" />
			<span class="input-float__label"><?php esc_html_e( 'Display name', 'asli' ); ?>&nbsp;<span class="required" aria-hidden="true">*</span></span>
		</label>
		<small id="account_display_name_description" class="lh-sm"><em><?php esc_html_e( 'This will be how your name will be displayed in the account section and in reviews', 'asli' ); ?></em></small>
	</p>
	<div class="clear"></div>

	<p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
		<label class="input-float" for="account_email">
			<input type="email" class="woocommerce-Input woocommerce-Input--email input-text input-float__input" name="account_email" id="account_email" autocomplete="email" value="<?php echo esc_attr( $user->user_email ); ?>" aria-required="true" />
			<span class="input-float__label"><?php esc_html_e( 'Email address', 'asli' ); ?>&nbsp;<span class="required" aria-hidden="true">*</span></span>
		</label>
	</p>

	<?php
		/**
		 * Hook where additional fields should be rendered.
		 *
		 * @since 8.7.0
		 */
		do_action( 'woocommerce_edit_account_form_fields' );
	?>

	<fieldset>
		<legend><?php esc_html_e( 'Password change', 'asli' ); ?></legend>

		<p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
			<label class="input-float" for="password_current">
				<input type="password" class="woocommerce-Input woocommerce-Input--password input-text input-float__input" name="password_current" id="password_current" autocomplete="off" />
				<span class="input-float__label"><?php esc_html_e( 'Current password (leave blank to leave unchanged)', 'asli' ); ?></span>
			</label>
		</p>
		<p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
			<label class="input-float" for="password_1">
				<input type="password" class="woocommerce-Input woocommerce-Input--password input-text input-float__input" name="password_1" id="password_1" autocomplete="off" />
				<span class="input-float__label"><?php esc_html_e( 'New password (leave blank to leave unchanged)', 'asli' ); ?></span>
			</label>
		</p>
		<p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
			<label class="input-float" for="password_2">
				<input type="password" class="woocommerce-Input woocommerce-Input--password input-text input-float__input" name="password_2" id="password_2" autocomplete="off" />
				<span class="input-float__label"><?php esc_html_e( 'Confirm new password', 'asli' ); ?></span>
			</label>
		</p>
	</fieldset>
	<div class="clear"></div>

	<?php do_action( 'woocommerce_edit_account_form' ); ?>

	<p>
		<?php wp_nonce_field( 'save_account_details', 'save-account-details-nonce' ); ?>
		<button type="submit" class="woocommerce-Button button<?php echo esc_attr( wc_wp_theme_get_element_class_name( 'button' ) ? ' ' . wc_wp_theme_get_element_class_name( 'button' ) : '' ); ?>" name="save_account_details" value="<?php esc_attr_e( 'Save changes', 'asli' ); ?>"><?php esc_html_e( 'Save changes', 'asli' ); ?></button>
		<input type="hidden" name="action" value="save_account_details" />
	</p>

	<?php do_action( 'woocommerce_edit_account_form_end' ); ?>
</form>

<?php do_action( 'woocommerce_after_edit_account_form' ); ?>
