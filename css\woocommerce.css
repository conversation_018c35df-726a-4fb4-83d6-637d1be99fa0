/*!==========================================================================
 * ==========================================================================
 * ==========================================================================
 *
 * Asli – AJAX Portfolio Elementor Theme
 * WooCommerce Integration Stylesheet
 *
 * [Table of Contents]
 *
 * 1. Breadcrumb
 * 2. Button Add To Cart
 * 3. Cart External
 * 4. Cart Grouped
 * 5. Cart Simple
 * 6. Cart Variable
 * 7. Checkout
 * 8. Filter
 * 9. Woo Commerce Form
 * 10. Mini Cart
 * 11. My Account
 * 12. My Account Addresses
 * 13. Sitewide Notice
 * 14. Ordering
 * 15. Order Details
 * 16. Product Cart
 * 17. Product Details
 * 18. Product Gallery
 * 19. Product Quantity
 * 20. Products Loop
 * 21. Products Related
 * 22. Reviews
 * 23. Select
 * 24. Result Count
 * 25. Shop Table
 * 26. Stock
 * 27. Tabs
 *
 * ==========================================================================
 * ==========================================================================
 * ==========================================================================
 */

/*!========================================================================
 * 1. Breadcrumb
 * ======================================================================!*/
.ui-element, .woocommerce .woocommerce-result-count, .woocommerce .woocommerce-ordering .orderby, .woocommerce .woocommerce-breadcrumb {
  font-family: var(--ui-element-font-family);
  font-weight: var(--ui-element-font-weight);
  line-height: var(--ui-element-line-height);
  letter-spacing: var(--ui-element-letter-spacing);
  font-size: calc(var(--ui-element-min-font-size) * 1px);
  color: var(--ui-element-color);
}
@media screen and (min-width: 360px) {
  .ui-element, .woocommerce .woocommerce-result-count, .woocommerce .woocommerce-ordering .orderby, .woocommerce .woocommerce-breadcrumb {
    font-size: calc(var(--ui-element-min-font-size) * 1px + (var(--ui-element-max-font-size) - var(--ui-element-min-font-size)) * (100vw - 360px) / 1560);
  }
}
@media screen and (min-width: 1920px) {
  .ui-element, .woocommerce .woocommerce-result-count, .woocommerce .woocommerce-ordering .orderby, .woocommerce .woocommerce-breadcrumb {
    font-size: calc(var(--ui-element-max-font-size) * 1px);
  }
}

.h4, body.woocommerce div.product .woocommerce-tabs .panel h2, body.woocommerce div.product.elementor .woocommerce-tabs .panel h2, .woocommerce .woocommerce-order-details__title, .woocommerce .woocommerce-order-downloads__title, .woocommerce-MyAccount-content h3 {
  font-family: var(--h4-font-family);
  font-weight: var(--h4-font-weight);
  line-height: var(--h4-line-height);
  letter-spacing: var(--h4-letter-spacing);
  font-size: calc(var(--h4-min-font-size) * 1px);
  color: var(--h4-color);
}
@media screen and (min-width: 360px) {
  .h4, body.woocommerce div.product .woocommerce-tabs .panel h2, body.woocommerce div.product.elementor .woocommerce-tabs .panel h2, .woocommerce .woocommerce-order-details__title, .woocommerce .woocommerce-order-downloads__title, .woocommerce-MyAccount-content h3 {
    font-size: calc(var(--h4-min-font-size) * 1px + (var(--h4-max-font-size) - var(--h4-min-font-size)) * (100vw - 360px) / 1560);
  }
}
@media screen and (min-width: 1920px) {
  .h4, body.woocommerce div.product .woocommerce-tabs .panel h2, body.woocommerce div.product.elementor .woocommerce-tabs .panel h2, .woocommerce .woocommerce-order-details__title, .woocommerce .woocommerce-order-downloads__title, .woocommerce-MyAccount-content h3 {
    font-size: calc(var(--h4-max-font-size) * 1px);
  }
}

.h5, .woocommerce .woocommerce-column__title, .woocommerce-account .addresses .title h3 {
  font-family: var(--h5-font-family);
  font-weight: var(--h5-font-weight);
  line-height: var(--h5-line-height);
  letter-spacing: var(--h5-letter-spacing);
  font-size: calc(var(--h5-min-font-size) * 1px);
  color: var(--h5-color);
}
@media screen and (min-width: 360px) {
  .h5, .woocommerce .woocommerce-column__title, .woocommerce-account .addresses .title h3 {
    font-size: calc(var(--h5-min-font-size) * 1px + (var(--h5-max-font-size) - var(--h5-min-font-size)) * (100vw - 360px) / 1560);
  }
}
@media screen and (min-width: 1920px) {
  .h5, .woocommerce .woocommerce-column__title, .woocommerce-account .addresses .title h3 {
    font-size: calc(var(--h5-max-font-size) * 1px);
  }
}

.h6, .woocommerce div.product .woocommerce-tabs ul.tabs li, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li {
  font-family: var(--h6-font-family);
  font-weight: var(--h6-font-weight);
  line-height: var(--h6-line-height);
  letter-spacing: var(--h6-letter-spacing);
  font-size: calc(var(--h6-min-font-size) * 1px);
  color: var(--h6-color);
}
@media screen and (min-width: 360px) {
  .h6, .woocommerce div.product .woocommerce-tabs ul.tabs li, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li {
    font-size: calc(var(--h6-min-font-size) * 1px + (var(--h6-max-font-size) - var(--h6-min-font-size)) * (100vw - 360px) / 1560);
  }
}
@media screen and (min-width: 1920px) {
  .h6, .woocommerce div.product .woocommerce-tabs ul.tabs li, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li {
    font-size: calc(var(--h6-max-font-size) * 1px);
  }
}

.woocommerce .woocommerce-breadcrumb {
  margin-bottom: 0;
}
.woocommerce .woocommerce-breadcrumb__divider {
  opacity: 0.5;
}
.woocommerce .marquee-header__woocommerce-breadcrumb {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 50;
  background-color: var(--color-dark-1);
  color: #fff;
  padding: 0.5em 1em;
  margin: -1px 0 0;
  display: inline-flex;
  line-height: 1;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
}
.woocommerce .marquee-header__woocommerce-breadcrumb a {
  color: currentColor;
  opacity: 0.7;
  transition: all 0.3s ease;
}
.woocommerce .marquee-header__woocommerce-breadcrumb a:hover {
  color: var(--color-accent-dark-theme);
  opacity: 1;
}

/*!========================================================================
 * 2. Button Add To Cart
 * ======================================================================!*/
.woocommerce ul.products li.product:not(.outofstock) .button.product_type_simple::before {
  content: "\e8cb";
  display: inline-block;
  font-family: "Material Icons";
  margin-right: 0.5em;
  vertical-align: bottom;
  font-size: 1rem;
}
.woocommerce ul.products li.product:not(.outofstock) .button.product_type_simple.added::before {
  content: "\e017";
  font-family: "WooCommerce";
}
.woocommerce ul.products li.product:not(.outofstock) .button.product_type_simple.loading {
  pointer-events: none;
  opacity: 0.5;
}
.woocommerce ul.products li.product:not(.outofstock) .button.product_type_simple.loading::before {
  content: "\e01c";
  font-family: "WooCommerce";
  animation: rotate 2s linear infinite;
}

/*!========================================================================
 * 3. Cart External
 * ======================================================================!*/
.woocommerce .cart_external .single_add_to_cart_button {
  color: #fff;
  background-color: #181818;
  border-radius: 512px;
  border: none;
  margin: 0;
  padding-left: 2rem;
  padding-right: 2rem;
}
.woocommerce .cart_external .single_add_to_cart_button:hover, .woocommerce .cart_external .single_add_to_cart_button:focus {
  color: #fff;
  background-color: var(--color-accent);
}
.woocommerce .cart_external .single_add_to_cart_button::before {
  content: "\f1e1";
  display: inline-block;
  font-family: "Material Icons";
  margin-right: 0.5em;
  vertical-align: bottom;
  font-size: 1rem;
  animation: none;
}

/*!========================================================================
 * 4. Cart Grouped
 * ======================================================================!*/
.woocommerce .cart_grouped {
  width: 100%;
}
.woocommerce .cart_grouped .added_to_cart {
  padding: 16px 36px;
}
.woocommerce .cart_grouped .single_add_to_cart_button {
  margin-top: 0.5em;
  min-width: 33.3333%;
  background-color: #181818;
  color: #fff;
  border-color: #181818;
}
.woocommerce .cart_grouped .single_add_to_cart_button:hover, .woocommerce .cart_grouped .single_add_to_cart_button:focus {
  background-color: var(--color-accent);
  border-color: var(--color-accent);
  color: #fff;
}
.woocommerce .cart_grouped .single_add_to_cart_button::before {
  content: "\e8cb";
  display: inline-block;
  font-family: "Material Icons";
  margin-right: 0.5em;
  vertical-align: bottom;
  font-size: 1rem;
  animation: none;
}
.woocommerce .cart_grouped .product-quantity {
  display: inline-flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  border: 1px solid var(--color-border-opacity);
  color: #181818;
  border-radius: 512px;
  transition: border-color 0.3s ease;
  width: auto;
}
.woocommerce .cart_grouped .product-quantity .product-quantity__input {
  color: currentColor !important;
  transition: color 0.3s ease;
}
.woocommerce .cart_grouped .product-quantity .product-quantity__button {
  padding: 0.66rem;
  color: currentColor !important;
}
.woocommerce .cart_grouped .product-quantity .product-quantity__button_minus {
  margin-left: 0.5em;
}
.woocommerce .cart_grouped .product-quantity .product-quantity__button_plus {
  margin-right: 0.5em;
}
.woocommerce .cart_grouped .product-quantity_focused, .woocommerce .cart_grouped .product-quantity_hovered {
  border-color: var(--color-accent);
  color: var(--color-accent);
}

.woocommerce div.product form.cart .group_table .wc-grouped-product-add-to-cart-checkbox {
  display: none;
}
.woocommerce div.product form.cart .group_table .woocommerce-grouped-product-list-item__label {
  font-weight: bold;
}
.woocommerce div.product form.cart .group_table .woocommerce-grouped-product-list-item__quantity .button {
  padding: 8px 16px;
  font-size: 0.875em;
}
.woocommerce div.product form.cart .group_table .woocommerce-grouped-product-list-item__quantity .input-checkbox__label:before {
  font-size: 1.5rem;
}
.woocommerce div.product form.cart .group_table .woocommerce-grouped-product-list-item__price p.stock {
  margin-top: 0.25em;
  margin-bottom: 0;
  line-height: 1;
}
.woocommerce div.product form.cart .group_table .woocommerce-grouped-product-list-item__price .woocommerce-Price-amount:last-of-type:not(ins .woocommerce-Price-amount):not(del .woocommerce-Price-amount)::after, .woocommerce div.product form.cart .group_table .woocommerce-grouped-product-list-item__price ins:last-of-type::after {
  content: "";
  display: block;
  width: 100%;
}
.woocommerce div.product form.cart .group_table .woocommerce-grouped-product-list-item:last-child {
  border-bottom: none;
}
.woocommerce div.product form.cart .group_table td:first-child {
  width: auto;
  text-align: left;
}
.woocommerce div.product form.cart .group_table td {
  padding-bottom: 1em !important;
}

/*!========================================================================
 * 5. Cart Simple
 * ======================================================================!*/
.woocommerce .cart_simple {
  display: inline-flex;
  flex-wrap: wrap;
  background-color: #181818;
  border-radius: 512px;
  transition: background-color 0.3s ease;
  --cart-button-paddings: 1.5rem;
}
@media screen and (max-width: 991px) {
  .woocommerce .cart_simple {
    --cart-button-paddings: 1rem;
  }
}
@media screen and (max-width: 576px) {
  .woocommerce .cart_simple {
    width: 100%;
  }
}
.woocommerce .cart_simple.cart_hovered {
  background-color: var(--color-accent);
}
.woocommerce .cart_simple.cart_hovered .single_add_to_cart_button:hover {
  background-color: transparent;
}
.woocommerce .cart_simple .product-quantity:not(.d-none) ~ .single_add_to_cart_button {
  border-left-width: 1px;
  border-left-style: solid;
  border-left-color: rgba(128, 128, 128, 0.5);
  padding-left: min(var(--cart-button-paddings), 1.5rem);
}
.woocommerce .cart_simple .single_add_to_cart_button {
  flex: 1;
  width: 100%;
  color: #fff;
  background-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  margin: 0;
  padding-left: var(--cart-button-paddings);
  padding-right: var(--cart-button-paddings);
}
.woocommerce .cart_simple .single_add_to_cart_button:hover, .woocommerce .cart_simple .single_add_to_cart_button:focus {
  color: #fff;
  background-color: transparent;
}
.woocommerce .cart_simple .single_add_to_cart_button::before {
  content: "\e8cb";
  display: inline-block;
  font-family: "Material Icons";
  margin-right: 0.5em;
  vertical-align: bottom;
  font-size: 1rem;
  animation: none;
}
.woocommerce .cart_simple a.added_to_cart {
  display: inline-flex;
  align-items: center;
  padding: 0 var(--cart-button-paddings) 0 var(--cart-button-paddings);
  color: #fff !important;
  border-left: 1px solid rgba(128, 128, 128, 0.5);
}

/*!========================================================================
 * 6. Cart Variable
 * ======================================================================!*/
.woocommerce div.product form.cart .woocommerce-variation-description {
  width: 100%;
}
.woocommerce div.product form.cart .woocommerce-variation-description > *:first-child {
  margin-top: 0;
}
.woocommerce div.product form.cart .woocommerce-variation-description > *:last-child {
  margin-bottom: 0;
}

.woocommerce div.product form.cart .single_variation_wrap {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  gap: 1em;
}
.woocommerce div.product form.cart .woocommerce-variation-price {
  display: inline-flex;
  flex-wrap: wrap;
}
.woocommerce div.product form.cart .woocommerce-variation-price .price {
  border-radius: 512px;
  padding: 0.5em 0.75em;
  line-height: 1;
  border: 1px solid var(--color-accent);
}
.woocommerce div.product form.cart .woocommerce-variation-price .price .woocommerce-Price-amount.amount {
  line-height: 1;
}

.woocommerce div.product form.cart .reset_variations {
  display: inline-block;
  margin-top: 1.25em;
  margin-bottom: 1em;
  font-weight: bold;
  line-height: 1;
}
.woocommerce div.product form.cart .reset_variations::before {
  content: "\e5cd";
  font-family: "Material Icons";
  display: inline-block;
  vertical-align: bottom;
  margin-right: 0.25em;
}

.woocommerce div.product form.cart .variations {
  margin-bottom: 0;
}

.woocommerce div.product form.cart .variations td, .woocommerce div.product form.cart .variations th {
  line-height: 1.5;
}

.woocommerce .cart_variable {
  width: 100%;
  max-width: 960px;
  --cart-button-paddings: 1.5rem;
}
@media screen and (max-width: 991px) {
  .woocommerce .cart_variable {
    --cart-button-paddings: 1rem;
  }
}
.woocommerce .cart_variable .single_variation {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
  gap: 1em;
}
.woocommerce .cart_variable .single_variation > * > *:first-child {
  margin-top: 0;
}
.woocommerce .cart_variable .single_variation > * > *:last-child {
  margin-bottom: 0;
}
.woocommerce .cart_variable table {
  margin-bottom: 0;
}
.woocommerce .cart_variable table tr {
  border-bottom: none;
}
.woocommerce .cart_variable table tr:last-child th, .woocommerce .cart_variable table tr:last-child td {
  padding-bottom: 0;
}
.woocommerce .cart_variable table th {
  padding-top: 0;
  line-height: 1;
}
.woocommerce .cart_variable table td {
  padding-top: 0;
  line-height: 1;
}
.woocommerce .cart_variable select {
  padding: 16px 36px 16px 16px;
  width: 100%;
}
.woocommerce .cart_variable.cart_hovered .woocommerce-variation-add-to-cart:not(.woocommerce-variation-add-to-cart-disabled) {
  background-color: var(--color-accent);
}
.woocommerce .cart_variable.cart_hovered .woocommerce-variation-add-to-cart:not(.woocommerce-variation-add-to-cart-disabled) .single_add_to_cart_button:hover {
  background-color: transparent;
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart {
  display: inline-flex;
  background-color: #181818;
  border-radius: 512px;
  transition: background-color 0.3s ease;
}
@media screen and (max-width: 576px) {
  .woocommerce .cart_variable .woocommerce-variation-add-to-cart {
    width: 100%;
  }
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart .product-quantity {
  display: none;
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart:not(.woocommerce-variation-add-to-cart-disabled) .product-quantity {
  display: flex;
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart:not(.woocommerce-variation-add-to-cart-disabled) .product-quantity ~ .single_add_to_cart_button {
  border-left-width: 1px;
  border-left-style: solid;
  border-left-color: rgba(128, 128, 128, 0.5);
  padding-left: min(var(--cart-button-paddings), 1.5rem);
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart .single_add_to_cart_button {
  color: #fff;
  background-color: transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  margin: 0;
  padding-left: var(--cart-button-paddings);
  padding-right: var(--cart-button-paddings);
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart .single_add_to_cart_button:hover, .woocommerce .cart_variable .woocommerce-variation-add-to-cart .single_add_to_cart_button:focus {
  color: #fff;
  background-color: transparent;
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart .single_add_to_cart_button::before {
  content: "\e8cb";
  display: inline-block;
  font-family: "Material Icons";
  margin-right: 0.5em;
  vertical-align: bottom;
  font-size: 1rem;
  animation: none;
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart.woocommerce-variation-add-to-cart-disabled {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart.woocommerce-variation-add-to-cart-disabled > * {
  opacity: 0.3;
}
.woocommerce .cart_variable .woocommerce-variation-add-to-cart.woocommerce-variation-add-to-cart-disabled * {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.woocommerce .cart_variable a.added_to_cart {
  display: inline-flex;
  align-items: center;
  padding: 0 var(--cart-button-paddings) 0 var(--cart-button-paddings);
  color: #fff !important;
  border-left: 1px solid rgba(128, 128, 128, 0.5);
}

/*!========================================================================
 * 7. Checkout
 * ======================================================================!*/
.elementor-widget-woocommerce-checkout-page .e-checkout-secondary-title {
  margin-top: 0;
  margin-bottom: 0;
}
.elementor-widget-woocommerce-checkout-page .woocommerce-checkout #payment .payment_methods .payment_box {
  background-color: #f8f8f8 !important;
}
.elementor-widget-woocommerce-checkout-page .woocommerce-checkout #payment .place-order {
  margin-top: 1em !important;
}
.elementor-widget-woocommerce-checkout-page h3#ship-to-different-address {
  padding-left: 0;
  margin-top: 0;
}

#add_payment_method #payment div.payment_box p:first-child, .woocommerce-cart #payment div.payment_box p:first-child, .woocommerce-checkout #payment div.payment_box p:first-child {
  margin-top: 0;
}

/*!========================================================================
 * 8. Filter
 * ======================================================================!*/
.woocommerce .wc-block-components-filter-reset-button {
  border: none;
  text-decoration: none;
}
.woocommerce .wc-block-components-filter-reset-button:hover {
  border: none;
}

/*!========================================================================
 * 9. Woo Commerce Form
 * ======================================================================!*/
.woocommerce .woocommerce-form {
  --form-border-radius: 4px;
  --form-background-color: #313130;
  --form-primary-color: #CCCCCC;
  --form-secondary-color: #FFFFFF;
  --form-accent-color: #656453;
  --form-width: 600px;
  --form-heading-color: #181818;
  --form-submit-button-color: #ffffff;
  --form-submit-button-background-color: transparent;
  --form-submit-button-border-color: #ffffff;
  --form-submit-button-color-hover: #181818;
  --form-submit-button-background-color-hover: var(--color-accent-dark-theme);
  --form-submit-button-border-color-hover: var(--color-accent-dark-theme);
  border-radius: var(--form-border-radius);
  background-color: var(--form-background-color);
  border-color: transparent;
  border-width: 0;
  max-width: var(--form-width);
  width: 100%;
}
.woocommerce .woocommerce-form.woocommerce-form-login {
  border-top: none !important;
}

.woocommerce form .form-row.woocommerce-form-row_flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 0;
  padding-right: 0;
  gap: 20px;
}
.woocommerce form .form-row.woocommerce-form-row_flex::before, .woocommerce form .form-row.woocommerce-form-row_flex::after {
  display: none;
}

.woocommerce form.login {
  margin: 0 auto;
}

.woocommerce form .form-row .input-checkbox {
  margin: 0;
}

.woocommerce .woocommerce-form-login .woocommerce-form-login__rememberme.input-checkbox {
  text-align: left;
}

.woocommerce-form > *:first-child {
  margin-top: 0 !important;
}
.woocommerce-form > *:last-child {
  margin-bottom: 0 !important;
}

.woocommerce-button.woocommerce-form-login__submit {
  padding: 10px 24px;
  margin: 8px 0 0;
  color: var(--form-submit-button-color);
  background-color: var(--form-submit-button-background-color);
  border-color: var(--form-submit-button-border-color);
  min-width: 8em;
  float: none;
}
.woocommerce-button.woocommerce-form-login__submit:hover {
  color: var(--form-submit-button-color-hover);
  background-color: var(--form-submit-button-background-color-hover);
  border-color: var(--form-submit-button-border-color-hover);
}

.woocommerce .woocommerce-form-login .woocommerce-form-login__submit {
  margin-top: 8px;
  margin-right: 0;
}

.woocommerce form.checkout_coupon, .woocommerce form.login, .woocommerce form.register {
  --form-spacing-elements: 1em;
  padding: calc(var(--form-spacing-elements, 1em) * 3);
}

.woocommerce-form__heading {
  color: var(--form-heading-color);
}

.woocommerce form .form-row {
  padding-left: 0;
  padding-right: 0;
  margin-bottom: var(--form-spacing-elements, 1em);
}

legend {
  margin-top: 0.5em;
}

/*!========================================================================
 * 10. Mini Cart
 * ======================================================================!*/
.mini-cart {
  position: relative;
  display: flex;
  --mini-cart-thunbals-size: 60px;
  --mini-cart-width: 320px;
  --mini-cart-icon-size: 28px;
  --mini-cart-spacing-elements: 1.25em;
  --mini-cart-border-radius: 4px;
  --mini-cart-color-borders: var(--color-border-opacity-dark-theme);
  --mini-cart-buttons-color: #fff;
  --mini-cart-buttons-background-color: transparent;
  --mini-cart-buttons-color-hover: #181818;
  --mini-cart-buttons-background-color-hover: var(--color-accent-dark-theme);
  --mini-cart-buttons-border-color: #fff;
  --mini-cart-buttons-border-color-hover: var(--color-accent-dark-theme);
  --mini-cart-color-links: #fff;
  --mini-cart-color-links-hover: var(--color-accent-dark-theme);
  --mini-cart-color-background: #181818;
  --mini-cart-color-text: #fff;
  --mini-cart-color-quantity: #999;
  color: var(--mini-cart-color-text);
  font-size: 16px;
  transition: opacity 0.3s ease;
  height: 100%;
}
.mini-cart.loading {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: 0.5;
}
.mini-cart.loading * {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.mini-cart:hover .mini-cart__content, .mini-cart:focus .mini-cart__content, .mini-cart.mini-cart_dropdown_opened .mini-cart__content {
  opacity: 1;
  visibility: visible;
  transform: translate(0px, 0px);
  z-index: 60;
}
.mini-cart:hover .mini-cart__badge, .mini-cart:focus .mini-cart__badge, .mini-cart.mini-cart_dropdown_opened .mini-cart__badge {
  background-color: currentColor;
}

.mini-cart .mini-cart__icon {
  font-size: var(--mini-cart-icon-size);
  max-width: var(--mini-cart-icon-size);
  max-height: var(--mini-cart-icon-size);
}

.mini-cart_dropdown_left .mini-cart__content {
  right: 0;
  left: auto;
}

.mini-cart_dropdown_right .mini-cart__content {
  right: auto;
  left: 0;
}

.mini-cart__content {
  position: absolute;
  top: 100%;
  right: 0;
  transform: translate(0px, 1em);
  list-style-type: none;
  padding: 0;
  margin: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  min-width: var(--mini-cart-width);
  z-index: -1;
  padding: 0;
  background-color: var(--mini-cart-color-background);
  border-radius: var(--mini-cart-border-radius);
  text-align: left;
  box-shadow: 0px 0px 30px 0px rgba(24, 24, 24, 0.08);
  padding: calc(var(--mini-cart-spacing-elements) * 1.25);
}
.mini-cart__content:hover {
  opacity: 1;
  visibility: visible;
  transform: translate(0px, 0px);
  z-index: 999;
}
.mini-cart__content .widget_shopping_cart_content > *:first-child {
  margin-top: 0 !important;
}
.mini-cart__content .widget_shopping_cart_content > *:last-child {
  margin-bottom: 0 !important;
}

.mini-cart__wrapper-icon {
  position: relative;
  display: inline-flex;
}

.mini-cart__badge {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: calc(100% - var(--mini-cart-icon-size) / 3);
  left: calc(100% - var(--mini-cart-icon-size) / 3);
  min-width: 22px;
  min-height: 22px;
  aspect-ratio: 1;
  background-color: #000;
  border-radius: 128px;
  padding: 0.25em;
  transition: background-color 0.3s ease;
}
.mini-cart__badge[data-product-count="0"] {
  display: none;
}

.mini-cart__counter {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.mini-cart__link {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.mini-cart.woocommerce.widget_shopping_cart .buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: calc(var(--mini-cart-spacing-elements) / 2) var(--mini-cart-spacing-elements);
  color: var(--mini-cart-buttons-color);
  margin-top: calc(var(--mini-cart-spacing-elements) / 2);
  margin-bottom: calc(var(--mini-cart-spacing-elements) / 2);
}
.mini-cart.woocommerce.widget_shopping_cart .buttons:before, .mini-cart.woocommerce.widget_shopping_cart .buttons:after {
  display: none;
}
.mini-cart.woocommerce.widget_shopping_cart .buttons a {
  padding: 12px 20px;
  font-size: 14px;
  line-height: 1;
  margin: 0;
  color: currentColor;
  background-color: var(--mini-cart-buttons-background-color);
  border-color: var(--mini-cart-buttons-border-color);
}
.mini-cart.woocommerce.widget_shopping_cart .buttons a:hover {
  color: var(--mini-cart-buttons-color-hover);
  background-color: var(--mini-cart-buttons-background-color-hover);
  border-color: var(--mini-cart-buttons-border-color-hover);
}
.mini-cart.woocommerce.widget_shopping_cart .total {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: calc(var(--mini-cart-spacing-elements) / 2) var(--mini-cart-spacing-elements);
  padding-top: var(--mini-cart-spacing-elements);
  border-top: 1px solid var(--mini-cart-color-borders);
  margin-top: var(--mini-cart-spacing-elements);
  margin-bottom: var(--mini-cart-spacing-elements);
  line-height: 1;
}
.mini-cart.woocommerce.widget_shopping_cart .total .woocommerce-Price-amount.amount {
  color: var(--mini-cart-color-text);
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list dl {
  float: right;
  padding: 0;
  margin: 0;
  border: none;
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list dl dd > * {
  margin-top: 0;
  margin-bottom: 0;
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list li {
  position: relative;
  padding-left: 0;
  padding-top: 0;
  padding-bottom: 0;
  padding-right: 2em;
  color: var(--mini-cart-color-text);
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list li:not(:first-child) {
  padding-top: calc(var(--mini-cart-spacing-elements) / 2);
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list li:not(:last-child) {
  padding-bottom: calc(var(--mini-cart-spacing-elements) / 2);
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list li a {
  display: block;
  font-weight: 700;
  line-height: 1;
  color: var(--mini-cart-color-links);
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list li a:hover {
  color: var(--mini-cart-color-links-hover);
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list li a.remove {
  position: absolute;
  top: -0.25em;
  right: 0;
  left: auto;
  line-height: 0.95;
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list li img {
  float: left;
  margin-left: 0px;
  width: var(--mini-cart-thunbals-size);
  height: auto;
  box-shadow: none;
  border-radius: calc(var(--mini-cart-border-radius) / 2);
  margin-right: 16px;
}
.mini-cart.woocommerce.widget_shopping_cart .cart_list li .woocommerce-Price-amount.amount {
  color: var(--mini-cart-color-quantity);
  font-size: 95%;
}

/*!========================================================================
 * 11. My Account
 * ======================================================================!*/
.woocommerce-MyAccount-content > p:first-of-type, .woocommerce-MyAccount-content-wrapper > p:first-of-type {
  margin-top: 0;
}
.woocommerce-MyAccount-content > form:first-of-type > *:first-child, .woocommerce-MyAccount-content-wrapper > form:first-of-type > *:first-child {
  margin-top: 0;
}

.woocommerce-MyAccount-navigation {
  max-width: 360px;
}
.woocommerce-MyAccount-navigation ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: block;
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link {
  white-space: nowrap;
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link a {
  display: block;
  padding: 0.5em 1.5em;
  font-weight: bold;
  border-left: 1px solid var(--color-border-opacity);
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link.is-active a {
  border-left-width: 2px;
  border-left-color: currentColor;
  color: var(--color-accent);
  background-color: rgba(101, 100, 83, 0.**********);
}

@media screen and (min-width: 768px) {
  .woocommerce-account .woocommerce-MyAccount-navigation {
    float: left;
    width: calc(30% - 20px);
  }
  .woocommerce-account .woocommerce-MyAccount-content {
    float: right;
    width: calc(70% - 20px);
  }
}
@media screen and (max-width: 767px) {
  .woocommerce-MyAccount-content {
    margin-top: 2em;
  }
  .woocommerce-MyAccount-navigation {
    max-width: 100%;
  }
}
.woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab__edit-address .woocommerce-MyAccount-content-wrapper, .woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab__view-order .woocommerce-MyAccount-content-wrapper {
  padding: var(--sections-padding, 16px 30px);
  border-left: var(--sections-border-left-width, 1px) var(--sections-border-type, solid) var(--sections-border-color, #d5d8dc);
  border-bottom: var(--sections-border-bottom-width, 1px) var(--sections-border-type, solid) var(--sections-border-color, #d5d8dc);
  border-right: var(--sections-border-right-width, 1px) var(--sections-border-type, solid) var(--sections-border-color, #d5d8dc);
  border-top: var(--sections-border-top-width, 1px) var(--sections-border-type, solid) var(--sections-border-color, #d5d8dc);
}
.woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce-Address address, .woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce-Addresses address {
  padding: 0;
}
.woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce-Address .edit, .woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce-Addresses .edit {
  position: relative;
  margin: 0;
}
.woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce-address-fields fieldset legend, .woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce-EditAccountForm fieldset legend {
  padding: 0;
}
.woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce-address-fields, .woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) address {
  padding: 0;
  border: none;
}
.woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab__view-order .order_details, .woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab__view-order .woocommerce-table--order-downloads {
  padding: 0;
  border: none;
}

@media screen and (max-width: 767px) {
  .woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab .woocommerce .woocommerce-MyAccount-navigation ul li {
    width: 100%;
    border-right: none;
  }
}
/*!========================================================================
 * 12. My Account Addresses
 * ======================================================================!*/
.woocommerce-account .addresses .title h3 {
  margin-top: 0;
  margin-bottom: 0;
}

.woocommerce-account .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce h2, .elementor-widget-woocommerce-my-account .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce h3 {
  margin-top: 0 !important;
  margin-bottom: 1rem !important;
}

.woocommerce-account .woocommerce-Address-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  padding-bottom: 0;
}
.woocommerce-account .woocommerce-Address-title a[class=edit]::before {
  content: "\e3c9";
  display: inline-block;
  vertical-align: bottom;
  margin: 0 4px;
  font-family: "Material Icons";
}

.woocommerce-account .addresses .title::before, .woocommerce-account .addresses .title::after {
  display: none;
}

.woocommerce .woocommerce-customer-details address, .woocommerce-Address address {
  padding: 1em 1.5em !important;
  border-radius: 4px;
  border: 1px solid var(--color-border-opacity) !important;
  margin-top: 0;
  margin-bottom: 0;
}

/*!========================================================================
 * 13. Sitewide Notice
 * ======================================================================!*/
.woocommerce-store-notice, p.demo_store {
  position: fixed;
  top: auto;
  left: 0;
  bottom: 0;
  right: 0;
  text-align: left;
  padding-left: var(--gutter-horizontal);
  padding-right: var(--gutter-horizontal);
  background-color: #181818;
  color: #fff;
  --notice-dismiss-button-color: #000;
  --notice-dismiss-button-background-color: var(--color-accent-dark-theme);
  --notice-dismiss-button-border-color: transparent;
  --notice-dismiss-button-color-hover: #000;
  --notice-dismiss-button-background-color-hover: #fff;
  --notice-dismiss-button-border-color-hover: transparent;
}

.woocommerce-store-notice__dismiss-link, .woocommerce-store-notice a.woocommerce-store-notice__dismiss-link {
  padding: 10px 24px;
  margin: 10px;
  color: var(--notice-dismiss-button-color);
  background-color: var(--notice-dismiss-button-background-color);
  border-color: var(--notice-dismiss-button-border-color);
}
.woocommerce-store-notice__dismiss-link:hover, .woocommerce-store-notice a.woocommerce-store-notice__dismiss-link:hover {
  color: var(--notice-dismiss-button-color-hover);
  background-color: var(--notice-dismiss-button-background-color-hover);
  border-color: var(--notice-dismiss-button-border-color-hover);
}

.woocommerce-store-notice a, p.demo_store a {
  text-decoration: none;
}

.woocommerce-store-notice__container {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
}

.woocommerce-store-notice__text {
  font-weight: bold;
}

/*!========================================================================
 * 14. Ordering
 * ======================================================================!*/
.woocommerce .woocommerce-ordering {
  margin-bottom: 0;
}
/*!========================================================================
 * 15. Order Details
 * ======================================================================!*/
.woocommerce ul.order_details {
  --gap: 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: var(--gap);
  margin: 1.5em 0 1.5em;
  padding: 1.5em;
  border: 1px solid var(--color-border-solid);
  border-radius: 4px;
}
.woocommerce ul.order_details li {
  width: 100%;
  flex: 0 1 calc(33.3333% - var(--gap));
  margin: 0;
  padding: 0;
  border-right: 0;
}
@media screen and (max-width: 991px) {
  .woocommerce ul.order_details li {
    flex: 0 1 calc(50% - var(--gap));
  }
}
@media screen and (max-width: 576px) {
  .woocommerce ul.order_details li {
    flex: 0 1 100%;
  }
}
.woocommerce ul.order_details:before, .woocommerce ul.order_details:after {
  display: none;
}

.woocommerce .woocommerce-customer-details:last-child, .woocommerce .woocommerce-order-downloads:last-child {
  margin-bottom: 0;
}
@media screen and (max-width: 767px) {
  .woocommerce .woocommerce-customer-details, .woocommerce .woocommerce-order-downloads {
    margin-bottom: 1em;
  }
}

.woocommerce .woocommerce-order-details {
  margin-bottom: 1em;
}

/*!========================================================================
 * 16. Product Cart
 * ======================================================================!*/
.woocommerce div.product form.cart, div.product.elementor form.cart {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
@media screen and (max-width: 576px) {
  .woocommerce div.product form.cart .single_add_to_cart_button, div.product.elementor form.cart .single_add_to_cart_button {
    width: 100%;
  }
}
.woocommerce div.product form.cart.cart_form-invalid .single_add_to_cart_button, div.product.elementor form.cart.cart_form-invalid .single_add_to_cart_button {
  pointer-events: none;
  opacity: 0.5;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.woocommerce div.product form.cart.cart_form-error .single_add_to_cart_button:before, div.product.elementor form.cart.cart_form-error .single_add_to_cart_button:before {
  content: "\e602";
  font-family: "WooCommerce";
  animation: none;
}
.woocommerce div.product form.cart.cart_form-loading, div.product.elementor form.cart.cart_form-loading {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.woocommerce div.product form.cart.cart_form-loading *, div.product.elementor form.cart.cart_form-loading * {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.woocommerce div.product form.cart.cart_form-loading > *, div.product.elementor form.cart.cart_form-loading > * {
  opacity: 0.5;
}
.woocommerce div.product form.cart.cart_form-loading .single_add_to_cart_button:before, div.product.elementor form.cart.cart_form-loading .single_add_to_cart_button:before {
  content: "\e01c";
  font-family: "WooCommerce";
  animation: rotate 2s linear infinite;
}
.woocommerce div.product form.cart.cart_form-added .single_add_to_cart_button:before, div.product.elementor form.cart.cart_form-added .single_add_to_cart_button:before {
  content: "\e017";
  font-family: "WooCommerce";
  animation: none;
}

/*!========================================================================
 * 17. Product Details
 * ======================================================================!*/
.woocommerce .woocommerce-product-details__short-description > :first-child {
  margin-top: 0;
}
.woocommerce .woocommerce-product-details__short-description > :last-child {
  margin-bottom: 0;
}
.woocommerce .elementor-widget-woocommerce-product-price p.price {
  margin-top: 0;
  margin-bottom: 0;
}

/*!========================================================================
 * 18. Product Gallery
 * ======================================================================!*/
.woocommerce div.product div.images {
  margin-bottom: 0;
}

/*!========================================================================
 * 19. Product Quantity
 * ======================================================================!*/
.woocommerce .product-quantity:not(.elementor-widget-woocommerce-cart .product-quantity):not(.elementor-widget-woocommerce-checkout-page .product-quantity) {
  display: flex;
  --product-quantity-digits: 1;
  --product-quantity-amount: 1;
}
.woocommerce .product-quantity:not(.elementor-widget-woocommerce-cart .product-quantity):not(.elementor-widget-woocommerce-checkout-page .product-quantity) .quantity {
  width: max(var(--product-quantity-digits, 1) * 0.66rem, 1rem);
}
.woocommerce .product-quantity:not(.elementor-widget-woocommerce-cart .product-quantity):not(.elementor-widget-woocommerce-checkout-page .product-quantity) .qty {
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  color: #fff;
  padding: 0;
  margin: 0;
  width: auto;
  max-width: 100%;
  -moz-appearance: textfield;
}
.woocommerce .product-quantity:not(.elementor-widget-woocommerce-cart .product-quantity):not(.elementor-widget-woocommerce-checkout-page .product-quantity) .qty::-webkit-inner-spin-button, .woocommerce .product-quantity:not(.elementor-widget-woocommerce-cart .product-quantity):not(.elementor-widget-woocommerce-checkout-page .product-quantity) .qty::-webkit-outer-spin-button {
  -webkit-appearance: none;
          appearance: none;
}
.woocommerce .product-quantity__button {
  color: #fff;
  font-weight: bold;
  font-size: 1rem;
  padding: 1rem;
  cursor: pointer;
  pointer-events: none;
  opacity: 0.3;
  transition: opacity 0.3s ease;
  transition: opacity 0.3s ease, color 0.3s ease;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.woocommerce .product-quantity__button:hover {
  color: var(--color-accent-dark-theme);
}
.woocommerce .product-quantity_increment-active .product-quantity__button_plus {
  pointer-events: auto;
  opacity: 1;
}
.woocommerce .product-quantity_decrement-active .product-quantity__button_minus {
  pointer-events: auto;
  opacity: 1;
}
.woocommerce div.product form.cart div.quantity {
  margin: 0;
}
.woocommerce .product-quantity__button_minus {
  margin-left: 1rem;
}

/*!========================================================================
 * 20. Products Loop
 * ======================================================================!*/
@media screen and (max-width: 991px) {
  .elementor-widget-wc-archive-products ul.products {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 767px) {
  .elementor-widget-wc-archive-products ul.products {
    grid-template-columns: repeat(1, 1fr);
  }
}

.section-shop__header-inner {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 0.5em 1em;
}

/*!========================================================================
 * 21. Products Related
 * ======================================================================!*/
.woocommerce .products.related h2 {
  margin-top: 0;
}

/*!========================================================================
 * 22. Reviews
 * ======================================================================!*/
.woocommerce #reviews #comment {
  height: 120px;
}
.woocommerce #reviews ol.commentlist {
  padding: 0;
}
.woocommerce #reviews ol.commentlist li .comment-text {
  background-color: #fff;
  border: none;
  padding: 1.5rem 1.5rem 0.25rem;
}

.woocommerce #reviews #comments ol.commentlist {
  margin-top: 1.5em;
}

.woocommerce #reviews #review_form_wrapper {
  margin-top: 1em;
}

/*!========================================================================
 * 23. Select
 * ======================================================================!*/
.select2-container .select2-selection--single {
  height: 48px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 48px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 46px;
  width: 40px;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 16px;
  padding-right: 40px;
}

.woocommerce form .form-row label[for=billing_state] ~ .woocommerce-input-wrapper .select2-container, .woocommerce form .form-row label[for=shipping_state] ~ .woocommerce-input-wrapper .select2-container {
  margin-top: 1em;
}

.woocommerce-address-fields > *:last-child {
  margin-bottom: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: #888 transparent transparent transparent !important;
}

/*!========================================================================
 * 24. Result Count
 * ======================================================================!*/
.woocommerce .woocommerce-result-count {
  margin: 0;
}

/*!========================================================================
 * 25. Shop Table
 * ======================================================================!*/
.woocommerce table.my_account_orders, .woocommerce table.shop_table {
  border-collapse: collapse;
  border: none;
}
.woocommerce table.my_account_orders td, .woocommerce table.my_account_orders th, .woocommerce table.shop_table td, .woocommerce table.shop_table th {
  padding: 0.75em 4px;
}
.woocommerce table.my_account_orders .button, .woocommerce table.shop_table .button {
  padding: 12px 24px;
  line-height: 1;
  margin: 4px;
}
.woocommerce td.woocommerce-orders-table__cell-order-number {
  white-space: nowrap;
}

@media screen and (min-width: 768px) {
  .woocommerce table.my_account_orders .woocommerce-orders-table__cell-order-actions, table.shop_table .woocommerce-orders-table__cell-order-actions {
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (max-width: 767px) {
  .woocommerce table.shop_table td {
    border-top: none;
  }
  .woocommerce table.shop_table_responsive tr, .woocommerce-page table.shop_table_responsive tr {
    border: 1px solid var(--color-border-opacity);
    border-radius: 4px;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .woocommerce table.shop_table_responsive tr:not(:last-child), .woocommerce-page table.shop_table_responsive tr:not(:last-child) {
    margin-bottom: 1em;
  }
  .woocommerce table.shop_table_responsive tr:nth-child(2n) td, .woocommerce-page table.shop_table_responsive tr:nth-child(2n) td {
    background-color: unset;
  }
  .woocommerce table.my_account_orders td, .woocommerce table.my_account_orders th, .woocommerce table.shop_table td, .woocommerce table.shop_table th {
    padding: 0.5em 0px;
  }
  .woocommerce table.woocommerce-orders-table td, .woocommerce table.woocommerce-orders-table th {
    padding: 0.5em 20px;
  }
}
/*!========================================================================
 * 26. Stock
 * ======================================================================!*/
.woocommerce .stock {
  line-height: 1;
}
.woocommerce .stock::before {
  content: "";
  display: inline-block;
  vertical-align: bottom;
  font-family: "Material Icons";
  margin-right: 0.25em;
}
.woocommerce .stock.in-stock:before {
  content: "\e5ca";
}
.woocommerce .stock.out-of-stock:before {
  content: "\f023";
}

/*!========================================================================
 * 27. Tabs
 * ======================================================================!*/
.woocommerce .woocommerce-tabs {
  --border-radius: 4px;
  --gutter-inner: max((var(--gutter-horizontal-min) * 1px), (var(--gutter-horizontal) / 2));
  width: 100%;
  margin-bottom: 0;
}

.woocommerce div.product .woocommerce-tabs ul.tabs, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs {
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0;
}
.woocommerce div.product .woocommerce-tabs ul.tabs:before, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs:before {
  border-color: var(--color-border-solid);
  display: none;
}
.woocommerce div.product .woocommerce-tabs ul.tabs li, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li {
  margin: 0;
  border-radius: 0;
  border: none;
  background: transparent;
  padding: 0;
  border-left: 1px solid var(--color-border-solid);
  border-top: 1px solid var(--color-border-solid);
}
.woocommerce div.product .woocommerce-tabs ul.tabs li a, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li a {
  padding: 1.25rem 2rem;
  line-height: 1;
  width: 100%;
  display: block;
}
.woocommerce div.product .woocommerce-tabs ul.tabs li:first-child, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li:first-child {
  border-top-left-radius: var(--border-radius);
}
.woocommerce div.product .woocommerce-tabs ul.tabs li:last-child, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li:last-child {
  border-right: 1px solid var(--color-border-solid);
  border-top-right-radius: var(--border-radius);
}
.woocommerce div.product .woocommerce-tabs ul.tabs li.active, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li.active {
  background: #181818;
  color: #fff;
  border-bottom-color: #181818;
}
.woocommerce div.product .woocommerce-tabs ul.tabs li:before, .woocommerce div.product .woocommerce-tabs ul.tabs li:after, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li:before, .woocommerce div.product.elementor .woocommerce-tabs ul.tabs li:after {
  display: none;
}

body.woocommerce div.product .woocommerce-tabs .panel, body.woocommerce div.product.elementor .woocommerce-tabs .panel {
  padding: var(--gutter-inner);
  margin-bottom: 0;
  border-top: 1px solid var(--color-border-solid);
  border-left: 1px solid var(--color-border-solid);
  border-right: 1px solid var(--color-border-solid);
  border-bottom: 1px solid var(--color-border-solid);
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}
body.woocommerce div.product .woocommerce-tabs .panel > h1, body.woocommerce div.product .woocommerce-tabs .panel h2, body.woocommerce div.product .woocommerce-tabs .panel h3, body.woocommerce div.product .woocommerce-tabs .panel h4, body.woocommerce div.product .woocommerce-tabs .panel h5, body.woocommerce div.product .woocommerce-tabs .panel h6, body.woocommerce div.product .woocommerce-tabs .panel p, body.woocommerce div.product .woocommerce-tabs .panel form, body.woocommerce div.product .woocommerce-tabs .panel .commentlist, body.woocommerce div.product.elementor .woocommerce-tabs .panel > h1, body.woocommerce div.product.elementor .woocommerce-tabs .panel h2, body.woocommerce div.product.elementor .woocommerce-tabs .panel h3, body.woocommerce div.product.elementor .woocommerce-tabs .panel h4, body.woocommerce div.product.elementor .woocommerce-tabs .panel h5, body.woocommerce div.product.elementor .woocommerce-tabs .panel h6, body.woocommerce div.product.elementor .woocommerce-tabs .panel p, body.woocommerce div.product.elementor .woocommerce-tabs .panel form, body.woocommerce div.product.elementor .woocommerce-tabs .panel .commentlist {
  max-width: calc(960px - var(--gutter-inner));
}
body.woocommerce div.product .woocommerce-tabs .panel > *:first-child, body.woocommerce div.product.elementor .woocommerce-tabs .panel > *:first-child {
  margin-top: 0;
}
body.woocommerce div.product .woocommerce-tabs .panel > *:last-child, body.woocommerce div.product.elementor .woocommerce-tabs .panel > *:last-child {
  margin-bottom: 0;
}
body.woocommerce div.product .woocommerce-tabs .panel .woocommerce-Reviews-title, body.woocommerce div.product.elementor .woocommerce-tabs .panel .woocommerce-Reviews-title {
  margin-top: 0;
}

@media screen and (max-width: 767px) {
  .woocommerce div.product .woocommerce-tabs ul.tabs li {
    width: 100%;
    border-right: 1px solid var(--color-border-solid);
    border-top-right-radius: 0;
  }
  .woocommerce div.product .woocommerce-tabs ul.tabs li:first-child {
    border-top-right-radius: var(--border-radius);
  }
  .woocommerce div.product .woocommerce-tabs ul.tabs li:last-child {
    border-top-right-radius: 0;
  }
  .woocommerce div.product .woocommerce-tabs .panel {
    border-top-right-radius: 0;
  }
}