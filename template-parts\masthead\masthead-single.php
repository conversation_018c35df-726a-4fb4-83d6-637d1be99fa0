<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

global $post;

$page_title         = get_the_title();
$post_show_info     = true;
$post_meta_set      = array( 'date', 'categories', 'comments', 'author' );
$has_post_thumbnail = has_post_thumbnail();

$section_attributes = array(
	'class' => array(
		'masthead_single-post',
		'mt-header-height',
		'pt-medium',
		'text-center',
		'container-fluid-gutters',
		'max-lg-px-0',
	),
	'id'    => 'page-masthead',
);

$section_attributes = Utilities::get_component_attributes(
	$section_attributes,
	array(
		'name'         => 'Masthead',
		'hasAnimation' => false,
	),
);

$col_attributes = array(
	'class' => array( 'col-12' ),
);

$image_wrapper = array(
	'class' => array(
		'masthead__media',
		'js-ajax-transition-element',
		'js-masthead__animation-mask',
	),
);

$thumbnail_args = array(
	'id'           => get_post_thumbnail_id(),
	'type'         => 'image_limited',
	'lazy_wrapper' => array(
		'class' => array(
			'mx-auto',
			'js-masthead__animation-mask-wrapper',
			'overflow-hidden',
			'w-100',
			'h-100',
			'js-ajax-transition-element__mask',
		),
	),
	'image'        => array(
		'class' => array(
			'lazy',
			'js-parallax__inner',
			'js-ajax-transition-element__media',
		),
	),
);

$heading_attributes = array(
	'class' => array(
		'entry-title',
		'masthead__heading',
		'my-0',
		'h2',
	),
);

$meta_args = array(
	'attributes' => array(
		'class' => array( 'mb-3' ),
	),
	'set'        => $post_meta_set,
);

$masthead_header_attributes = array(
	'class' => array(
		'container',
		'masthead__header',
	),
);

if ( $has_post_thumbnail ) {
	$masthead_header_attributes['class'][] = 'mb-small';
} else {
	$masthead_header_attributes['class'][] = 'mb-3';
}

?>

<div <?php Utilities::print_attributes( $section_attributes ); ?>>
	<!-- Content -->
	<div <?php Utilities::print_attributes( $masthead_header_attributes ); ?>>
		<div class="row justify-content-center">
			<div <?php Utilities::print_attributes( $col_attributes ); ?>>
				<?php if ( $post_show_info && ! empty( $post_meta_set ) ) : ?>
					<!-- Post meta -->
					<?php get_template_part( 'template-parts/blog/post/partials/meta', '', $meta_args ); ?>
					<!-- - Post meta -->
				<?php endif; ?>
				<?php if ( ! empty( $page_title ) ) : ?>
					<h1 <?php Utilities::print_attributes( $heading_attributes ); ?>><?php echo wp_kses( $page_title, wp_kses_allowed_html( 'post' ) ); ?></h1>
				<?php endif; ?>
			</div>
		</div>
	</div>
	<!-- - Content -->

	<?php if ( $has_post_thumbnail ) : ?>
		<!-- Featured media -->
		<div <?php Utilities::print_attributes( $image_wrapper ); ?>>
			<?php get_template_part( 'template-parts/lazy/lazy', 'media', $thumbnail_args ); ?>
		</div>
		<!-- Featured media -->
	<?php endif; ?>
</div>
