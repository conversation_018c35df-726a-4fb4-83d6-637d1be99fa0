<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$defaults = array(
	'label'   => Utilities::get_kit_settings( 'scroll_down_label', esc_html__( 'Scroll Down', 'asli' ) ),
	'href'    => '#asli-scroll-down',
	'rel'     => 'nofollow',
	'target'  => '',
	'icon'    => 'asterisk',
	'options' => array(
		'loopRounds' => Utilities::get_kit_settings( 'scroll_down_loop_rounds', 1 ),
		'delimiter'  => Utilities::get_kit_settings( 'scroll_down_delimiter', '&nbsp;&nbsp;→&nbsp;&nbsp;' ),
	),
);

$args = wp_parse_args( $args, $defaults );

$tag = $args['href'] ? 'a' : 'div';

$container_attributes = array(
	'class'                  => array(),
	'data-arts-os-animation' => 'false',
);

$container_attributes = Utilities::get_component_attributes(
	$container_attributes,
	array(
		'name'    => 'RotatingButton',
		'options' => is_array( $args['options'] ) ? $args['options'] : false,
	)
);

$link_attributes = array(
	'class' => array(
		'rotating-button__link',
		'js-rotating-button__link',
	),
);

$cursor_attributes = array(
	'color' => 'transparent',
	'scale' => 'current',
);

if ( $args['href'] ) {
	$link_attributes['href'] = $args['href'];

	if ( ! empty( $args['rel'] ) ) {
		$link_attributes['rel'] = $args['rel'];
	}

	if ( ! empty( $args['target'] ) ) {
		$link_attributes['target'] = $args['target'];
	}

	$link_attributes['data-arts-cursor-follower-target'] = wp_json_encode( $cursor_attributes );
}

?>

<div <?php Utilities::print_attributes( $container_attributes ); ?>>
	<<?php echo esc_html( $tag ); ?> <?php Utilities::print_attributes( $link_attributes ); ?>>
		<?php if ( ! empty( $args['label'] ) ) : ?>
			<div class="rotating-button__label js-rotating-button__label"><?php echo esc_html( $args['label'] ); ?></div>
		<?php endif; ?>
		<?php if ( $args['icon'] === 'asterisk' ) : ?>
			<div class="rotating-button__icon js-rotating-button__icon">
				<?php get_template_part( 'template-parts/svg/asterisk' ); ?>
			</div>
		<?php elseif ( $args['icon'] === 'progress' ) : ?>
			<div class="rotating-button__icon js-rotating-button__progress">
				<div class="rotating-button__dot">
					<?php get_template_part( 'template-parts/svg/circle' ); ?>
				</div>
			</div>
		<?php endif; ?>
	</<?php echo esc_html( $tag ); ?>>
</div>
