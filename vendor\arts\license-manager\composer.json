{"name": "arts/license-manager", "type": "library", "repositories": [{"type": "path", "url": "/Users/<USER>/Projects/Framework/packages/ArtsNoticeManager", "options": {"symlink": true}}], "autoload": {"psr-4": {"Arts\\LicenseManager\\": "src/php/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "arts/notice-manager": "@dev"}, "config": {"allow-plugins": {"composer/installers": true}, "optimize-autoloader": true, "sort-packages": true}}