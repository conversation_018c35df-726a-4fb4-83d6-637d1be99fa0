"use strict";(self.webpackChunkArtsInfiniteList=self.webpackChunkArtsInfiniteList||[]).push([[247],{165:(t,i,e)=>{e.r(i),e.d(i,{default:()=>a});var s=e(199);const n={autoInit:!0,duration:6};class a extends s.v{constructor({autoLoad:t=!1,container:i,options:e,controller:s,config:a}){super({autoLoad:t,container:i,options:e,controller:s,config:a,defaults:n}),this._handlers={interactionStart:this._onInteractionStart.bind(this),interactionComplete:this._onInteractionComplete.bind(this),scrollSnap:this._onScrollSnap.bind(this),visibleUpdate:this._onVisibleUpdate.bind(this)},this.config.autoInit&&this.init()}init(){this.enabled||"number"==typeof this.config.duration&&(this._attachEvents(),this._attachToggleViewEvents(),this._updateTimer(),this._updateAnimation(),this._onVisibleUpdate(this.controller.visible),this.enabled=!0,this.initialized=!0)}destroy(){this.enabled&&this.initialized&&(this._stopTimer(),this._detachEvents(),this._detachToggleViewEvents(),this.controller.emit("autoplayStop"),this.enabled=!1)}enable(){!this.enabled&&this.initialized&&"number"==typeof this.config.duration&&(this._attachEvents(),this._updateTimer(),this._updateAnimation(),this._onVisibleUpdate(this.controller.visible),this.enabled=!0)}disable(){this.enabled&&this.initialized&&(this._stopTimer(),this._detachEvents(),this.controller.emit("autoplayStop"),this.enabled=!1)}get timer(){return this._timer}set timer(t){this._timer=t}_updateTimer(){this.controller.emit("autoplayStart"),this.timer=gsap.delayedCall(this.config.duration,(()=>{this.controller.emit("autoplayComplete"),this.controller.snapNext()})).pause()}get animation(){return this._animation}set animation(t){this._animation=t}_updateAnimation(){const t=this;this.animation=gsap.timeline({paused:!0}).to({},{duration:this.config.duration,ease:"none",onUpdate:function(){t.controller.emit("autoplayProgress",this.progress())}})}_stopTimer(){this.controller.emit("autoplayStop"),this.timer&&this.timer.kill(),this.animation&&this.animation.kill()}_pauseTimer(){this.controller.emit("autoplayPause"),this.timer.pause(),this.animation.pause()}_resumeTimer(){this.animation.paused()&&(this.controller.emit("autoplayResume"),this.timer.play(),this.animation.play())}_restartTimer(){this.controller.emit("autoplayStart"),this.timer.restart(!0),this.animation.restart(!0)}_attachEvents(){this.controller.on("dragStart",this._handlers.interactionStart),this.controller.on("interactionStart",this._handlers.interactionStart),this.controller.on("scrollSnap",this._handlers.scrollSnap),this.controller.on("visibleUpdate",this._handlers.visibleUpdate)}_detachEvents(){this.controller.off("dragStart",this._handlers.interactionStart),this.controller.off("interactionStart",this._handlers.interactionStart),this.controller.off("scrollSnap",this._handlers.scrollSnap),this.controller.off("visibleUpdate",this._handlers.visibleUpdate)}_onInteractionStart(){this._pauseTimer()}_onInteractionComplete(){this._resumeTimer()}_onScrollSnap(){this.controller.visible&&this._restartTimer()}_onVisibleUpdate(t){t?this._resumeTimer():this._pauseTimer()}}}}]);