<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'wp_ajax_woocommerce_arts_add_to_cart_variable', 'arts_ajax_add_to_cart_variable' );
add_action( 'wp_ajax_nopriv_woocommerce_arts_add_to_cart_variable', 'arts_ajax_add_to_cart_variable' );
if ( ! function_exists( 'arts_ajax_add_to_cart_variable' ) ) {
	function arts_ajax_add_to_cart_variable() {
		// phpcs:disable WordPress.Security.NonceVerification.Missing
		if ( ! isset( $_POST['product_id'] ) ) {
			return;
		}

		$product_id        = apply_filters( 'woocommerce_add_to_cart_product_id', absint( $_POST['product_id'] ) );
		$quantity          = empty( $_POST['quantity'] ) ? 1 : wc_stock_amount( wp_unslash( $_POST['quantity'] ) );
		$passed_validation = apply_filters( 'woocommerce_add_to_cart_validation', true, $product_id, $quantity );
		$product_status    = get_post_status( $product_id );
		$variation_id      = isset( $_POST['variation_id'] ) ? absint( $_POST['variation_id'] ) : 0;
		$variation         = array();
		$cart_item_data    = $_POST;

		foreach ( $cart_item_data as $key => $value ) {
			if ( preg_match( '/^attribute*/', $key ) ) {
				$variation[ $key ] = $value;
			}
		}

		foreach ( $variation as $key => $value ) {
			$variation[ $key ] = stripslashes( $value );
		}

		if ( $passed_validation && false !== WC()->cart->add_to_cart( $product_id, $quantity, $variation_id, $variation ) && 'publish' === $product_status ) {

			do_action( 'woocommerce_ajax_added_to_cart', $product_id );

			if ( 'yes' === get_option( 'woocommerce_cart_redirect_after_add' ) ) {
				wc_add_to_cart_message( array( $product_id => $quantity ), true );
			}

			WC_AJAX::get_refreshed_fragments();

		} else {

			// If there was an error adding to the cart, redirect to the product page to show any errors.
			$data = array(
				'error'       => true,
				'product_url' => apply_filters( 'woocommerce_cart_redirect_after_error', get_permalink( $product_id ), $product_id ),
			);

			wp_send_json( $data );
		}
			// phpcs:enable
	}
}
