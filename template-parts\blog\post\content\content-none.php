<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Content: None
 */

?>

<?php if ( is_home() && current_user_can( 'publish_posts' ) ) : ?>
	<p><?php echo sprintf( '%1$s <a href="%2$s">%3$s</a>', esc_html__( 'Ready to publish your first post?', 'asli' ), esc_url( admin_url( 'post-new.php' ) ), esc_html__( 'Get started here!', 'asli' ) ); ?></p>
<?php elseif ( is_search() ) : ?>
	<p><?php echo esc_html__( 'Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'asli' ); ?></p>
	<?php get_search_form(); ?>
<?php else : ?>
	<p><?php echo esc_html__( 'It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'asli' ); ?></p>
	<?php get_search_form(); ?>
<?php endif; ?>
