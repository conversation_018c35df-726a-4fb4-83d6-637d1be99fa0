<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

global $post;

$inner_container_attributes = array(
	'class' => array(),
);
$inner_row_attributes       = array(
	'class' => array(
		'row',
		'justify-content-center',
	),
);
$inner_col_attributes       = array(
	'class' => array(),
);

if ( Utilities::is_woocommerce() || Utilities::is_cart() || Utilities::is_account_page() ) {
	$inner_container_attributes['class'][] = 'container-fluid-gutters';
	$inner_col_attributes['class'][]       = 'col-12';
} else {
	$inner_container_attributes['class'][] = 'container';
	$inner_col_attributes['class'][]       = 'col-lg-8';
	$inner_col_attributes['class'][]       = 'section-blog__post';
}

get_header();

if ( trim( $post->post_content ) !== '' ) {
	$inner_container_attributes['class'][] = 'py-small';
}

$inner_container_attributes = apply_filters( 'arts/page_inner/container/attributes', $inner_container_attributes );
$inner_row_attributes       = apply_filters( 'arts/page_inner/row/attributes', $inner_row_attributes );
$inner_container_attributes = apply_filters( 'arts/page_inner/column/attributes', $inner_container_attributes );

/**
 * Page Masthead
 */
get_template_part( 'template-parts/masthead/masthead' );
the_post();

?>

<?php if ( ! Utilities::is_built_with_elementor() ) : ?>
	<div <?php Utilities::print_attributes( $inner_container_attributes ); ?>>
		<div <?php Utilities::print_attributes( $inner_row_attributes ); ?>>
			<div <?php Utilities::print_attributes( $inner_col_attributes ); ?>>
				<div class="post">
					<!-- Post content -->
					<div class="post__content clearfix">
						<?php the_content(); ?>
					</div>
					<?php
						wp_link_pages(
							array(
								'before'      => '<div class="page-links">' . esc_html__( 'Pages:', 'asli' ),
								'after'       => '</div>',
								'link_before' => '<span class="page-number">',
								'link_after'  => '</span>',
							)
						);
					?>
					<!-- - Post content -->
					<?php if ( comments_open() || get_comments_number() ) : ?>
						<!-- Post comments -->
						<div class="post__comments mt-small">
							<?php comments_template(); ?>
						</div>
						<!-- - Post comments -->
					<?php endif; ?>
				</div>
			</div>
		</div>
	</div>
<?php else : ?>
	<?php if ( ! function_exists( 'elementor_theme_do_location' ) || ! elementor_theme_do_location( 'single-page' ) ) : // Elementor "page" location ?>
		<?php the_content(); ?>
	<?php endif; ?>
<?php endif; ?>

<?php get_footer(); ?>
