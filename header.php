<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$ajax_enabled                 = Utilities::get_kit_settings( 'ajax_enabled', false );
$preloader_enabled            = arts_is_preloader_enabled();
$container_attributes         = arts_get_main_container_attributes();
$container_content_attributes = arts_get_main_content_attributes();

?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta http-equiv="x-ua-compatible" content="ie=edge">
	<?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
	<?php arts_body_open(); ?>

	<?php if ( $ajax_enabled ) : ?>
		<!-- PAGE AJAX OUTER WRAPPER -->
		<div data-barba="wrapper">
			<?php do_action( 'asli/barba/after_wrapper_start' ); ?>
	<?php endif; ?>

	<?php if ( $preloader_enabled ) : ?>
		<!-- PAGE PRELOADER -->
		<?php get_template_part( 'template-parts/preloader/preloader' ); ?>
		<!-- - PAGE PRELOADER -->
	<?php endif; ?>

	<?php if ( ! function_exists( 'elementor_theme_do_location' ) || ! elementor_theme_do_location( 'header' ) ) : ?>
		<!-- PAGE HEADER [fallback] -->
		<?php get_template_part( 'template-parts/header/header' ); ?>
		<!-- - PAGE HEADER [fallback] -->
	<?php endif; ?>

	<!-- PAGE MAIN CONTAINER -->
	<main <?php Utilities::print_attributes( $container_attributes ); ?>>
		<!-- PAGE CONTENT -->
		<div <?php Utilities::print_attributes( $container_content_attributes ); ?>>
