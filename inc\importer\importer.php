<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\WizardSetup\Plugin;

add_filter( 'arts/wizard_setup/plugin/config', 'arts_wizard_setup_plugin_config' );
if ( ! function_exists( 'arts_wizard_setup_plugin_config' ) ) {
	/**
	 * Configure the theme setup settings after demo import.
	 *
	 * @param array $config The configuration array to be modified.
	 * @return array The modified configuration array.
	 */
	function arts_wizard_setup_plugin_config( $config ) {
		$config['license_required']                = false;
		$config['regenerate_thumbnails_on_import'] = false;

		// Setup demo data
		if ( ! isset( $config['setup_demo_data'] ) ) {
			$config['setup_demo_data'] = array();
		}

		if ( isset( $config['setup_demo_data'][0] ) ) {
			$config['setup_demo_data'][0]['wie_url']              = false;
		}

		// Setup child theme
		if ( ! isset( $config['setup_child_theme'] ) ) {
			$config['setup_child_theme'] = array();
		}

		$config['setup_child_theme']['screenshot'] = get_template_directory() . '/inc/importer/screenshot.png';

		// Setup Elementor
		if ( ! isset( $config['setup_elementor'] ) ) {
			$config['setup_elementor'] = array();
		}

		if ( ! isset( $config['setup_elementor']['options'] ) || ! is_array( $config['setup_elementor']['options'] ) ) {
			$config['setup_elementor']['options'] = array();
		}

		// Update Page title selector
		$config['setup_elementor']['options']['page_title_selector'] = 'h1.entry-title';

		// Update Disable default color schemes and fonts
		$config['setup_elementor']['options']['disable_color_schemes']      = 'yes';
		$config['setup_elementor']['options']['disable_typography_schemes'] = 'yes';

		// Update CSS print method
		$config['setup_elementor']['options']['css_print_method'] = 'internal';

		// Enable Optimized Assets Loading
		$config['setup_elementor']['options']['experiment-e_optimized_assets_loading'] = 'active';

		// Allow SVG uploads
		$config['setup_elementor']['options']['unfiltered_files_upload'] = '1';

		// Enable Flex containers
		$config['setup_elementor']['options']['experiment-container'] = 'active';

		// Disable WooCommerce Elementor's mini-cart template
		$config['setup_elementor']['options']['use_mini_cart_template'] = 'no';

		// Setup WooCommerce
		if ( ! isset( $config['setup_woocommerce'] ) ) {
			$config['setup_woocommerce'] = array();
		}

		// Setup WordPress
		if ( ! isset( $config['setup_wordpress'] ) ) {
			$config['setup_wordpress'] = array();
		}

		$config['setup_wordpress']['home_page_title'] = 'Images Hover / WebGL';
		$config['setup_wordpress']['blog_page_title'] = 'Blog';
		$config['setup_wordpress']['menu']            = array(
			'name'     => 'Top Menu All',
			'location' => 'main_menu',
		);

		return $config;
	}
}

Plugin::instance();
