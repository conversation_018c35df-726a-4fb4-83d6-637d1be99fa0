<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_smooth_scroll_options' ) ) {
	/**
	 * Get smooth scroll options.
	 *
	 * @return array|bool $options
	 */
	function arts_get_smooth_scroll_options() {
		$options                  = false;
		$smooth_scrolling_enabled = Utilities::get_kit_settings( 'smooth_scrolling_enabled', false );

		if ( isset( $_GET['smooth_scrolling'] ) ) {
			$force_smooth_scrolling_enabled = preg_replace( '/[^-a-zA-Z0-9_]/', '', $_GET['smooth_scrolling'] );

			if ( $force_smooth_scrolling_enabled === 'yes' ) {
				$smooth_scrolling_enabled = true;
			} elseif ( $force_smooth_scrolling_enabled === 'no' ) {
				$smooth_scrolling_enabled = false;
			}
		}

		if ( $smooth_scrolling_enabled ) {
			$smooth_scrolling_duration         = Utilities::get_kit_settings( 'smooth_scrolling_duration', 1.2 );
			$smooth_scrolling_gsap_raf_enabled = Utilities::get_kit_settings( 'smooth_scrolling_gsap_raf_enabled', true );

			$options = array(
				'duration'   => floatval( $smooth_scrolling_duration ),
				'easing'     => 'expo.out',
				'useGSAPRaf' => boolval( $smooth_scrolling_gsap_raf_enabled ),
			);
		}

		return $options;
	}
}
