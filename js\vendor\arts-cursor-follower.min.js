(()=>{"use strict";var t={417:t=>{var e=function(t){return function(t){return!!t&&"object"==typeof t}(t)&&!function(t){var e=Object.prototype.toString.call(t);return"[object RegExp]"===e||"[object Date]"===e||function(t){return t.$$typeof===s}(t)}(t)},s="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function i(t,e){return!1!==e.clone&&e.isMergeableObject(t)?a((s=t,Array.isArray(s)?[]:{}),t,e):t;var s}function n(t,e,s){return t.concat(e).map((function(t){return i(t,s)}))}function o(t){return Object.keys(t).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return t.propertyIsEnumerable(e)})):[]}(t))}function r(t,e){try{return e in t}catch(t){return!1}}function a(t,s,l){(l=l||{}).arrayMerge=l.arrayMerge||n,l.isMergeableObject=l.isMergeableObject||e,l.cloneUnlessOtherwiseSpecified=i;var h=Array.isArray(s);return h===Array.isArray(t)?h?l.arrayMerge(t,s,l):function(t,e,s){var n={};return s.isMergeableObject(t)&&o(t).forEach((function(e){n[e]=i(t[e],s)})),o(e).forEach((function(o){(function(t,e){return r(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))})(t,o)||(r(t,o)&&s.isMergeableObject(e[o])?n[o]=function(t,e){if(!e.customMerge)return a;var s=e.customMerge(t);return"function"==typeof s?s:a}(o,s)(t[o],e[o],s):n[o]=i(e[o],s))})),n}(t,s,l):i(s,l)}a.all=function(t,e){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce((function(t,s){return a(t,s,e)}),{})};var l=a;t.exports=l},624:(t,e,s)=>{s.r(e)},661:(t,e,s)=>{s.r(e)}},e={};function s(i){var n=e[i];if(void 0!==n)return n.exports;var o=e[i]={exports:{}};return t[i](o,o.exports,s),o.exports}s.d=(t,e)=>{for(var i in e)s.o(e,i)&&!s.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},s.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),s.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i={};(()=>{s.d(i,{default:()=>f});var t=s(417);const e={init:!0,animationDuration:.25,hideIFramesHover:!0,trailing:.25,elastic:2,highlight:{includeClass:"cursor-highlight",excludeClass:"cursor-no-highlight",scale:"80px",opacity:.2},clickScale:!1,cursorElements:{followerSelector:'[data-arts-cursor-follower-element="follower"]',wrapperSelector:'[data-arts-cursor-follower-element="wrapper"]',loadingSelector:'[data-arts-cursor-follower-element="loading"]',toggleLabelSelector:'[data-arts-cursor-follower-element="toggleLabel"]',toggleClassSelector:'[data-arts-cursor-follower-element="toggleClass"]',arrowUpSelector:'[data-arts-cursor-follower-element="arrowUp"]',arrowRightSelector:'[data-arts-cursor-follower-element="arrowRight"]',arrowDownSelector:'[data-arts-cursor-follower-element="arrowDown"]',arrowLeftSelector:'[data-arts-cursor-follower-element="arrowLeft"]'},delegatedElementSelector:"[data-arts-cursor-follower-delegated]",hideNativeElementSelector:'[data-barba="wrapper"]',matchMedia:"(hover: hover) and (pointer: fine)",passiveListeners:!0,useCSSVars:!1,useGSAPRaf:!1};class n{static getElementByStringSelector(t,e=document){if("string"==typeof t){const s=e.querySelector(t);if(s&&null!==s)return s}if(n.isHTMLElement(t))return t}static isHTMLElement(t,e="Element"){if(!t)return!1;let s=t.__proto__;for(;null!==s;){if(s.constructor.name===e)return!0;s=s.__proto__}return!1}static getElementsInContainer(t,e){return"string"==typeof e&&t&&null!==t?[...t.querySelectorAll(e)]:"object"==typeof e?[...e]:void 0}}class o{constructor({container:t,attributeSelector:s="data-arts-cursor-follower-options",options:i}){this._data=e,n.isHTMLElement(t)&&this._transformOptions({container:t,attributeSelector:s,options:i})}get data(){return this._data}set data(t){this._data=t}_transformOptions({container:s,attributeSelector:i,options:n}){if(!s)return{};let r={};if(n&&e&&(r=t(e,n)),i){let e;e="DATA"===i?function(t,e={separator:"-",pattern:/^/}){let s={};var i;return void 0===e.separator&&(e.separator="-"),Array.prototype.slice.call(t.attributes).filter((i=e.pattern,function(t){let e;return e=/^data\-/.test(t.name),void 0===i?e:e&&i.test(t.name.slice(5))})).forEach((function(t){t.name.slice(5).split(e.separator).reduce((function(e,s,i,n){return"data"===s?e:(i===n.length-1?e[s]=t.value:e[s]=e[s]||{},e[s])}),s)})),s}(s):o.parseOptionsStringObject(s.getAttribute(i)),e&&0!==Object.keys(e).length&&(e=o.transformPluginOptions(e),r=t(r,e))}this.data=r}static parseOptionsStringObject(t){let e={};if(!t)return e;try{e=JSON.parse(o.convertStringToJSON(t))}catch(e){console.warn(`${t} is not a valid parameters object`)}return e}static convertStringToJSON(t){if(t)return t.replace(/'/g,'"').replace(/(?=[^"]*(?:"[^"]*"[^"]*)*$)(\w+:)|(\w+ :)/g,(function(t){return'"'+t.substring(0,t.length-1)+'":'}))}static transformPluginOptions(t){return t}}class r{constructor({elements:t,options:e}){this._isAnimating=!1,this._options={},this._follower={x:0,y:0},this._mouse={x:0,y:0},this._velocity={x:0,y:0},this._mouseMagnetic={x:!1,y:!1},this._handlers={mouseMove:this._onMouseMove.bind(this),mouseMoveOnce:this._onMouseMoveOnce.bind(this),animationFrame:this._onAnimationFrame.bind(this)},this._setter={},this._firstMove=!0,this.elements=t,this.options=e,this._updateListenerOptions()}get listenerOptions(){return this._listenerOptions}set listenerOptions(t){this._listenerOptions=t}_updateListenerOptions(){this.listenerOptions={passive:this.options.passiveListeners}}get raf(){return this._raf}set raf(t){this._raf=t}get timer(){return this._timer}set timer(t){this._timer=t}get animation(){return this._animation}set animation(t){this._animation=t}_updateAnimation(){this.animation=gsap.timeline()}get options(){return this._options}set options(t){this._options=t}get firstMove(){return this._firstMove}set firstMove(t){this._firstMove=t}get follower(){return this._follower}set follower(t){this._follower=t}_updateFollower(){const t=1-Math.pow(1-this.options.trailing,gsap.ticker.deltaRatio());this.follower.x+=(this.mouse.x-this.follower.x)*t,this.follower.y+=(this.mouse.y-this.follower.y)*t}get elements(){return this._elements}set elements(t){this._elements=t}get mouse(){return this._mouse}set mouse(t){this._mouse=t}updateMouse(t){this.mouse={x:"number"==typeof this.mouseMagnetic.x?this.mouseMagnetic.x:t.clientX,y:"number"==typeof this.mouseMagnetic.y?this.mouseMagnetic.y:t.clientY}}get mouseMagnetic(){return this._mouseMagnetic}set mouseMagnetic(t){this._mouseMagnetic=t}get setter(){return this._setter}set setter(t){this._setter=t}get velocity(){return this._velocity}set velocity(t){this._velocity=t}_updateVelocity(){this.velocity={x:this.mouse.x-this.follower.x,y:this.mouse.y-this.follower.y}}init(){return new Promise((t=>{this._centerFollower().then((()=>this._updateSetter())).then((()=>{this._updateAnimation(),this._attachEvents(),t(!0)}))}))}destroy(){return new Promise((t=>{this._detachEvents(),this._scaleCursor(!1),t(!0)}))}_attachEvents(){document.body.addEventListener("mousemove",this._handlers.mouseMove,this.listenerOptions),document.body.addEventListener("mousemove",this._handlers.mouseMoveOnce,{once:!0,passive:!0})}_detachEvents(){document.removeEventListener("mousemove",this._handlers.mouseMove,this.listenerOptions),document.removeEventListener("mousemove",this._handlers.mouseMoveOnce,this.listenerOptions)}_centerFollower(){return new Promise((t=>{const e=window.innerWidth/2,s=window.innerHeight/2;this.options.useCSSVars||gsap.set(this.elements.cursor,{xPercent:-50,yPercent:-50}),Object.assign(this.follower,{x:e,y:s}),Object.assign(this.mouse,{x:e,y:s}),t(!0)}))}_updateSetter(){return new Promise((t=>{this.setter={cursor:gsap.quickSetter(this.elements.cursor,"css"),wrapper:gsap.quickSetter(this.elements.wrapper,"css")},t(!0)}))}_onMouseMove(t){this.updateMouse(t),this.isAnimating?this._setTimerToCancelAnimationLoop():(this.isAnimating=!0,this._setAnimationLoop())}_setTimerToCancelAnimationLoop(t=1e3){clearTimeout(this.timer),this.timer=setTimeout((()=>{this.isAnimating=!1,this._cancelAnimationLoop()}),t)}_onMouseMoveOnce(){this._scaleCursor(),this.firstMove=!1}_onAnimationFrame(){this._updateFollower(),this._updateVelocity(),this._updateCursorPosition(),this.options.useGSAPRaf||(this.raf=requestAnimationFrame(this._handlers.animationFrame))}_setAnimationLoop(){this.options.useGSAPRaf?gsap.ticker.add(this._handlers.animationFrame):this.raf=requestAnimationFrame(this._handlers.animationFrame)}_cancelAnimationLoop(){this.options.useGSAPRaf?gsap.ticker.remove(this._handlers.animationFrame):cancelAnimationFrame(this.raf)}_scaleCursor(t=!0){this.animation.clear(),this.options.useCSSVars?this.animation.to(this.elements.cursor,{"--scaleX":t?1:0,"--scaleY":t?1:0,duration:.3}):this.animation.to(this.elements.cursor,{scaleX:t?1:0,scaleY:t?1:0,duration:.3})}get isAnimating(){return this._isAnimating}set isAnimating(t){this._isAnimating=t}_updateCursorPosition(){const t={};this.options.useCSSVars?Object.assign(t,{"--translateX":this.follower.x+"px","--translateY":this.follower.y+"px"}):Object.assign(t,{x:this.follower.x,y:this.follower.y}),"number"!=typeof this.options.elastic||this.firstMove||this._transformElasticEffect(t),this.setter.cursor(t)}_transformElasticEffect(t){const e={};this._addElasticVars(t),this.options.useCSSVars&&"--rotate"in t?(Object.assign(e,{"--rotate":-t["--rotate"]+"deg"}),Object.assign(t,{"--rotate":t["--rotate"]+"deg"})):"rotation"in t&&Object.assign(e,{rotation:-t.rotation}),this.setter.wrapper(e)}_addElasticVars(t){const e=this.options.elastic,s=Math.sqrt(Math.pow(this.velocity.x,2)+Math.pow(this.velocity.y,2)),i=Math.min(.001*s,.1)*e,n=180*Math.atan2(this.velocity.y,this.velocity.x)/Math.PI;return this.options.useCSSVars?Object.assign(t,{"--rotate":n,"--scaleX":1+i,"--scaleY":1-i}):Object.assign(t,{rotation:n,scaleX:1+i,scaleY:1-i}),t}}function a({enable:t=!1,elements:e,options:s,timeline:i}){i.clear(),i.set(e.cursor,{clearProps:"color"}).set(e.follower,{clearProps:"boxShadow,backgroundColor,color"}),t?s.useCSSVars?i.to(e.follower,{"--scale":0}).to(e.loading,{scale:1},"<"):i.to(e.follower,{scale:0}).to(e.loading,{scale:1},"<"):s.useCSSVars?i.to(e.follower,{"--scale":1}).to(e.loading,{scale:0},"<"):i.to(e.follower,{scale:1}).to(e.loading,{scale:0},"<")}function l({enable:t,element:e,options:s,strength:i=1,movement:n,rect:o,event:r,timeline:a}){if(a.clear(),t&&o){const{top:t,left:l,width:h,height:c}=o,u=l+h/2,m=t+c/2,d=Math.floor(u-r.clientX)/-2*i,p=Math.floor(m-r.clientY)/-2*i,g=u+d,_=m+p;n.mouseMagnetic={x:g,y:_},a.to(e,{duration:s.animationDuration,x:d,y:p,ease:"power3.out"})}else n.mouseMagnetic={x:!1,y:!1},a.to(e,{duration:s.animationDuration,y:"",x:"",ease:"back.out(1.7)",overwrite:!0})}function h({enable:t=!1,elements:e,options:s,originalStyles:i,timeline:n}){const o={duration:s.animationDuration,ease:"back.out(1.7)",overwrite:!0};if(n.clear(),t&&s.highlight){const t=Math.max(i.width,i.height);let e=1,n=1;"scale"in s.highlight&&(e="string"==typeof s.highlight.scale?parseFloat(s.highlight.scale)/t:s.highlight.scale),"opacity"in s.highlight&&(n=s.highlight.opacity),Object.assign(o,{backgroundColor:i.color||i.background,opacity:n,scale:e})}else Object.assign(o,{backgroundColor:"",opacity:"",scale:""});s.useCSSVars&&"scale"in o&&(Object.assign(o,{"--scale":o.scale}),delete o.scale),n.to(e.follower,o,"start")}function c({enable:t=!1,elements:e,options:s,label:i,timeline:n}){n.clear(),t?n.fromTo(e.toggleLabel,{autoAlpha:0,y:10,innerHTML:i.replace("\\n","<br>"),immediateRender:!0},{duration:s.animationDuration,autoAlpha:1,y:0,overwrite:!0},"start"):n.to(e.toggleLabel,{duration:s.animationDuration,autoAlpha:0,y:-10,innerHTML:""},"start")}function u({elements:t,options:e,rect:s,scale:i=1,scalePressed:n,color:o,background:r,originalStyles:a,timeline:l}){if(l.clear(),s){const t=Math.max(a.width,a.height);"current"===i?i=(Math.max(s.width,s.height)/t).toFixed(2):"string"==typeof i&&(i=(parseFloat(i)/t).toFixed(2))}const h=a.boxShadow,c={duration:e.animationDuration,scale:i,ease:"power3.out",background:r,overwrite:!0,opacity:""};if(h&&"none"!==h){let t="",e=h.match(/(?:[^\s\(\)]+|\([^\(\)]*\))+/g);e&&e.length&&e.forEach(((e,s)=>{4===s&&(e=parseFloat(e)/parseFloat(i.toString())+"px"),0===s&&(o||r)&&(o&&r||!o&&r?e=r:o&&!r&&(e=o)),t+=`${e} `})),Object.assign(c,{boxShadow:t})}if("scale"in c)if(e.useCSSVars)Object.assign(c,{"--scale":c.scale}),delete c.scale;else if("function"==typeof n){const t=c.scale;Object.assign(c,{scale:()=>t*n()})}l.to(t.cursor,{color:o,duration:e.animationDuration},"start"),l.to(t.follower,c,"start")}function m({enable:t=!1,options:e,toggleClassElement:s,className:i,timeline:n}){i&&(n.clear(),t?n.fromTo(s,{autoAlpha:0,y:10},{onStart:()=>{s.classList.add(...i.split(" "))},duration:e.animationDuration,autoAlpha:1,y:0},"start"):n.to(s,{onStart:()=>{s.classList.remove(...i.split(" "))},duration:e.animationDuration,autoAlpha:0,y:-10},"start"))}function d(t,e=!1){t.classList.toggle("cursor-none",e)}function p({enable:t=!1,options:e,direction:s="all",distance:i=60,elements:n,timeline:o}){if(o.clear(),t)switch(s){case"horizontal":o.to(n.arrowLeft,{duration:e.animationDuration,autoAlpha:1,x:-i,overwrite:!0},"start"),o.to(n.arrowRight,{duration:e.animationDuration,autoAlpha:1,x:i,overwrite:!0},"start");break;case"vertical":o.to(n.arrowUp,{duration:e.animationDuration,autoAlpha:1,y:-i,overwrite:!0},"start"),o.to(n.arrowDown,{duration:e.animationDuration,autoAlpha:1,y:i,overwrite:!0},"start");break;default:o.to(n.arrowUp,{duration:e.animationDuration,autoAlpha:1,y:-i,overwrite:!0},"start"),o.to(n.arrowRight,{duration:e.animationDuration,autoAlpha:1,x:i,overwrite:!0},"start"),o.to(n.arrowDown,{duration:e.animationDuration,autoAlpha:1,y:i,overwrite:!0},"start"),o.to(n.arrowLeft,{duration:e.animationDuration,autoAlpha:1,x:-i,overwrite:!0},"start")}else o.to([n.arrowUp,n.arrowRight,n.arrowDown,n.arrowLeft],{duration:e.animationDuration,autoAlpha:0,x:0,y:0,overwrite:!0},"start")}class g{constructor({movement:t,options:e,elements:s,originalStyles:i}){this._haveEffectsApplied=!0,this._readingRect=!1,this._options={},this._handlers={mouseMove:this._onMouseMove.bind(this),mouseEnter:this._onMouseEnter.bind(this),mouseLeave:this._onMouseLeave.bind(this),release:this._onRelease.bind(this),press:this._onPress.bind(this)},this._currentItem={element:null,rect:null,options:{}},this._isLoading=!1,this._autoReset=!0,this._isPressed=!1,this._pressedAnimation=gsap.timeline(),this._pressed={scale:1},this.elements=s,this.movement=t,this.options=e,this.originalStyles=i,this._updateHideNativeElement(),this._updateListenerOptions()}init(){return new Promise((t=>{this._updateAnimations(),this._attachEvents(),this._updateBoundRectObserver(),t(!0)}))}destroy(){return new Promise((t=>{this._destroyBoundRectObserver(),this._destroyAnimations(),this._detachEvents(),this.originalStyles={color:null,background:null,boxShadow:null,width:null,height:null},this.isLoading=!1,t(!0)}))}_destroyAnimations(){for(const t in this.animations)this.animations.hasOwnProperty(t)&&this.animations[t].kill()}get haveEffectsApplied(){return this._haveEffectsApplied}set haveEffectsApplied(t){this._haveEffectsApplied=t}set(t){Object.assign(this.currentItem,{options:t}),t&&"autoReset"in t&&"boolean"==typeof t.autoReset&&(this.autoReset=t.autoReset),this._applyEffects()}reset(){this._resetEffects()}setLoading(t){if(t){if(this.isLoading)return;this.isLoading=!0,this._detachEvents(),a({enable:!0,elements:this.elements,options:this.options,timeline:this.animations.setLoading})}else{if(!this.isLoading)return;a({enable:!1,elements:this.elements,options:this.options,timeline:this.animations.setLoading}),this._attachEvents(),this.isLoading=!1}}get rectReady(){return this._rectReady}set rectReady(t){this._rectReady=t}set animations(t){this._animations=t}get animations(){return this._animations}_updateAnimations(){this.animations={highlightCursor:gsap.timeline(),magnetElement:gsap.timeline(),setArrows:gsap.timeline(),setCursorClass:gsap.timeline(),setCursorLabel:gsap.timeline(),setCursorStyle:gsap.timeline(),setLoading:gsap.timeline()}}get readingRect(){return this._readingRect}set readingRect(t){this._readingRect=t}get hideNativeElement(){return this._hideNativeElement}set hideNativeElement(t){this._hideNativeElement=t}_updateHideNativeElement(){this.hideNativeElement=n.getElementByStringSelector(this.options.hideNativeElementSelector)}get autoReset(){return this._autoReset}set autoReset(t){this._autoReset=t}get isLoading(){return this._isLoading}set isLoading(t){this._isLoading=t}get isPressed(){return this._isPressed}set isPressed(t){this._isPressed=t,"number"==typeof this.options.clickScale&&this._updateAnimation()}get pressed(){return this._pressed}set pressed(t){this._pressed=t}get pressedAnimation(){return this._pressedAnimation}set pressedAnimation(t){this._pressedAnimation=t}_updateAnimation(){if("number"==typeof this.options.clickScale){const t={duration:this.options.animationDuration,scale:this.isPressed?this.options.clickScale:1,overwrite:!0};this.options.useCSSVars?Object.assign(t,{onUpdate:()=>gsap.set(this.elements.follower,{"--scale-pressed":this.pressed.scale})}):Object.assign(t,{onUpdate:()=>gsap.set(this.elements.follower,{scale:this.pressed.scale})}),this.pressedAnimation.clear().to(this.pressed,t)}}get listenerOptions(){return this._listenerOptions}set listenerOptions(t){this._listenerOptions=t}_updateListenerOptions(){this.listenerOptions={capture:!0,passive:this.options.passiveListeners}}get options(){return this._options}set options(t){this._options=t}get elements(){return this._elements}set elements(t){this._elements=t}get movement(){return this._movement}set movement(t){this._movement=t}get originalStyles(){return this._originalStyles}set originalStyles(t){this._originalStyles=t}get currentItem(){return this._currentItem}set currentItem(t){this._currentItem=t}_attachEvents(){document.body.addEventListener("mousemove",this._handlers.mouseMove,this.listenerOptions),document.body.addEventListener("mouseenter",this._handlers.mouseEnter,this.listenerOptions),document.body.addEventListener("mouseleave",this._handlers.mouseLeave,this.listenerOptions),document.body.addEventListener("mouseleave",this._handlers.release,this.listenerOptions),document.body.addEventListener("mouseup",this._handlers.release,this.listenerOptions),document.body.addEventListener("mousedown",this._handlers.press,this.listenerOptions)}_detachEvents(){document.body.removeEventListener("mousemove",this._handlers.mouseMove,this.listenerOptions),document.body.removeEventListener("mouseenter",this._handlers.mouseEnter,this.listenerOptions),document.body.removeEventListener("mouseleave",this._handlers.mouseLeave,this.listenerOptions),document.body.removeEventListener("mouseleave",this._handlers.release,this.listenerOptions),document.body.removeEventListener("mouseup",this._handlers.release,this.listenerOptions),document.body.removeEventListener("mousedown",this._handlers.press,this.listenerOptions)}_onMouseMove(t){this.isLoading||"magnetic"in this.currentItem.options&&l({enable:!0,options:this.options,strength:"number"==typeof this.currentItem.options.magnetic?this.currentItem.options.magnetic:void 0,movement:this.movement,rect:this.currentItem.rect?this.currentItem.rect:null,element:this.currentItem.element,event:t,timeline:this.animations.magnetElement})}_onMouseEnter(t){if(this.isLoading)return;const e=this._getInteractiveElementOptions(t),s=t.target;e&&("delegate"in e&&e.delegate?this.currentItem.element=s.querySelector(this.options.delegatedElementSelector)||s:this.currentItem.element=s,Object.assign(this.currentItem,{options:e})),this._shouldHighlightElement(s)&&h({enable:!0,elements:this.elements,options:this.options,originalStyles:this.originalStyles,timeline:this.animations.highlightCursor}),this._applyEffects()}_onMouseLeave(){this.isLoading||this.autoReset&&(this.rectReady&&"function"==typeof this.rectReady.then&&this.haveEffectsApplied?this.rectReady.then((()=>{this._resetEffects()})):this._resetEffects())}_onRelease(t){this.isPressed=!1}_onPress(t){this.isPressed=!0}_applyEffects(){let t=!1;"label"in this.currentItem.options&&(c({enable:!0,elements:this.elements,options:this.options,label:this.currentItem.options.label,timeline:this.animations.setCursorLabel}),t=!0),("scale"in this.currentItem.options||"color"in this.currentItem.options||"background"in this.currentItem.options)&&(this._getBoundingClientRectAsync3(this.currentItem.element).then((()=>{u({elements:this.elements,options:this.options,rect:this.currentItem.rect,scale:this.currentItem.options.scale,scalePressed:()=>this.pressed.scale,color:this.currentItem.options.color||"",background:this.currentItem.options.background||"",originalStyles:this.originalStyles,timeline:this.animations.setCursorStyle})})),t=!0),"className"in this.currentItem.options&&(m({enable:!0,options:this.options,toggleClassElement:this.elements.toggleClass,className:this.currentItem.options.className,timeline:this.animations.setCursorClass}),t=!0),this.hideNativeElement&&"hideNative"in this.currentItem.options&&(d(this.hideNativeElement,this.currentItem.options.hideNative),t=!0),"arrows"in this.currentItem.options&&(p({enable:!0,options:this.options,direction:this.currentItem.options.arrows,distance:this.currentItem.options.arrowsDistance,elements:this.elements,timeline:this.animations.setArrows}),t=!0),Object.assign(this,{haveEffectsApplied:t})}_resetEffects(){"magnetic"in this.currentItem.options?l({enable:!1,options:this.options,movement:this.movement,element:this.currentItem.element,timeline:this.animations.magnetElement}):Object.assign(this.movement,{mouseMagnetic:{x:!1,y:!1}}),h({enable:!1,elements:this.elements,options:this.options,originalStyles:this.originalStyles,timeline:this.animations.highlightCursor}),u({elements:this.elements,options:this.options,scale:1,scalePressed:()=>this.pressed.scale,color:"",background:"",rect:this.currentItem.rect,originalStyles:this.originalStyles,timeline:this.animations.setCursorStyle}),c({enable:!1,elements:this.elements,options:this.options,timeline:this.animations.setCursorLabel}),m({enable:!1,options:this.options,toggleClassElement:this.elements.toggleClass,className:this.currentItem.options.className,timeline:this.animations.setCursorClass}),this.hideNativeElement&&d(this.hideNativeElement,!1),p({enable:!1,options:this.options,elements:this.elements,timeline:this.animations.setArrows}),Object.assign(this,{currentItem:{element:null,rect:null,options:{}},haveEffectsApplied:!1})}_getInteractiveElementOptions(t){const e=t.target;return n.isHTMLElement(e)&&"artsCursorFollowerTarget"in e.dataset?o.parseOptionsStringObject(e.getAttribute("data-arts-cursor-follower-target")):!(!n.isHTMLElement(e)||"IFRAME"!==e.tagName||!this.options.hideIFramesHover)&&{scale:0}}_shouldHighlightElement(t){return!(!this.options.highlight||this._shouldNotHighlightElement(t)||"A"!==t.tagName&&!t.classList.contains(this.options.highlight.includeClass)&&!("highlight"in this.currentItem.options))}_shouldNotHighlightElement(t){return!this.options.highlight||!!t.classList.contains(this.options.highlight.excludeClass)||"artsCursorFollowerTarget"in t.dataset}get boundRectObserver(){return this._boundRectObserver}set boundRectObserver(t){this._boundRectObserver=t}_updateBoundRectObserver(){this.boundRectObserver=new IntersectionObserver((t=>{for(const e of t){const t=e.boundingClientRect;this.currentItem.rect=t,this._setRectReady()}}))}_destroyBoundRectObserver(){this.boundRectObserver&&"function"==typeof this.boundRectObserver.disconnect&&(this.boundRectObserver.disconnect(),this.boundRectObserver=null)}_setRectReady(t){}_getBoundingClientRectAsync3(t){return this.readingRect&&(this._destroyBoundRectObserver(),this._updateBoundRectObserver()),new Promise((e=>{t?(this.readingRect=!0,this.boundRectObserver.observe(t),this.rectReady=new Promise((t=>{this._setRectReady=t})),this.rectReady.then((()=>{this.boundRectObserver.unobserve(t),this.readingRect=!1,e(this.currentItem.rect)}))):e(!1)}))}_getBoundingClientRectAsync2(t){return this._boundRectObserver&&"function"==typeof this._boundRectObserver.disconnect&&(this._boundRectObserver.disconnect(),this._boundRectObserver=null),new Promise((e=>{this._boundRectObserver=new IntersectionObserver((s=>{for(const i of s){const s=i.boundingClientRect;this.currentItem.rect=s,this._boundRectObserver.unobserve(t),this._boundRectObserver.disconnect(),e(s)}})),this._boundRectObserver.observe(t)}))}_getBOundingClientRectAsync1(t){return new Promise((e=>{gsap.ticker.add((()=>{if(n.isHTMLElement(t)){const s=t.getBoundingClientRect();this.currentItem.rect=s,e(s)}else e(!1)}),!0,!1)}))}}class _{constructor({condition:t,callbackMatch:e,callbackNoMatch:s}){this._handlers={change:this._onChange.bind(this)},this.condition=t,this.callbacks={match:e,noMatch:s},(this._hasMatchFunction()||this._hasNoMatchFunction())&&this.init()}init(){this.mediaQuery=this._addMatchMedia(),this._attachEvents()}destroy(){this._detachEvents(),this.mediaQuery=null}get mediaQuery(){return this._mediaQuery}set mediaQuery(t){this._mediaQuery=t}get callbacks(){return this._callbacks}set callbacks(t){this._callbacks=t}get condition(){return this._condition}set condition(t){this._condition=t}_hasMatchFunction(){return"function"==typeof this.callbacks.match}_hasNoMatchFunction(){return"function"==typeof this.callbacks.noMatch}_addMatchMedia(){return window.matchMedia(`${this.condition}`)}_attachEvents(){"function"==typeof this.mediaQuery.addEventListener?this.mediaQuery.addEventListener("change",this._handlers.change):this.mediaQuery.addListener(this._handlers.change)}_detachEvents(){"function"==typeof this.mediaQuery.removeEventListener?this.mediaQuery.removeEventListener("change",this._handlers.change):this.mediaQuery.removeListener(this._handlers.change)}_onChange(t){t.matches?this._hasMatchFunction()&&this.callbacks.match():t.matches||this._hasNoMatchFunction()&&this.callbacks.noMatch()}}s(624),s(661);const f=class extends class{constructor({cursor:t,options:e={}}){this._enabled=!1,this._initialized=!1,this._elements={},t&&e&&(this._updateCursorElement(t),this._updateOptions(t,e),this._updateElements(),this._updateOriginalStyles())}set(t){this.initialized&&!this.interaction.isLoading&&this.interaction.set(t)}reset(){this.initialized&&!this.interaction.isLoading&&this.interaction.reset()}setLoading(t){this.initialized&&this.interaction.setLoading(t)}update(){gsap.set(this.elements.follower,{clearProps:"boxShadow"}),this._updateOriginalStyles(),Object.assign(this.interaction,{originalStyles:this.originalStyles,hideNativeElement:n.getElementByStringSelector(this.options.hideNativeElementSelector)})}get enabled(){return this._enabled}set enabled(t){this._enabled=t}get initialized(){return this._initialized}set initialized(t){this._initialized=t}get elements(){return this._elements}set elements(t){this._elements=t}_updateElements(){Object.assign(this.elements,{follower:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.followerSelector)[0],wrapper:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.wrapperSelector)[0],loading:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.loadingSelector)[0],toggleLabel:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.toggleLabelSelector)[0],toggleClass:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.toggleClassSelector)[0],arrowUp:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.arrowUpSelector)[0],arrowRight:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.arrowRightSelector)[0],arrowDown:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.arrowDownSelector)[0],arrowLeft:n.getElementsInContainer(this.elements.cursor,this.options.cursorElements.arrowLeftSelector)[0]})}_updateCursorElement(t){Object.assign(this.elements,{cursor:n.getElementByStringSelector(t)})}_cleanElements(){gsap.set(Object.values(this.elements),{clearProps:"all"}),gsap.set(document.querySelectorAll('[data-arts-cursor-follower-target*="magnetic"]'),{clearProps:"transform"})}get originalStyles(){return this._originalStyles}set originalStyles(t){this._originalStyles=t}_updateOriginalStyles(){const{offsetWidth:t,offsetHeight:e}=this.elements.cursor;this.originalStyles={color:gsap.getProperty(this.elements.follower,"color"),background:gsap.getProperty(this.elements.follower,"background"),boxShadow:gsap.getProperty(this.elements.follower,"box-shadow"),width:t,height:e}}get movement(){return this._movement}set movement(t){this._movement=t}_updateMovement(){this.movement=new r({elements:this.elements,options:this.options})}get interaction(){return this._interaction}set interaction(t){this._interaction=t}_updateInteraction(){this.interaction=new g({movement:this.movement,options:this.options,elements:this.elements,originalStyles:this.originalStyles})}get options(){return this._options}set options(t){this._options=t}_updateOptions(t,e){this.options=new o({container:t,attributeSelector:"data-arts-cursor-follower-options",options:e}).data}get matchMedia(){return this._matchMedia}set matchMedia(t){this._matchMedia=t}updateMouse(t){this.movement.updateMouse(t)}}{constructor(t=document.querySelector('[data-arts-cursor-follower="cursor"]'),e={}){super({cursor:t,options:e}),this.ready=new Promise((t=>{this.setReady=t})),this.options.init&&(this.options.matchMedia&&!window.matchMedia(`${this.options.matchMedia}`).matches?(this.matchMedia=new _({condition:this.options.matchMedia,callbackMatch:this.init.bind(this)}),this._onInitReject()):this.init())}init(){this.initialized||(this.matchMedia&&this.matchMedia.destroy(),this.options.matchMedia&&(this.matchMedia=new _({condition:this.options.matchMedia,callbackMatch:this.init.bind(this),callbackNoMatch:this.destroy.bind(this)})),this._updateMovement(),this._updateInteraction(),this.movement.init().then((()=>this.interaction.init())).then((()=>{this._onInitSuccess(),this.initialized=!0,this.enabled=!0,this.setReady()})))}destroy(){this.enabled=!1,this.initialized=!1,this.interaction.destroy(),this.movement.destroy(),this._cleanElements(),this._onInitReject()}_onInitSuccess(){document.documentElement.classList.add("has-cursor-follower"),document.documentElement.classList.remove("no-cursor-follower"),this.options.useCSSVars&&this.elements.cursor.classList.add("uses-css-vars")}_onInitReject(){document.documentElement.classList.add("no-cursor-follower"),document.documentElement.classList.remove("has-cursor-follower"),this.options.useCSSVars&&this.elements.cursor.classList.remove("uses-css-vars")}setReady(){}}})(),this.ArtsCursorFollower=i.default})();