<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$outer_footer_attributes = array(
	'class' => array(
		'footer',
		'borders-auto-opacity-solid',
		'mt-auto',
	),
	'id'    => 'page-footer',
);

$inner_footer_attributes = array(
	'class' => array(
		'footer__area',
		'bt-auto',
	),
);

$container_footer_attributes = array(
	'class' => array(),
);

$row_footer_attributes = array(
	'class' => array(
		'row',
		'justify-content-center',
		'align-items-center',
		'footer__row-pb-small',
	),
);

$col_footer_attributes = array(
	'class' => array(
		'col-12',
		'footer__column-pt-small',
		'text-lg-start',
		'text-center',
		'd-flex',
		'flex-wrap',
		'justify-content-between',
	),
);

if ( Utilities::is_woocommerce() || Utilities::is_checkout() || Utilities::is_cart() || Utilities::is_account_page() ) {
	$container_footer_attributes['class'][] = 'container-fluid-gutters';
} else {
	$container_footer_attributes['class'][] = 'container';
	$col_footer_attributes['class'][]       = 'col-lg-8';
}

$outer_footer_attributes     = apply_filters( 'arts/page_footer/outer/attributes', $outer_footer_attributes );
$inner_footer_attributes     = apply_filters( 'arts/page_footer/inner/attributes', $inner_footer_attributes );
$container_footer_attributes = apply_filters( 'arts/page_footer/container/attributes', $container_footer_attributes );
$row_footer_attributes       = apply_filters( 'arts/page_footer/row/attributes', $row_footer_attributes );
$col_footer_attributes       = apply_filters( 'arts/page_footer/column/attributes', $col_footer_attributes );

?>

<footer <?php Utilities::print_attributes( $outer_footer_attributes ); ?>>
	<div <?php Utilities::print_attributes( $inner_footer_attributes ); ?>>
		<div <?php Utilities::print_attributes( $container_footer_attributes ); ?>>
			<div <?php Utilities::print_attributes( $row_footer_attributes ); ?>>
				<div <?php Utilities::print_attributes( $col_footer_attributes ); ?>>
					<div class="col-auto">
						<small><?php echo esc_html__( 'Powered by Asli WordPress theme.', 'asli' ); ?></small>
					</div>
					<div class="col-auto">
						<small><?php echo esc_html( '© ' . date( 'Y' ) ); ?></small>
					</div>
				</div>
			</div>
		</div>
	</div>
</footer>
