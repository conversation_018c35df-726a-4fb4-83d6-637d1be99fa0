<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'comment_form_default_fields', 'arts_comment_form_default_fields' );
if ( ! function_exists( 'arts_comment_form_default_fields' ) ) {
	/**
	 * Set custom fields (author, email, url) for the comment form.
	 *
	 * @param array $args    Optional. An array of arguments for the comment form fields.
	 * @param int   $post_id Optional. The post ID for which the comment form is being generated.
	 *
	 * @return array The default comment form fields.
	 */
	function arts_comment_form_default_fields( $args = array(), $post_id = null ) {
		if ( null === $post_id ) {
			$post_id = get_the_ID();
		}

		// Exit the function when comments for the post are closed.
		if ( ! comments_open( $post_id ) ) {
			/**
			 * Fires after the comment form if comments are closed.
			 *
			 * @since 3.0.0
			 */
			do_action( 'comment_form_comments_closed' );

			return;
		}

		$commenter = wp_get_current_commenter();

		$args = wp_parse_args( $args );

		if ( ! isset( $args['format'] ) ) {
			$args['format'] = current_theme_supports( 'html5', 'comment-form' ) ? 'html5' : 'xhtml';
		}

		$req      = get_option( 'require_name_email' );
		$html_req = ( $req ? " required='required'" : '' );
		$html5    = 'html5' === $args['format'];

		$fields = array(
			'author' => '
				<div class="row form__row">
					<div class="col form__col">
						<label class="input-float">
							<input class="input-float__input" id="author" name="author" type="text" value="' . esc_attr( $commenter['comment_author'] ) . '" size="30" maxlength="245"' . $html_req . ' data-pristine-required-message="' . esc_html__( 'Please enter your name', 'asli' ) . '"/>' .
								'<span class="input-float__label">' . esc_html__( 'Name', 'asli' ) . ( $req ? ' <span class="required">*</span>' : '' ) . '</span><span class="form__error"></span>
						</label>
					</div>
				</div>
			',
			'email'  => '
				<div class="row form__row">
					<div class="col form__col">
						<label class="input-float">
							<input class="input-float__input" id="email" name="email" ' . ( $html5 ? 'type="email"' : 'type="text"' ) . ' value="' . esc_attr( $commenter['comment_author_email'] ) . '" size="30" maxlength="100" aria-describedby="email-notes"' . $html_req . ' data-pristine-required-message="' . esc_html__( 'Please enter your e-mail', 'asli' ) . '" data-pristine-email-message="' . esc_html__( 'Please enter a valid e-mail', 'asli' ) . '"/>' .
								'<span class="input-float__label">' . esc_html__( 'Email', 'asli' ) . ( $req ? ' <span class="required">*</span>' : '' ) . '</span><span class="form__error"></span>
						</label>
					</div>
				</div>
			',
			'url'    => '
				<div class="row form__row">
					<div class="col form__col">
						<label class="input-float">
							<input class="input-float__input" id="url" name="url" ' . ( $html5 ? 'type="url"' : 'type="text"' ) . ' value="' . esc_attr( $commenter['comment_author_url'] ) . '" size="30" maxlength="200" data-pristine-url-message="' . esc_html__( 'Please enter a valid URL', 'asli' ) . '"/>' .
								'<span class="input-float__label">' . esc_html__( 'Website', 'asli' ) . '</span><span class="form__error"></span>
						</label>
					</div>
				</div>
			',
		);

		return $fields;
	}
}
