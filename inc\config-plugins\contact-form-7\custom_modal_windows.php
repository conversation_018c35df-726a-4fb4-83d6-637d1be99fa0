<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_action( 'wp_enqueue_scripts', 'arts_enqueue_cf_7_styles', 20 );
if ( ! function_exists( 'arts_enqueue_cf_7_styles' ) ) {
	/**
	 * If custom modals are enabled, hides the default Contact Form 7
	 * response boxes by adding inline styles.
	 *
	 * @return void
	 */
	function arts_enqueue_cf_7_styles() {
		$cf_7_modals_enabled = Utilities::get_kit_settings( 'cf_7_modals_enabled', true );

		// hide default Contact Form 7 response boxes if custom modals are enabled
		if ( $cf_7_modals_enabled ) {
			$styles_string = wp_strip_all_tags( '.wpcf7-mail-sent-ok, .wpcf7 form.sent .wpcf7-response-output, .wpcf7-mail-sent-ng, .wpcf7 form.failed .wpcf7-response-output, .wpcf7 form.unaccepted .wpcf7-response-output, .wpcf7 form.invalid .wpcf7-response-output { display: none !important; }' );

			wp_add_inline_style( 'contact-form-7', $styles_string );
		}
	}
}
