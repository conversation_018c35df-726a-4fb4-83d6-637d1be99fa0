<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'comment_form_defaults', 'arts_comment_form_defaults' );
if ( ! function_exists( 'arts_comment_form_defaults' ) ) {
	/**
	 * Set custom textarea & submit button for the comment form.
	 *
	 * @return array Default arguments for the comment form.
	 */
	function arts_comment_form_defaults() {
		/**
		 * Filters whether an empty comment should be allowed.
		 *
		 * @since 5.1.0
		 *
		 * @param bool  $allow_empty_comment Whether to allow empty comments. Default false.
		 * @param array $commentdata         Array of comment data to be sent to wp_insert_comment().
		 */
		$allow_empty_comment = apply_filters( 'allow_empty_comment', false );
		$html_req            = ( $allow_empty_comment ? '' : " required='required'" );

		$args = array(
			'comment_field' =>
				'
				<div class="row form__row">
					<div class="col form__col">
						<label class="input-float">
							<textarea id="comment" name="comment" class="input-float__input input-float__input_textarea" cols="45" rows="8" maxlength="65525"' . $html_req . ' data-pristine-required-message="' . esc_html__( 'Please type your comment text', 'asli' ) . '"></textarea><span class="input-float__label">' . esc_html_x( 'Comment', 'noun', 'asli' ) . ( $allow_empty_comment ? '' : ' <span class="required">*</span>' ) . '</span><span class="form__error"></span>
						</label>
					</div>
				</div>
			',
			'class_submit'  => 'button button_icon cursor-highlight',
			'submit_button' =>
				'
				<button name="%1$s" type="submit" id="%2$s" class="%3$s">
					<span class="button__label button__label-normal">
						<span class="button__title">%4$s</span>
						<span class="button__icon button__icon_after material-icons keyboard_arrow_right"></span>
					</span>
					<span class="button__label button__label-hover">
						<span class="button__title">%4$s</span>
						<span class="button__icon button__icon_after material-icons keyboard_arrow_right"></span>
					</span>
				</button>
				',
			'submit_field'  =>
				'
				<div class="row form__row form__row_submit text-end">
					<div class="col form__col form__col_submit">
						%1$s %2$s
					</div>
				</div>
			',
		);

		return $args;
	}
}
