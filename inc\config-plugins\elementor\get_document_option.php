<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_elementor_get_document_option' ) ) {
	/**
	 * Retrieve a specific document option for a given post.
	 *
	 * @param string   $option_name    The name of the option to retrieve.
	 * @param mixed    $option_default The value to return if the option is not found. Defaults to an empty string.
	 * @param int|null $post_id        The ID of the post. Defaults to null.
	 *
	 * @return mixed The value of the option, or the fallback value if not found.
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::get_document_option()` method instead.
	 */
	function arts_elementor_get_document_option( $option, $option_default = false, $post_id = null ) {
		return Utilities::get_document_option( $option, $post_id, $option_default );
	}
}

if ( ! function_exists( 'arts_get_document_option' ) ) {
	/**
	 * Retrieve a specific document option for a given post.
	 *
	 * @param string   $option_name    The name of the option to retrieve.
	 * @param mixed    $option_default The value to return if the option is not found. Defaults to an empty string.
	 * @param int|null $post_id        The ID of the post. Defaults to null.
	 *
	 * @return mixed The value of the option, or the fallback value if not found.
	 *
	 * @deprecated 2.0.0 Use `\Arts\Utilities\Utilities::get_document_option()` method instead.
	 */
	function arts_get_document_option( $option_name, $option_default = false, $post_id = null ) {
		if ( did_action( 'elementor/loaded' ) ) {
			return Utilities::get_document_option( $option_name, $post_id, $option_default );
		} else {
			return $option_default;
		}
	}
}
