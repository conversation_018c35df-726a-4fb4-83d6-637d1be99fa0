const _0x3a9db2=_0x22ca;function _0x15f2(){const _0x5e5058=['delimiter','lanes','element','onScrollSpeed','24594534ugFeAp','addMarqueeDelimiter','_clamp','pluginsReady','_onAfterResize','getById','onHoverSpeed','init','.js-marquee-header__list-lane','controller','2467008RzcojJ','marquee','options','function','4788884WZXbOP','setNormalSpeed','2813076PUStWy','all','labels','.js-marquee-header__list-item','956578mXPthJ','738aKkZcd','.js-marquee-header__label','&nbsp;&nbsp;/&nbsp;&nbsp;','velocity','_handlers','utilities','finally','infiniteList','5211008GmvPqB','velocityWatcher','onUpdate','push','7FhnbMG','dataReady','16670SHaAwn','afterResize','updateLazy','loop','bind','speed','onScrollInverseDirection','innerSelectors','off','_createInfiniteList','clamp','plugins','direction','none','_attachEvents','items','_detachEvents','destroy'];_0x15f2=function(){return _0x5e5058;};return _0x15f2();}(function(_0x5a4507,_0x5d2426){const _0x48db4a=_0x22ca,_0x15fe3f=_0x5a4507();while(!![]){try{const _0x2cb778=-parseInt(_0x48db4a(0xd7))/0x1+parseInt(_0x48db4a(0xd3))/0x2+-parseInt(_0x48db4a(0xcd))/0x3+-parseInt(_0x48db4a(0xd1))/0x4+-parseInt(_0x48db4a(0xe6))/0x5*(-parseInt(_0x48db4a(0xd8))/0x6)+-parseInt(_0x48db4a(0xe4))/0x7*(parseInt(_0x48db4a(0xe0))/0x8)+parseInt(_0x48db4a(0xfc))/0x9;if(_0x2cb778===_0x5d2426)break;else _0x15fe3f['push'](_0x15fe3f['shift']());}catch(_0x17d5af){_0x15fe3f['push'](_0x15fe3f['shift']());}}}(_0x15f2,0xe10eb));function _0x22ca(_0x401302,_0x311715){const _0x15f2ac=_0x15f2();return _0x22ca=function(_0x22ca4a,_0x19cb28){_0x22ca4a=_0x22ca4a-0xca;let _0x1cd778=_0x15f2ac[_0x22ca4a];return _0x1cd778;},_0x22ca(_0x401302,_0x311715);}export default class MarqueeHeader extends BaseComponent{constructor({name:_0x274cbc,loadInnerComponents:_0x320697,loadAfterSyncStyles:_0x354c58,parent:_0x1cb278,element:_0x3e23ba}){const _0x45c45d=_0x22ca;super({'name':_0x274cbc,'loadInnerComponents':_0x320697,'loadAfterSyncStyles':_0x354c58,'parent':_0x1cb278,'element':_0x3e23ba,'defaults':{'marquee':!![],'loop':!![],'speed':0.5,'onHoverSpeed':0.1,'onScrollSpeed':![],'onScrollInverseDirection':![],'delimiter':_0x45c45d(0xda)},'innerElements':{'lanes':_0x45c45d(0xcb),'items':_0x45c45d(0xd6),'labels':_0x45c45d(0xd9)}}),this['_handlers']={'afterResize':this[_0x45c45d(0x100)][_0x45c45d(0xea)](this)},this[_0x45c45d(0xe5)][_0x45c45d(0xde)](()=>{const _0x5d71b9=_0x45c45d;this[_0x5d71b9(0xfe)]=gsap['utils'][_0x5d71b9(0xf0)](-0xa,0xa),this['setup']();});}[_0x3a9db2(0xca)](){return new Promise(_0x217e44=>{const _0x57aa99=_0x22ca;app['utilities'][_0x57aa99(0xfd)](this['options'][_0x57aa99(0xf8)],this['elements'][_0x57aa99(0xd5)])[_0x57aa99(0xde)](()=>{const _0x1f2be6=_0x57aa99;this['_createInfiniteList'](),this[_0x1f2be6(0xdf)]?this[_0x1f2be6(0xdf)][_0x1f2be6(0xff)][_0x1f2be6(0xde)](()=>{this['_attachEvents'](),_0x217e44(!![]);}):_0x217e44(!![]);});});}[_0x3a9db2(0xf7)](){return new Promise(_0x473e33=>{const _0x5399e1=_0x22ca,_0x3ebac4=[];this[_0x5399e1(0xf6)]();if(this[_0x5399e1(0xdf)]&&typeof this['infiniteList'][_0x5399e1(0xf7)]===_0x5399e1(0xd0)){const _0x428ecc=scheduler['postTask'](()=>{const _0x558b65=_0x5399e1;this['infiniteList'][_0x558b65(0xf7)]();});_0x3ebac4[_0x5399e1(0xe3)](_0x428ecc);}Promise[_0x5399e1(0xd4)](_0x3ebac4)['finally'](()=>_0x473e33(!![]));});}['getScrubAnimation'](){const _0x1e845d=_0x3a9db2;if(typeof this['options'][_0x1e845d(0xfb)]==='number'){const _0x43fe00={'velocity':this[_0x1e845d(0xcf)]['speed']},_0x3e590a=ScrollTrigger[_0x1e845d(0x101)](_0x1e845d(0xe1)),_0x429b47={'trigger':this[_0x1e845d(0xfa)],'once':![],'invalidateOnRefresh':!![]};return _0x3e590a&&(_0x429b47[_0x1e845d(0xe2)]=_0x4df8c0=>{const _0x3c1320=_0x1e845d;let _0x275ee5=this[_0x3c1320(0xfe)](Math['abs'](_0x3e590a['getVelocity']())/0x12c)*this['options']['onScrollSpeed'];_0x275ee5>_0x43fe00[_0x3c1320(0xdb)]&&(_0x43fe00[_0x3c1320(0xdb)]=_0x275ee5*(this[_0x3c1320(0xcf)][_0x3c1320(0xec)]?_0x4df8c0[_0x3c1320(0xf2)]:0x1),gsap['to'](_0x43fe00,{'velocity':this[_0x3c1320(0xcf)][_0x3c1320(0xeb)],'duration':0.6,'ease':_0x3c1320(0xf3),'overwrite':!![],'onUpdate':()=>{const _0x2b60e6=_0x3c1320;this['infiniteList']&&_0x2b60e6(0xce)in this[_0x2b60e6(0xdf)][_0x2b60e6(0xf1)]&&this['infiniteList']['plugins'][_0x2b60e6(0xce)][_0x2b60e6(0xd2)](_0x43fe00[_0x2b60e6(0xdb)]);}}));}),_0x429b47;}}[_0x3a9db2(0xf4)](){const _0x2f25ec=_0x3a9db2;this[_0x2f25ec(0xdf)][_0x2f25ec(0xcc)]['on'](_0x2f25ec(0xe7),this[_0x2f25ec(0xdc)][_0x2f25ec(0xe7)]);}[_0x3a9db2(0xf6)](){const _0x2900cc=_0x3a9db2;this[_0x2900cc(0xdf)][_0x2900cc(0xcc)][_0x2900cc(0xee)]('afterResize',this[_0x2900cc(0xdc)][_0x2900cc(0xe7)]);}[_0x3a9db2(0xef)](){const _0x356a4e=_0x3a9db2;this[_0x356a4e(0xdf)]=new ArtsInfiniteList(this[_0x356a4e(0xfa)],{'direction':'horizontal','listElementsSelector':this[_0x356a4e(0xed)][_0x356a4e(0xf5)],'mapWheelEventYtoX':![],'multiLane':{'laneSelector':this['innerSelectors'][_0x356a4e(0xf9)],'laneOptionsAttribute':'data-arts-infinite-list-options'},'autoClone':this['options']['loop'],'loop':this[_0x356a4e(0xcf)][_0x356a4e(0xe9)],'plugins':{'marquee':!!this['options'][_0x356a4e(0xce)]?{'speed':this[_0x356a4e(0xcf)]['speed'],'onHoverSpeed':this[_0x356a4e(0xcf)][_0x356a4e(0x102)]}:![],'scroll':![]}});}[_0x3a9db2(0x100)](){const _0x1d2c12=_0x3a9db2;app[_0x1d2c12(0xdd)][_0x1d2c12(0xe8)]();}}