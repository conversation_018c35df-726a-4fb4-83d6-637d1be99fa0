<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_ajax_update_head_nodes' ) ) {
	/**
	 * Retrieves the head nodes selectors that should be updated
	 * during AJAX transitions.
	 *
	 * @return string Processed head nodes setting.
	 */
	function arts_get_ajax_update_head_nodes() {
		$ajax_update_head_nodes = rtrim( Utilities::get_kit_settings( 'ajax_update_head_nodes', '' ), ',' ); // remove all commas off the string

		return preg_replace( '/\s+/S', ' ', $ajax_update_head_nodes );
	}
}
