function _0x262d(){const _0x4b0738=['toggleScene','filter','load','AutoScrollNextWebGL','top-=','completeClass','no-overscroll','none','setup','isElementorEditor','_webGLLoader','_getProgressScene','AJAX','removeEventListener','_detachEvents','fromTo','string','element','marginTop','animateProgress','componentsManager','curtains','_onToggleScene','updateRef','utilities','clickHeader','isActive','getScrubAnimation','scrollLock','update','_toggleScrollingClass','_onClickHeader','bottom+=100%\x20bottom+=','_onProgressScene','Header','_createFixedScene','mediaWrapper','_toggleCompleteClass','origin','9951170CWkfke','create','end','postTask','scaleTexture','default','.js-auto-scroll-next__scroll-down-wrapper','toggleHidden','.js-auto-scroll-next__next-header','offsetHeight','push','function','97764IszgLH','bottom+=100%\x20bottom','documentElement','_getSceneTargetScroll','finally','bind','.js-auto-scroll-next__canvas-wrapper','_createPrefetchScene','_transitionActive','_isWebGLEnabled','headerRef','debounce','mount','3aIhrez','media','timeline','_attachEvents','ready','fixedSceneSafeMargin','665LBJmVa','51455371oFQpJR','fonts','prefetchURLResources','_getWebGLLoader','start','2811752FGlZwa','Mask','destroy','scrollingClass','shoudLoadAJAX','center\x20center','elements','90477qiLWVc','85ranqrX','number','scheduleLateTask','init','matchMedia','getAttribute','toggle','toggleHeaderVisibility','click','_onResize','_updateMask','setLoading','onSceneProgress','_onTransitionStart','then','auto-scroll-next_complete','scrollTo','_handlers','running','pageLock','progressScene','_initAnimations','prefetchScene','catch','209648gLnHrG','resize','top-=2000px\x20bottom','_webGLEnabled','length','.js-auto-scroll-next__next-header\x20a','progressLine','fixedScene','onSceneIdle','kill','-20%','dataReady','options','scrollY','auto-scroll-next_scrolling','classList','nextLinks','href','_setRefs','nextHeader','maskRef','1425643oErfQC','_setReady','scalePlane','addEventListener','getComponentByName','scrollDownWrapper','.js-auto-scroll-next__media','536bIDXpw'];_0x262d=function(){return _0x4b0738;};return _0x262d();}const _0x40e320=_0x1ade;function _0x1ade(_0x5c9f14,_0x2c2649){const _0x262dcc=_0x262d();return _0x1ade=function(_0x1adec6,_0x3faed9){_0x1adec6=_0x1adec6-0x64;let _0x19bd7b=_0x262dcc[_0x1adec6];return _0x19bd7b;},_0x1ade(_0x5c9f14,_0x2c2649);}(function(_0x2934dd,_0x104045){const _0x3e02f8=_0x1ade,_0x4dba79=_0x2934dd();while(!![]){try{const _0x3c39d4=-parseInt(_0x3e02f8(0x91))/0x1+parseInt(_0x3e02f8(0xe5))/0x2*(-parseInt(_0x3e02f8(0xd9))/0x3)+-parseInt(_0x3e02f8(0x7c))/0x4*(-parseInt(_0x3e02f8(0x64))/0x5)+parseInt(_0x3e02f8(0xcc))/0x6*(-parseInt(_0x3e02f8(0xdf))/0x7)+parseInt(_0x3e02f8(0x98))/0x8*(parseInt(_0x3e02f8(0xec))/0x9)+-parseInt(_0x3e02f8(0xc0))/0xa+parseInt(_0x3e02f8(0xe0))/0xb;if(_0x3c39d4===_0x104045)break;else _0x4dba79['push'](_0x4dba79['shift']());}catch(_0x3ea5b4){_0x4dba79['push'](_0x4dba79['shift']());}}}(_0x262d,0xd3da6));export default class AutoScrollNext extends BaseComponent{constructor({name:_0x40e57d,loadInnerComponents:_0x5660d5,loadAfterSyncStyles:_0x87de42,parent:_0x41fa29,element:_0x6dd4d2}){const _0x5e9b38=_0x1ade;super({'name':_0x40e57d,'loadInnerComponents':_0x5660d5,'loadAfterSyncStyles':_0x87de42,'parent':_0x41fa29,'element':_0x6dd4d2,'defaults':{'webGL':{'enabled':!![],'vertices':0x10},'onSceneProgress':{'speed':0x8,'amplitude':0x4,'segments':0x4,'scalePlane':1.1,'scaleTexture':0x1},'onSceneIdle':{'speed':0x4,'amplitude':0x2,'segments':0x4,'scalePlane':0x1,'scaleTexture':1.2},'scrollingClass':_0x5e9b38(0x8a),'completeClass':_0x5e9b38(0x73),'toggleHeaderVisibility':!![],'fixedSceneSafeMargin':0x0},'innerElements':{'canvasWrapper':_0x5e9b38(0xd2),'fixedWrapper':'.js-auto-scroll-next__fixed-wrapper','scrollDownWrapper':_0x5e9b38(0xc6),'nextHeader':_0x5e9b38(0xc8),'nextLinks':_0x5e9b38(0x81),'progressLine':'.js-auto-scroll-next__progress-line','mediaWrapper':'.js-auto-scroll-next__wrapper-media','media':_0x5e9b38(0x97)}}),this['_handlers']={'clickHeader':this[_0x5e9b38(0xb8)][_0x5e9b38(0xd1)](this),'toggleScene':this['_onToggleScene']['bind'](this),'progressScene':this[_0x5e9b38(0xba)][_0x5e9b38(0xd1)](this),'transitionStart':this['_onTransitionStart'][_0x5e9b38(0xd1)](this),'resize':app[_0x5e9b38(0xb1)][_0x5e9b38(0xd7)](this[_0x5e9b38(0x6d)]['bind'](this),app[_0x5e9b38(0xb1)]['getDebounceTime']())},this[_0x5e9b38(0x87)][_0x5e9b38(0xd0)](()=>{const _0x433578=_0x5e9b38;this['_transitionActive']=![],this[_0x433578(0x7f)]=this[_0x433578(0xd5)](),this[_0x433578(0xa3)]=this['_webGLEnabled']&&this['elements'][_0x433578(0xda)][0x0]?this[_0x433578(0xe3)]():null,this['setup']();});}[_0x40e320(0xa1)](){const _0x237e01=_0x40e320,_0x4b0d8f=app[_0x237e01(0xad)][_0x237e01(0x95)](_0x237e01(0xa5));document[_0x237e01(0xe1)][_0x237e01(0xdd)]['then'](()=>this[_0x237e01(0xd8)]())[_0x237e01(0xd0)](()=>{const _0x4070f7=_0x237e01;_0x4b0d8f&&_0x4b0d8f[_0x4070f7(0x76)]?_0x4b0d8f[_0x4070f7(0x66)](()=>new Promise(_0x44810e=>{const _0x10d879=_0x4070f7;this[_0x10d879(0x67)]()[_0x10d879(0x72)](()=>this[_0x10d879(0x79)]())[_0x10d879(0xd0)](()=>{_0x44810e(!![]);});}),_0x4070f7(0xc2)):this[_0x4070f7(0x67)]()['finally'](()=>this['_initAnimations']()),this[_0x4070f7(0x92)]();});}['init'](){return new Promise(_0x3cae01=>{const _0x2af9b6=_0x1ade;this[_0x2af9b6(0x8e)](),this[_0x2af9b6(0xbc)](),this[_0x2af9b6(0xeb)][_0x2af9b6(0x8c)][_0x2af9b6(0x80)]&&app[_0x2af9b6(0xe9)]()&&app['AJAXReady']['finally'](()=>{const _0x469b5f=_0x2af9b6;this[_0x469b5f(0xd3)]();}),this[_0x2af9b6(0xa3)]?(this[_0x2af9b6(0x6f)](!![]),this[_0x2af9b6(0xa3)][_0x2af9b6(0x72)](_0x36868c=>{const _0x93d6dd=_0x2af9b6;this['curtains']=new _0x36868c[(_0x93d6dd(0xc5))]({'element':this[_0x93d6dd(0xaa)],'elements':this[_0x93d6dd(0xeb)],'options':this[_0x93d6dd(0x88)]}),this[_0x93d6dd(0xae)][_0x93d6dd(0x67)]()[_0x93d6dd(0xd0)](()=>{const _0x34a95e=_0x93d6dd;this[_0x34a95e(0xdc)](),this[_0x34a95e(0x6f)](![]),_0x3cae01(!![]);});})[_0x2af9b6(0x7b)](()=>_0x3cae01(!![]))):(this[_0x2af9b6(0xdc)](),_0x3cae01(!![]));});}[_0x40e320(0xe7)](){return new Promise(_0x48cb88=>{const _0x31cbd8=_0x1ade,_0xafbcde=[],_0x9e3c5c=app[_0x31cbd8(0xad)][_0x31cbd8(0x95)](_0x31cbd8(0xa5));this['_detachEvents']();if(!_0x9e3c5c||!_0x9e3c5c['running']){if(this[_0x31cbd8(0x83)]&&typeof this[_0x31cbd8(0x83)][_0x31cbd8(0x85)]===_0x31cbd8(0xcb)){const _0x2adc3f=scheduler[_0x31cbd8(0xc3)](()=>{const _0x335c70=_0x31cbd8;this[_0x335c70(0x83)][_0x335c70(0x85)]();});_0xafbcde[_0x31cbd8(0xca)](_0x2adc3f);}if(this[_0x31cbd8(0x7a)]&&typeof this[_0x31cbd8(0x7a)][_0x31cbd8(0x85)]==='function'){const _0xbe46a5=scheduler[_0x31cbd8(0xc3)](()=>{const _0x243937=_0x31cbd8;this[_0x243937(0x7a)][_0x243937(0x85)]();});_0xafbcde['push'](_0xbe46a5);}if(this[_0x31cbd8(0xd6)]){const _0x3d7a5e=scheduler[_0x31cbd8(0xc3)](()=>{const _0x27129d=_0x31cbd8;this[_0x27129d(0xd6)]['toggleHidden'](![]);});_0xafbcde[_0x31cbd8(0xca)](_0x3d7a5e);}}Promise['all'](_0xafbcde)['finally'](()=>_0x48cb88(!![]));});}[_0x40e320(0xe3)](){const _0x1e8f9d=_0x40e320;return app['componentsManager'][_0x1e8f9d(0x9b)]({'properties':app['components'][_0x1e8f9d(0x9c)]});}['_setRefs'](){const _0x8910cf=_0x40e320;this[_0x8910cf(0xb0)](_0x8910cf(0xd6),_0x8910cf(0xbb)),this['maskRef']=this['components'][_0x8910cf(0x9a)](_0x12f034=>_0x12f034['name']===_0x8910cf(0xe6));}[_0x40e320(0xdc)](){const _0x5c473b=_0x40e320;this['elements'][_0x5c473b(0x8f)][0x0]&&this[_0x5c473b(0xeb)][_0x5c473b(0x8f)][0x0][_0x5c473b(0x94)]('click',this[_0x5c473b(0x75)]['clickHeader'],!![]),this[_0x5c473b(0xeb)][_0x5c473b(0xbd)][0x0]&&this['elements'][_0x5c473b(0xbd)][0x0][_0x5c473b(0x94)](_0x5c473b(0x6c),this['_handlers'][_0x5c473b(0xb2)],!![]),this['elements']['scrollDownWrapper'][0x0]&&this[_0x5c473b(0xeb)][_0x5c473b(0x96)][0x0][_0x5c473b(0x94)](_0x5c473b(0x6c),this['_handlers'][_0x5c473b(0xb2)],!![]);}[_0x40e320(0xa7)](){const _0x1b472a=_0x40e320;this[_0x1b472a(0xeb)]['nextHeader'][0x0]&&this[_0x1b472a(0xeb)][_0x1b472a(0x8f)][0x0][_0x1b472a(0xa6)](_0x1b472a(0x6c),this[_0x1b472a(0x75)][_0x1b472a(0xb2)],!![]),this[_0x1b472a(0xeb)][_0x1b472a(0xbd)][0x0]&&this[_0x1b472a(0xeb)]['mediaWrapper'][0x0][_0x1b472a(0xa6)](_0x1b472a(0x6c),this[_0x1b472a(0x75)][_0x1b472a(0xb2)],!![]),this['elements'][_0x1b472a(0x96)][0x0]&&this['elements'][_0x1b472a(0x96)][0x0][_0x1b472a(0xa6)](_0x1b472a(0x6c),this['_handlers']['clickHeader'],!![]);}[_0x40e320(0xbc)](){const _0x255d09=_0x40e320;this[_0x255d09(0x83)]=ScrollTrigger[_0x255d09(0xc1)]({'start':()=>'top\x20top','end':()=>_0x255d09(0xb9)+this[_0x255d09(0xaa)][_0x255d09(0xc9)]*this[_0x255d09(0x88)][_0x255d09(0xde)],'pin':this[_0x255d09(0xeb)]['fixedWrapper'][0x0],'pinSpacing':!![],'animation':this[_0x255d09(0xa4)](),'trigger':this['element'],'invalidateOnRefresh':!![],'scrub':!![],'onUpdate':this[_0x255d09(0x75)][_0x255d09(0x78)],'onRefresh':this[_0x255d09(0x75)][_0x255d09(0x7d)],'onEnter':this[_0x255d09(0x75)][_0x255d09(0x99)],'onLeaveBack':this[_0x255d09(0x75)][_0x255d09(0x99)],'onLeave':this['_handlers']['transitionStart']});}[_0x40e320(0xd3)](){const _0x5c102b=_0x40e320;this[_0x5c102b(0x7a)]=ScrollTrigger['create']({'trigger':this[_0x5c102b(0xaa)],'start':()=>_0x5c102b(0x7e),'once':!![],'onEnter':()=>{const _0x160800=_0x5c102b;scheduler[_0x160800(0xc3)](()=>{const _0x90a9fe=_0x160800,_0x383418=app[_0x90a9fe(0xad)][_0x90a9fe(0x95)](_0x90a9fe(0xa5));try{const _0x4cabaa=new URL(this['elements'][_0x90a9fe(0x8c)][0x0][_0x90a9fe(0x69)](_0x90a9fe(0x8d)));_0x4cabaa[_0x90a9fe(0x8d)]&&_0x4cabaa[_0x90a9fe(0x8d)]!=='#'&&window['location'][_0x90a9fe(0xbf)]===_0x4cabaa[_0x90a9fe(0xbf)]&&_0x383418[_0x90a9fe(0xe2)](_0x4cabaa[_0x90a9fe(0x8d)]),this['prefetchScene']&&this[_0x90a9fe(0x7a)][_0x90a9fe(0x85)]();}catch(_0xdb6f26){}});}});}[_0x40e320(0xaf)](){const _0x468bee=_0x40e320;document['body'][_0x468bee(0x8b)]['toggle'](_0x468bee(0x9f),this[_0x468bee(0x83)][_0x468bee(0xb3)]),this[_0x468bee(0xb7)](this[_0x468bee(0x83)][_0x468bee(0xb3)]),this[_0x468bee(0x6e)](),this[_0x468bee(0xae)]&&this[_0x468bee(0xae)][_0x468bee(0xb6)]();}[_0x40e320(0x6e)](){const _0x382fce=_0x40e320;this[_0x382fce(0x90)]&&this[_0x382fce(0x90)][0x0]&&this[_0x382fce(0x90)][0x0]['update']();}[_0x40e320(0xa4)](){const _0x113e9b=_0x40e320,_0x4d5735=gsap[_0x113e9b(0xdb)]({'paused':!![]});this[_0x113e9b(0xeb)]['progressLine']&&_0x4d5735[_0x113e9b(0xa8)](this['elements'][_0x113e9b(0x82)],{'scaleX':0x0,'transformOrigin':'left\x20center'},{'scaleX':0x1,'ease':_0x113e9b(0xa0)},_0x113e9b(0xe4));this['elements'][_0x113e9b(0x96)][0x0]&&_0x4d5735[_0x113e9b(0xa8)](this[_0x113e9b(0xeb)]['scrollDownWrapper'][0x0],{'y':'0%','autoAlpha':0x1},{'y':_0x113e9b(0x86),'autoAlpha':0x0,'ease':_0x113e9b(0xa0)},_0x113e9b(0xe4));if(this[_0x113e9b(0xeb)][_0x113e9b(0xbd)][0x0]){let _0x379a44,_0x1c3b44;typeof this[_0x113e9b(0x88)][_0x113e9b(0x70)]['scalePlane']==='number'&&typeof this[_0x113e9b(0x88)][_0x113e9b(0x84)]['scalePlane']==='number'&&(_0x379a44=this[_0x113e9b(0x88)][_0x113e9b(0x84)][_0x113e9b(0x93)],_0x1c3b44=this[_0x113e9b(0x88)][_0x113e9b(0x70)][_0x113e9b(0x93)]),_0x379a44&&_0x1c3b44&&_0x4d5735[_0x113e9b(0xa8)](this[_0x113e9b(0xeb)][_0x113e9b(0xbd)][0x0],{'scale':_0x379a44,'transformOrigin':'center\x20center'},{'scale':_0x1c3b44,'ease':'none'},_0x113e9b(0xe4));}if(this[_0x113e9b(0xeb)][_0x113e9b(0xda)][0x0]){let _0x4a048b,_0x4be4ab;typeof this[_0x113e9b(0x88)][_0x113e9b(0x70)][_0x113e9b(0xc4)]===_0x113e9b(0x65)&&typeof this[_0x113e9b(0x88)]['onSceneIdle'][_0x113e9b(0xc4)]===_0x113e9b(0x65)&&(_0x4a048b=this['options']['onSceneIdle'][_0x113e9b(0xc4)],_0x4be4ab=this[_0x113e9b(0x88)]['onSceneProgress']['scaleTexture']),_0x4a048b&&_0x4be4ab&&_0x4d5735['fromTo'](this['elements']['media'][0x0],{'scale':_0x4a048b,'transformOrigin':_0x113e9b(0xea)},{'scale':_0x4be4ab,'ease':_0x113e9b(0xa0)},'start');}return _0x4d5735;}[_0x40e320(0xb4)](){const _0x5da143=_0x40e320;if(!!this[_0x5da143(0x88)][_0x5da143(0x6b)]&&this['headerRef']){const _0x261f2b={'trigger':this[_0x5da143(0xaa)],'start':()=>_0x5da143(0x9d)+this[_0x5da143(0xd6)]['element'][_0x5da143(0xc9)]+'\x20top','end':()=>_0x5da143(0xcd),'scrub':!![],'matchMedia':this['options'][_0x5da143(0x68)],'onToggle':_0x2ea60c=>this[_0x5da143(0xd6)][_0x5da143(0xc7)](_0x2ea60c[_0x5da143(0xb3)])};return _0x261f2b;}}[_0x40e320(0xb7)](_0x3ce75f=!![]){const _0x33cd73=_0x40e320;typeof this['options'][_0x33cd73(0xe8)]==='string'&&this['element']['classList'][_0x33cd73(0x6a)](this[_0x33cd73(0x88)][_0x33cd73(0xe8)],_0x3ce75f);}[_0x40e320(0xbe)](_0x33b0f7=!![]){const _0x25933c=_0x40e320;typeof this['options'][_0x25933c(0x9e)]===_0x25933c(0xa9)&&this[_0x25933c(0xaa)][_0x25933c(0x8b)]['toggle'](this[_0x25933c(0x88)][_0x25933c(0x9e)],_0x33b0f7);}[_0x40e320(0x71)](){const _0x13de64=_0x40e320;if(!!app[_0x13de64(0x88)][_0x13de64(0xa2)])return;let _0x13d0f6=window[_0x13de64(0x89)];const _0x36867f=setInterval(()=>{const _0x20d5f0=_0x13de64;_0x13d0f6!==window['scrollY']?_0x13d0f6=window[_0x20d5f0(0x89)]:(clearInterval(_0x36867f),this[_0x20d5f0(0xbe)](!![]),this[_0x20d5f0(0xeb)]['nextLinks'][_0x20d5f0(0x80)]&&this[_0x20d5f0(0xeb)]['nextLinks'][0x0][_0x20d5f0(0x6c)]());},0xc8);app[_0x13de64(0xb1)][_0x13de64(0xb5)](!![]),app[_0x13de64(0xb1)][_0x13de64(0x77)](!![]),this[_0x13de64(0xd4)]=!![],this[_0x13de64(0x83)][_0x13de64(0x85)](![],![]);}[_0x40e320(0xba)]({progress:_0x3a2ebd}){const _0x553001=_0x40e320;this[_0x553001(0xae)]&&this['curtains'][_0x553001(0xac)](_0x3a2ebd);if(typeof this[_0x553001(0x88)][_0x553001(0x70)]['scalePlane']==='number'&&typeof this[_0x553001(0x88)][_0x553001(0x84)][_0x553001(0x93)]===_0x553001(0x65)){const _0x5ebb97=this[_0x553001(0x88)][_0x553001(0x70)]['scalePlane']-this[_0x553001(0x88)][_0x553001(0x84)][_0x553001(0x93)],_0x12172b=this[_0x553001(0x88)][_0x553001(0x84)]['scalePlane']+_0x3a2ebd*_0x5ebb97;this[_0x553001(0x90)]&&this[_0x553001(0x90)][0x0]&&this[_0x553001(0xeb)][_0x553001(0xbd)][0x0]&&(this[_0x553001(0x90)][0x0]['scaleX']=_0x12172b,this[_0x553001(0x90)][0x0]['scaleY']=_0x12172b,this[_0x553001(0x90)][0x0]['setMask']());}}['_onClickHeader'](){const _0x5906c8=_0x40e320;if(!this[_0x5906c8(0xd4)]){const _0x237515=this[_0x5906c8(0xcf)]();this['_transitionActive']=!![],app['utilities'][_0x5906c8(0x74)]({'target':_0x237515,'cb':()=>{const _0x97ce60=_0x5906c8;this[_0x97ce60(0xd4)]=![];}});}}[_0x40e320(0xcf)](){const _0x1ba2a6=_0x40e320;return this['fixedScene'][_0x1ba2a6(0xc2)]+parseInt(getComputedStyle(document[_0x1ba2a6(0xce)])[_0x1ba2a6(0xab)])+0x1;}[_0x40e320(0x6d)](){const _0x472a78=_0x40e320;this[_0x472a78(0x6e)](),this[_0x472a78(0xae)]&&this[_0x472a78(0xae)][_0x472a78(0xb6)]();}}