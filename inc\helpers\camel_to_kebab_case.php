<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_camel_to_kebab_case' ) ) {
	/**
	 * Converts a string from camel case to kebab case.
	 *
	 * @param string $string The input string to be converted.
	 * @example Example `myCamelCaseString` becomes `my-camel-case-string`
	 *
	 * @return string The converted string in kebab case.
	 * @deprecated 2.0.0 Use `Arts\Utilities\Utilities::convert_camel_to_kebab_case()` method instead.
	 */
	function arts_camel_to_kebab_case( $string ) {
		return Utilities::convert_camel_to_kebab_case( $string );
	}
}
