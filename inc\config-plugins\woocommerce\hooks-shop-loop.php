<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'woocommerce_before_shop_loop', 'arts_output_shop_wrapper_start', -9999 );
if ( ! function_exists( 'arts_output_shop_wrapper_start' ) ) {
	function arts_output_shop_wrapper_start() {
		?><div class="section-shop">
		<?php
	}
}

add_action( 'woocommerce_after_shop_loop', 'arts_output_shop_wrapper_end', 9999 );
if ( ! function_exists( 'arts_output_shop_wrapper_end' ) ) {
	function arts_output_shop_wrapper_end() {
		?>
		</div>
		<?php
	}
}

add_action( 'woocommerce_before_shop_loop', 'arts_output_content_header_wrapper_start', 15 );
if ( ! function_exists( 'arts_output_content_header_wrapper_start' ) ) {
	function arts_output_content_header_wrapper_start() {
		if ( ! has_action( 'woocommerce_before_shop_loop', 'woocommerce_catalog_ordering' ) && ! has_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count' ) ) {
			return;
		}

		$header_inner_attributes = array(
			'class' => array(
				'section-shop__header-inner',
			),
		);

		if ( class_exists( '\Elementor\Plugin' ) ) {
			$header_inner_attributes['class'][] = 'pb-xsmall';
		} else {
			$header_inner_attributes['class'][] = 'px-gutters';
			$header_inner_attributes['class'][] = 'py-xsmall';
		}
		?>
		<div class="section-shop__header">
			<div <?php arts_print_attributes( $header_inner_attributes ); ?>>
		<?php
	}
}

add_action( 'woocommerce_before_shop_loop', 'arts_output_content_header_wrapper_end', 40 );
if ( ! function_exists( 'arts_output_content_header_wrapper_end' ) ) {
	function arts_output_content_header_wrapper_end() {
		if ( ! has_action( 'woocommerce_before_shop_loop', 'woocommerce_catalog_ordering' ) && ! has_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count' ) ) {
			return;
		}
		?>
		</div></div>
		<?php
	}
}

add_action( 'woocommerce_before_shop_loop', 'arts_output_content_loop_wrapper_start', 50 );
if ( ! function_exists( 'arts_output_content_loop_wrapper_start' ) ) {
	function arts_output_content_loop_wrapper_start() {
		$shop_sidebar_id   = 'shop-sidebar';
		$is_active_sidebar = is_active_sidebar( $shop_sidebar_id );

		$shop_container_attributes = array(
			'class' => array( 'section-shop__container' ),
		);

		$row_attributes = array(
			'class' => array(
				'row',
				'section-shop__row',
			),
		);

		$col_products_attributes = array(
			'class' => array(
				'section-shop__products',
				'col-12',
				'order-1',
				'order-lg-1',
			),
		);

		$col_sidebar_attributes = array(
			'class' => array(
				'section-shop__products',
				'col-12',
				'col-lg-3',
				'order-2',
				'mt-medium',
				'mt-lg-0',
				'order-lg-2',
			),
		);

		if ( ! class_exists( '\Elementor\Plugin' ) ) {
			$shop_container_attributes['class'][] = 'px-gutters';
		}

		if ( $is_active_sidebar ) {
			$row_attributes['class'][]          = 'justify-content-between';
			$col_products_attributes['class'][] = 'col-lg-8';
		} else {
			$row_attributes['class'][] = 'justify-content-center';
		}

		?>
		 <div <?php arts_print_attributes( $shop_container_attributes ); ?>>
			 <div <?php arts_print_attributes( $row_attributes ); ?>>
				<?php if ( $is_active_sidebar ) : ?>
					<!-- Sidebar -->
					<div <?php arts_print_attributes( $col_sidebar_attributes ); ?>>
						<aside class="sidebar widget-area">
							<?php dynamic_sidebar( $shop_sidebar_id ); ?>
						</aside>
					</div>
					<!-- - Sidebar -->
				<?php endif; ?>
				 <div <?php arts_print_attributes( $col_products_attributes ); ?>>
		<?php
	}
}

add_action( 'woocommerce_after_shop_loop', 'arts_output_content_loop_wrapper_end', 100 );
if ( ! function_exists( 'arts_output_content_loop_wrapper_end' ) ) {
	function arts_output_content_loop_wrapper_end() {
		?>
		 </div></div></div>
		<?php
	}
}
