<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$defaults = array(
	'title'       => '',
	'title_hover' => '',
	'icon'        => array(
		'after'  => '',
		'before' => '',
	),
	'attributes'  => array(
		'href'  => '#',
		'class' => array(
			'button',
			'ui-element',
		),
	),
	'progress'    => false,
);

$args = Utilities::parse_args_recursive( $args, $defaults );

$label_normal_attributes = array(
	'class' => array( 'button__label' ),
);

$label_hover_attributes = array(
	'class' => array( 'button__label' ),
);

if ( $args['progress'] ) {
	$args['attributes']['class'][]     = 'button-progress';
	$label_hover_attributes['class'][] = 'button-progress__hold';
} else {
	$label_hover_attributes['class'][] = 'button__label-hover';
}

if ( ! empty( $args['title_hover'] ) ) {
	$label_normal_attributes['class'][] = $args['progress'] ? 'button-progress__normal' : 'button__label-normal';
}

if ( ! empty( $args['icon']['before'] ) || ! empty( $args['icon']['after'] ) ) {
	$args['attributes']['class'][] = 'button_icon';
}

$is_elementor_icon_manager         = class_exists( '\Elementor\Icons_Manager' );
$is_icon_before_elementor_rendered = $is_elementor_icon_manager && ! empty( $args['icon']['before'] ) && is_array( $args['icon']['before'] ) && array_key_exists( 'value', $args['icon']['before'] ) && ! empty( $args['icon']['before']['value'] );
$is_icon_after_elementor_rendered  = $is_elementor_icon_manager && ! empty( $args['icon']['after'] ) && is_array( $args['icon']['after'] ) && array_key_exists( 'value', $args['icon']['after'] ) && ! empty( $args['icon']['after']['value'] );

?>

<a <?php Utilities::print_attributes( $args['attributes'] ); ?>>
	<span <?php Utilities::print_attributes( $label_normal_attributes ); ?>>
		<?php if ( ! empty( $args['icon']['before'] ) ) : ?>
			<?php if ( $is_icon_before_elementor_rendered ) : ?>
				<span class="button__icon button__icon_before"><?php \Elementor\Icons_Manager::render_icon( $args['icon']['before'] ); ?></span>
			<?php elseif ( is_string( $args['icon']['before'] ) ) : ?>
				<span class="button__icon button__icon_before <?php echo esc_attr( $args['icon']['before'] ); ?>"></span>
			<?php endif; ?>
		<?php endif; ?>

		<span class="button__title"><?php echo esc_html( $args['title'] ); ?></span>

		<?php if ( ! empty( $args['icon']['after'] ) ) : ?>
			<?php if ( $is_icon_after_elementor_rendered ) : ?>
				<span class="button__icon button__icon_after"><?php \Elementor\Icons_Manager::render_icon( $args['icon']['after'] ); ?></span>
			<?php elseif ( is_string( $args['icon']['after'] ) ) : ?>
				<span class="button__icon button__icon_after <?php echo esc_attr( $args['icon']['after'] ); ?>"></span>
			<?php endif; ?>
		<?php endif; ?>
	</span>
	<?php if ( ! empty( $args['title_hover'] ) ) : ?>
		<span <?php Utilities::print_attributes( $label_hover_attributes ); ?>>
			<?php if ( ! empty( $args['icon']['before'] ) ) : ?>
				<?php if ( $is_icon_before_elementor_rendered ) : ?>
					<span class="button__icon button__icon_before"><?php \Elementor\Icons_Manager::render_icon( $args['icon']['before'] ); ?></span>
				<?php elseif ( is_string( $args['icon']['before'] ) ) : ?>
					<span class="button__icon button__icon_before <?php echo esc_attr( $args['icon']['before'] ); ?>"></span>
				<?php endif; ?>
			<?php endif; ?>

			<span class="button__title"><?php echo esc_html( $args['title_hover'] ); ?></span>

			<?php if ( ! empty( $args['icon']['after'] ) ) : ?>
				<?php if ( $is_icon_after_elementor_rendered ) : ?>
					<span class="button__icon button__icon_after"><?php \Elementor\Icons_Manager::render_icon( $args['icon']['after'] ); ?></span>
				<?php elseif ( is_string( $args['icon']['after'] ) ) : ?>
					<span class="button__icon button__icon_after <?php echo esc_attr( $args['icon']['after'] ); ?>"></span>
				<?php endif; ?>
			<?php endif; ?>
		</span>
	<?php endif; ?>

	<?php if ( $args['progress'] ) : ?>
		<?php get_template_part( 'template-parts/svg/responsive-rectangle' ); ?>
	<?php endif; ?>
</a>
