<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_filter( 'woocommerce_demo_store', 'arts_custom_sitewide_notice' );
if ( ! function_exists( 'arts_custom_sitewide_notice' ) ) {
	function arts_custom_sitewide_notice() {
		$notice = get_option( 'woocommerce_demo_store_notice' );

		if ( empty( $notice ) ) {
			$notice = __( 'This is a demo store for testing purposes &mdash; no orders shall be fulfilled.', 'asli' );
		}

		$notice_id = hash( 'sha256', $notice );

		$output  = '<div class="woocommerce-store-notice demo_store" data-notice-id="' . esc_attr( $notice_id ) . '" style="display:none;">';
		$output .= '<div class="woocommerce-store-notice__container">';
		$output .= '<p class="woocommerce-store-notice__text">' . wp_kses_post( $notice ) . '</p>';
		$output .= '<a href="#" class="woocommerce-store-notice__dismiss-link">' . esc_html__( 'Dismiss', 'asli' ) . '</a>';
		$output .= '</div>';
		$output .= '</div>';

		return $output;
	}
}
