<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

add_action( 'wp_enqueue_scripts', 'arts_inline_theme_options', 60 );
if ( ! function_exists( 'arts_inline_theme_options' ) ) {
	/**
	 * Localize the theme options
	 *
	 * @return void
	 */
	function arts_inline_theme_options() {
		wp_localize_script(
			'asli-app',
			'asli_theme_options',
			array(
				'themeVersion'      => esc_attr( ARTS_THEME_VERSION ),
				'themeURL'          => esc_url( ARTS_THEME_URL ),
				'isElementorEditor' => boolval( Utilities::is_elementor_editor_active() ),
				'ajaxURL'           => admin_url( 'admin-ajax.php' ),
				'nonce'             => array(
					'elementorEditor' => wp_create_nonce( 'elementor-editor-nonce' ),
				),

				/**
				 * Smooth scrolling options
				 */
				'smoothScroll'      => arts_get_smooth_scroll_options(),

				/**
				 * Scrolling options for components that use virtual scroll
				 */
				'virtualScroll'     => arts_get_virtual_scroll_options(),

				/**
				 * Preloader options
				 */
				'preloader'         => arts_get_preloader_options(),

				/**
				 * Mouse cursor follower options
				 */
				'cursorFollower'    => arts_get_cursor_follower_options(),

				/**
				 * System loading mouse indicator
				 */
				'cursorLoading'     => boolval( Utilities::get_kit_settings( 'cursor_progress_enabled', true ) ),

				/**
				 * AJAX transitions
				 */
				'ajax'              => arts_get_ajax_transitions_options(),

				/**
				 * Lightbox gallery
				 */
				'gallery'           => arts_get_lightbox_gallery_options(),

				/**
				 * On-Scroll animations
				 */
				'animations'        => arts_get_animations_options(),

				/**
				 * SVG shape used for creating "drawing" effect
				 */
				'circleTemplate'    => arts_get_drawing_circle_template(),

				/**
				 * Contact Form 7 config
				 */
				'contactForm7'      => arts_get_contact_form_7_options(),
			)
		);
	}
}
