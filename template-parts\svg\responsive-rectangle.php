<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$attributes = Utilities::get_component_attributes(
	array(),
	array(
		'name'         => 'ResponsiveRectangle',
		'options'      => array(),
		'hasAnimation' => false,
	)
);
?>

<div <?php Utilities::print_attributes( $attributes ); ?>>
	<svg class="svg-rect js-responsive-rectangle__svg" width="100%" height="100%">
		<rect class="js-responsive-rectangle__rect" x="0" y="0" rx="0" width="0" height="0"></rect>
	</svg>
</div>
