<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_lightbox_gallery_options' ) ) {
	/**
	 * Get the lightbox gallery options.
	 *
	 * @return array $options
	 */
	function arts_get_lightbox_gallery_options() {
		$lightbox_gallery_captions_enabled = Utilities::get_kit_settings( 'lightbox_gallery_captions_enabled', true );
		$lightbox_images_counter_enabled   = Utilities::get_kit_settings( 'lightbox_images_counter_enabled', true );
		$lightbox_zoom_button_enabled      = Utilities::get_kit_settings( 'lightbox_zoom_button_enabled', false );
		$lightbox_initial_zoom_level       = Utilities::get_kit_settings( 'lightbox_initial_zoom_level', 'fit' );
		$lightbox_secondary_zoom_level     = Utilities::get_kit_settings( 'lightbox_secondary_zoom_level', 2.5 );
		$lightbox_maximum_zoom_level       = Utilities::get_kit_settings( 'lightbox_maximum_zoom_level', 4.0 );
		$lightbox_background_opacity       = Utilities::get_kit_settings( 'lightbox_background_opacity', 1.0 );
		$lightbox_elements_color_hover     = Utilities::get_kit_settings( 'lightbox_elements_color_hover', '#FDF9CF' );
		$lightbox_arrows_color_hover       = Utilities::get_kit_settings( 'lightbox_arrows_color_hover', '#FDF9CF' );

		$options = array(
			'itemsSelector'      => 'a[href]:not(a[href="#"]):not(a[href*="#"]):not(.no-lightbox)',
			'bgOpacity'          => floatval( $lightbox_background_opacity ),
			'initialZoomLevel'   => esc_js( $lightbox_initial_zoom_level ),
			'secondaryZoomLevel' => floatval( $lightbox_secondary_zoom_level ),
			'maxZoomLevel'       => floatval( $lightbox_maximum_zoom_level ),
			// "X" (close) button
			'close'              => array(
				'custom'     => true,
				'label'      => false,
				'labelHover' => false,
				'cursor'     => array(
					'magnetic'   => 0.25,
					'scale'      => 1.3,
					'hideNative' => false,
					'color'      => esc_js( $lightbox_elements_color_hover ),
				),
			),
			// Prev & next gallery arrows
			'arrows'             => array(
				'custom' => true,
				'cursor' => array(
					'scale'    => 'current',
					'magnetic' => 0.25,
					'color'    => esc_js( $lightbox_arrows_color_hover ),
				),
			),
			// Images counter in gallery (e.g. "2 / 7")
			'counter'            => false,
			// Images captions grabbed from 'data-caption' attribute on <a> link
			// or from "alt" attribute of the currently active image
			'captions'           => boolval( $lightbox_gallery_captions_enabled ),
			// Media loading indicator
			'preloader'          => array(
				'custom' => true,
			),
			// "Zoom" button in top bar
			'zoom'               => false,
		);

		if ( $lightbox_images_counter_enabled ) {
			$options['counter'] = array(
				'custom' => true,
			);
		}

		if ( $lightbox_zoom_button_enabled ) {
			$options['zoom'] = array(
				'custom' => true,
			);
		}

		return $options;
	}
}
