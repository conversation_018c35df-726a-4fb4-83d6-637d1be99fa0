function _0x2e83(_0x424b12,_0x1966fc){const _0x3d3e6e=_0x3d3e();return _0x2e83=function(_0x2e83e5,_0x2d4705){_0x2e83e5=_0x2e83e5-0x134;let _0xf00036=_0x3d3e6e[_0x2e83e5];return _0xf00036;},_0x2e83(_0x424b12,_0x1966fc);}const _0x1cbeac=_0x2e83;(function(_0x47bdf0,_0x371b58){const _0x509825=_0x2e83,_0x59c364=_0x47bdf0();while(!![]){try{const _0x23c4bc=parseInt(_0x509825(0x156))/0x1+parseInt(_0x509825(0x16f))/0x2*(-parseInt(_0x509825(0x137))/0x3)+parseInt(_0x509825(0x157))/0x4*(-parseInt(_0x509825(0x181))/0x5)+parseInt(_0x509825(0x169))/0x6+-parseInt(_0x509825(0x13d))/0x7+parseInt(_0x509825(0x17d))/0x8+parseInt(_0x509825(0x165))/0x9;if(_0x23c4bc===_0x371b58)break;else _0x59c364['push'](_0x59c364['shift']());}catch(_0x2d5948){_0x59c364['push'](_0x59c364['shift']());}}}(_0x3d3e,0xad595));export default class RotatingButton extends BaseComponent{constructor({name:_0x4c5c16,loadInnerComponents:_0x1958df,loadAfterSyncStyles:_0x115077,parent:_0x4fb2bc,element:_0x387ffc}){const _0x299b3e=_0x2e83;super({'name':_0x4c5c16,'loadInnerComponents':_0x1958df,'loadAfterSyncStyles':_0x115077,'parent':_0x4fb2bc,'element':_0x387ffc,'defaults':{'rotateAnimation':{'onScrollSpeed':0x2,'constantRotation':![]},'delimiter':'&nbsp;&nbsp;→&nbsp;&nbsp;','loopRounds':0x2},'innerElements':{'link':_0x299b3e(0x143),'label':_0x299b3e(0x17c),'icon':_0x299b3e(0x180)}}),this[_0x299b3e(0x154)]={'displayInit':app[_0x299b3e(0x14b)]['debounce'](this[_0x299b3e(0x17f)]['bind'](this),app[_0x299b3e(0x14b)]['getDebounceTime']())},this[_0x299b3e(0x14f)][_0x299b3e(0x174)](()=>{const _0x243258=_0x299b3e;this['mq']=null,this['originalLabel']='',this[_0x243258(0x159)]=gsap['utils'][_0x243258(0x173)](0x1,0xa),this[_0x243258(0x142)]();});}[_0x1cbeac(0x142)](){const _0x4e39b6=_0x1cbeac,_0x38655d=app['componentsManager'][_0x4e39b6(0x16d)](_0x4e39b6(0x17e));document[_0x4e39b6(0x147)]['ready'][_0x4e39b6(0x15c)](()=>this['mount']())[_0x4e39b6(0x174)](()=>{const _0x10160c=_0x4e39b6;_0x38655d&&_0x38655d[_0x10160c(0x155)]?_0x38655d[_0x10160c(0x152)](()=>new Promise(_0x1f6587=>{const _0x58c155=_0x10160c;this[_0x58c155(0x140)]()[_0x58c155(0x15c)](()=>this[_0x58c155(0x185)]())[_0x58c155(0x174)](()=>_0x1f6587(!![]));},'endBefore')):this[_0x10160c(0x140)]()[_0x10160c(0x174)](()=>this[_0x10160c(0x185)]()),this['_setReady']();});}['init'](){return new Promise(_0x3b97ce=>{setTimeout(()=>{const _0x4fc084=_0x2e83;this['element'][_0x4fc084(0x170)]?this['_doInit']()['finally'](()=>_0x3b97ce(!![])):(this['mq']=app[_0x4fc084(0x14b)][_0x4fc084(0x17b)]({'callback':this['_handlers'][_0x4fc084(0x16e)],'immediateCall':![]}),_0x3b97ce(!![]));},0x1);});}['destroy'](){return new Promise(_0x1c5267=>{const _0x4bbed3=_0x2e83;this[_0x4bbed3(0x150)](),_0x1c5267(!![]);});}[_0x1cbeac(0x17f)](){const _0x125632=_0x1cbeac;this[_0x125632(0x153)][_0x125632(0x170)]&&(this['mq']&&typeof this['mq'][_0x125632(0x163)]===_0x125632(0x177)&&this['mq']['clear'](),this[_0x125632(0x14c)]());}[_0x1cbeac(0x14c)](){return new Promise(_0x32459e=>{const _0x2a7e60=_0x2e83;this[_0x2a7e60(0x187)]['label']&&this['elements'][_0x2a7e60(0x15a)][0x0]?this[_0x2a7e60(0x15d)]()[_0x2a7e60(0x15c)](()=>this['_loopLabel']())[_0x2a7e60(0x15c)](()=>this['_createCircle']())[_0x2a7e60(0x174)](()=>{const _0x1771a1=_0x2a7e60;this[_0x1771a1(0x148)](),_0x32459e(!![]);}):_0x32459e(!![]);});}[_0x1cbeac(0x14d)](){const _0x57b335=_0x1cbeac;if(!this[_0x57b335(0x171)][_0x57b335(0x179)])return;const _0x27b0da=gsap[_0x57b335(0x176)]({'paused':!![],'defaults':{'ease':_0x57b335(0x146),'repeat':-0x1}})['to'](this[_0x57b335(0x187)][_0x57b335(0x15a)][0x0],{'rotate':0x168,'duration':0x1e},_0x57b335(0x138))['to'](this['elements']['icon'][0x0],{'rotate':-0x168,'duration':0xa},_0x57b335(0x138)),_0x2274d9={'animation':_0x27b0da,'trigger':this['element'],'once':![],'onToggle':_0x3af13a=>{const _0x33dac6=_0x57b335;_0x3af13a[_0x33dac6(0x178)]?_0x27b0da[_0x33dac6(0x164)]():_0x27b0da['pause']();}};!!this[_0x57b335(0x171)]['rotateAnimation']['constantRotation']&&(_0x2274d9[_0x57b335(0x183)]=undefined,_0x2274d9[_0x57b335(0x138)]=-0x1,_0x2274d9[_0x57b335(0x136)]=-0x1);if(typeof this['options'][_0x57b335(0x179)][_0x57b335(0x13c)]===_0x57b335(0x186)){const _0x15c81b={'velocity':0x1},_0x10f5ea=ScrollTrigger[_0x57b335(0x151)](_0x57b335(0x13e));_0x10f5ea&&(_0x2274d9['onUpdate']=()=>{const _0x346c49=_0x57b335;let _0x3f43f1=this['_clamp'](Math['abs'](_0x10f5ea[_0x346c49(0x13f)]())/0x64)*this[_0x346c49(0x171)][_0x346c49(0x179)][_0x346c49(0x13c)];_0x3f43f1>_0x15c81b[_0x346c49(0x139)]&&(_0x15c81b[_0x346c49(0x139)]=_0x3f43f1,gsap['to'](_0x15c81b,{'velocity':0x1,'duration':0.6,'ease':_0x346c49(0x172),'overwrite':!![],'onUpdate':()=>{const _0x200b42=_0x346c49;_0x27b0da[_0x200b42(0x134)](_0x15c81b['velocity']);}}));});}return _0x2274d9;}[_0x1cbeac(0x135)](){const _0x498fc5=_0x1cbeac,_0x21eacb=gsap[_0x498fc5(0x176)]({'paused':!![]})['animateScale'](this['elements'][_0x498fc5(0x149)],{'ease':_0x498fc5(0x172),'animateFrom':_0x498fc5(0x160)},'<');return _0x21eacb;}[_0x1cbeac(0x15d)](){return new Promise(_0x1fa141=>{const _0xb8af47=_0x2e83,_0x12a46f=[];this[_0xb8af47(0x14a)]=this[_0xb8af47(0x187)]['label'][0x0][_0xb8af47(0x161)];let _0x19883c=this[_0xb8af47(0x14a)];if(this[_0xb8af47(0x171)]['delimiter']&&this[_0xb8af47(0x171)][_0xb8af47(0x184)]['length']){_0x19883c=this[_0xb8af47(0x14a)]+this['options'][_0xb8af47(0x184)];const _0x1fe727=scheduler[_0xb8af47(0x167)](()=>{const _0x3065ac=_0xb8af47;this['elements'][_0x3065ac(0x15a)][0x0][_0x3065ac(0x161)]=_0x19883c;});_0x12a46f[_0xb8af47(0x15e)](_0x1fe727);}Promise[_0xb8af47(0x158)](_0x12a46f)[_0xb8af47(0x174)](()=>{this['label']=_0x19883c,_0x1fa141(!![]);});});}[_0x1cbeac(0x16b)](){return new Promise(_0x150c42=>{const _0x386a12=_0x2e83,_0x558399=[];if(this[_0x386a12(0x171)][_0x386a12(0x15b)]>0x0){let _0x44b6e0=this['label'];for(let _0x982516=0x0;_0x982516<this['options'][_0x386a12(0x15b)];_0x982516++){_0x44b6e0+=this[_0x386a12(0x15a)];}const _0x250737=scheduler[_0x386a12(0x167)](()=>{const _0x4363af=_0x386a12;this[_0x4363af(0x187)]['label'][0x0][_0x4363af(0x161)]=_0x44b6e0;});_0x558399[_0x386a12(0x15e)](_0x250737);}Promise['all'](_0x558399)[_0x386a12(0x174)](()=>_0x150c42(!![]));});}[_0x1cbeac(0x141)](){return new Promise(_0x1413aa=>{const _0x5ac973=_0x2e83,_0x3dd7b6=[],_0x5426e5=scheduler[_0x5ac973(0x167)](()=>{const _0x19fe45=_0x5ac973;this['circleInstance']=new CircleType(this[_0x19fe45(0x187)]['label'][0x0]);});_0x3dd7b6[_0x5ac973(0x15e)](_0x5426e5);const _0x13b290=scheduler['postTask'](()=>{const _0x3f64aa=_0x5ac973;this['elements'][_0x3f64aa(0x15a)][0x0][_0x3f64aa(0x16c)](_0x3f64aa(0x15f),this[_0x3f64aa(0x15a)]);});_0x3dd7b6[_0x5ac973(0x15e)](_0x13b290),Promise['all'](_0x3dd7b6)[_0x5ac973(0x174)](()=>_0x1413aa(!![]));});}[_0x1cbeac(0x145)](){return new Promise(_0x138cb=>{const _0x1cd1e1=_0x2e83,_0x42b44d=[];if(this['elements']['link'][0x0]){let _0x57dc05;const _0x356254=scheduler['postTask'](()=>{const _0x365517=_0x2e83;_0x57dc05=this['elements'][_0x365517(0x15a)][0x0]['offsetHeight'],this[_0x365517(0x187)][_0x365517(0x168)][0x0][_0x365517(0x16a)]['width']=_0x57dc05+'px',this['elements'][_0x365517(0x168)][0x0][_0x365517(0x16a)][_0x365517(0x14e)]=_0x57dc05+'px';});_0x42b44d[_0x1cd1e1(0x15e)](_0x356254);}if(this[_0x1cd1e1(0x13b)]){const _0x42176d=scheduler[_0x1cd1e1(0x167)](()=>{const _0x545d47=_0x1cd1e1;this['stScrub'][_0x545d47(0x13a)]();});_0x42b44d[_0x1cd1e1(0x15e)](_0x42176d);}Promise['all'](_0x42b44d)[_0x1cd1e1(0x174)](()=>_0x138cb(!![]));});}['_attachEvents'](){const _0x42a37f=_0x1cbeac;this[_0x42a37f(0x162)]=new ResizeObserver(this[_0x42a37f(0x175)][_0x42a37f(0x182)](this)),this[_0x42a37f(0x162)][_0x42a37f(0x144)](this['elements'][_0x42a37f(0x15a)][0x0]),this[_0x42a37f(0x162)][_0x42a37f(0x144)](app['elements'][_0x42a37f(0x166)]);}[_0x1cbeac(0x175)](){const _0x466fa6=_0x1cbeac;this[_0x466fa6(0x145)]()[_0x466fa6(0x174)](()=>{const _0x25ae4a=_0x466fa6;this[_0x25ae4a(0x17a)][_0x25ae4a(0x13a)]();});}[_0x1cbeac(0x150)](){const _0x2c830=_0x1cbeac;this[_0x2c830(0x162)]&&typeof this[_0x2c830(0x162)]['disconnect']==='function'&&(this[_0x2c830(0x162)]['disconnect'](),this[_0x2c830(0x162)]=null);}}function _0x3d3e(){const _0x1eadd2=['_clamp','label','loopRounds','then','_addDelimiter','push','aria-label','center','innerHTML','resizeInstance','clear','play','5028669nAsqYR','container','postTask','link','4179654TsbKbt','style','_loopLabel','setAttribute','getComponentByName','displayInit','90TeJhwa','offsetParent','options','power3.out','clamp','finally','_onUpdate','timeline','function','isActive','rotateAnimation','circleInstance','attachResponsiveResize','.js-rotating-button__label','7637024YJcwaA','AJAX','_onDisplayInit','.js-rotating-button__icon','195080dFgTns','bind','trigger','delimiter','_initAnimations','number','elements','timeScale','getRevealAnimation','end','83841LARBRQ','start','velocity','refresh','stScrub','onScrollSpeed','5424006esXgPA','velocityWatcher','getVelocity','init','_createCircle','setup','.js-rotating-button__link','observe','_setSize','none','fonts','_attachEvents','icon','originalLabel','utilities','_doInit','getScrubAnimation','height','dataReady','_detachEvents','getById','scheduleLateTask','element','_handlers','running','571548PiYTTY','4wXExPV','all'];_0x3d3e=function(){return _0x1eadd2;};return _0x3d3e();}