<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$ajax_spinner_desktop_enabled = Utilities::get_kit_settings( 'ajax_spinner_desktop_enabled', false );
$ajax_spinner_mobile_enabled  = Utilities::get_kit_settings( 'ajax_spinner_mobile_enabled', true );

$spinner_attributes = array(
	'class'   => array( 'spinner' ),
	'id'      => 'loading-indicator',
	'viewBox' => '25 25 50 50',
	'xmlns'   => 'http://www.w3.org/2000/svg',
);

if ( $ajax_spinner_desktop_enabled ) {
	$spinner_attributes['class'][] = 'd-lg-block';
} else {
	$spinner_attributes['class'][] = 'd-lg-none';
}

if ( $ajax_spinner_mobile_enabled ) {
	$spinner_attributes['class'][] = 'd-block';
} else {
	$spinner_attributes['class'][] = 'd-none';
}

?>

<svg <?php Utilities::print_attributes( $spinner_attributes ); ?>><circle cx="50" cy="50" r="24" fill="none"></circle></svg>
