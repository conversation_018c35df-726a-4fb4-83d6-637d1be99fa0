<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

add_action( 'wp_ajax_woocommerce_arts_add_to_cart_grouped', 'arts_ajax_add_to_cart_grouped' );
add_action( 'wp_ajax_nopriv_woocommerce_arts_add_to_cart_grouped', 'arts_ajax_add_to_cart_grouped' );
if ( ! function_exists( 'arts_ajax_add_to_cart_grouped' ) ) {
	function arts_ajax_add_to_cart_grouped() {
		// phpcs:disable WordPress.Security.NonceVerification.Missing
		if ( ! isset( $_POST['quantity'] ) || ! is_array( $_POST['quantity'] ) || ! isset( $_POST['product_id'] ) ) {
			return;
		}

		$grouped_product_id = absint( $_POST['product_id'] );
		$all_added          = array();

		foreach ( $_POST['quantity'] as $product_id => $quantity ) {
			$product_id = absint( $product_id );
			$quantity   = absint( $quantity );

			if ( $quantity >= 1 ) {
				$product_id        = apply_filters( 'woocommerce_add_to_cart_product_id', absint( $product_id ) );
				$quantity          = wc_stock_amount( wp_unslash( $quantity ) );
				$passed_validation = apply_filters( 'woocommerce_add_to_cart_validation', true, $product_id, $quantity );
				$product_status    = get_post_status( $product_id );
				$variation_id      = 0;
				$variation         = array();

				if ( $passed_validation && false !== WC()->cart->add_to_cart( $product_id, $quantity, $variation_id, $variation ) && 'publish' === $product_status ) {
					$all_added[] = true;
				} else {
					$all_added[] = false;
				}
			}
		}

		if ( ! empty( $all_added ) ) {
			// If there was an error adding to the cart, redirect to the product page to show any errors.
			if ( in_array( false, $all_added ) ) {
				$data = array(
					'error'       => true,
					'product_url' => apply_filters( 'woocommerce_cart_redirect_after_error', get_permalink( $grouped_product_id ), $grouped_product_id ),
				);

				wp_send_json( $data );
			} else {
				do_action( 'woocommerce_ajax_added_to_cart', $product_id );
			}

			if ( 'yes' === get_option( 'woocommerce_cart_redirect_after_add' ) ) {
				wc_add_to_cart_message( array( $grouped_product_id => 1 ), true );
			}
		}

		WC_AJAX::get_refreshed_fragments();
		wp_die();

		// phpcs:enable
	}
}
