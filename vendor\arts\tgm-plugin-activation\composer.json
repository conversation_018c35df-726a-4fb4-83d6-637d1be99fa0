{"name": "arts/tgm-plugin-activation", "type": "library", "version": "2.6.1", "description": "TGM Plugin Activation is a PHP library that allows you to easily require or recommend plugins for your WordPress themes (and plugins). It allows your users to install and even automatically activate plugins in singular or bulk fashion using native WordPress classes, functions and interfaces. You can reference pre-packaged plugins, plugins from the WordPress Plugin Repository or even plugins hosted elsewhere on the internet.", "keywords": ["wordpress", "plugin", "activation", "tgm", "require", "recommend"], "autoload": {"files": ["src/class-tgm-plugin-activation.php"]}}