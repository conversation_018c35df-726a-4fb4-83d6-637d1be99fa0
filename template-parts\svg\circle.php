<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$defaults = array(
	'size'   => 60,
	'stroke' => 1,
	'outer'  => array(
		'class' => array( 'svg-circle' ),
	),
	'inner'  => array(
		'class' => array( 'circle' ),
		'fill'  => 'none',
	),
);
$args     = Utilities::parse_args_recursive( $args, $defaults );

$outer_attributes = Utilities::parse_args_recursive(
	$args['outer'],
	array(
		'viewBox' => "0 0 {$args['size']} {$args['size']}",
	)
);

$inner_attributes = Utilities::parse_args_recursive(
	array(
		'cx' => $args['size'] / 2,
		'cy' => $args['size'] / 2,
		'r'  => $args['size'] / 2 - $args['stroke'],
	),
	$args['inner'],
);

?>

<svg <?php Utilities::print_attributes( $outer_attributes ); ?>>
	<circle <?php Utilities::print_attributes( $inner_attributes ); ?>></circle>
</svg>
