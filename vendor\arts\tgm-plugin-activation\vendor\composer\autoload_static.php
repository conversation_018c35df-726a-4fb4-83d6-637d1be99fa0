<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit94c36adabc76bbfa091d2b2fa4ad6e6b
{
    public static $files = array (
        'bb31413de2f15b3a33244901ff2c882c' => __DIR__ . '/../..' . '/src/class-tgm-plugin-activation.php',
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->classMap = ComposerStaticInit94c36adabc76bbfa091d2b2fa4ad6e6b::$classMap;

        }, null, ClassLoader::class);
    }
}
