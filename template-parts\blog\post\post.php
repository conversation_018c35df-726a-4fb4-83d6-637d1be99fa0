<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$has_post_thumbnail = has_post_thumbnail();
$post_permalink     = get_permalink();
$thumbnail_args     = array(
	'id'           => get_post_thumbnail_id(),
	'lazy_wrapper' => array(
		'class' => array( 'hover-zoom__inner' ),
	),
	'image'        => array(
		'class' => array(
			'lazy',
			'of-contain',
			'js-parallax__inner',
			'js-ajax-transition-element__media',
		),
	),
);

$blog_posts_date_style           = 'info';
$blog_read_more_label            = esc_html__( 'Read More', 'asli' );
$blog_posts_excerpt_words_number = 55;
$post_meta_set                   = array( 'date', 'categories', 'comments', 'author' );

$post_attributes = Utilities::parse_args_recursive(
	$args,
	array(
		'id'                          => 'post-' . get_the_ID(),
		'class'                       => get_post_class( 'post', get_the_ID() ),
		'data-hover-group-class'      => 'hover-zoom-hovered',
		'data-arts-os-animation-name' => 'animatedJumpScale',
		'data-post-id'                => get_the_ID(),
	)
);

$post_header_attributes = array(
	'class' => array( 'post__header', 'content-width-3' ),
);

$link_heading_attributes = array(
	'class' => array( 'post__link', 'd-block' ),
	'href'  => $post_permalink,
);

$link_media_attributes = array(
	'class' => array(
		'post__media',
		'post__link',
		'd-block',
		'hover-zoom',
		'js-parallax',
	),
	'href'  => $post_permalink,
);

$heading_attributes = array(
	'class' => array(
		'my-0',
		'transition-color',
		'h4',
	),
);

$button_args = array(
	'title'       => $blog_read_more_label,
	'title_hover' => $blog_read_more_label,
	'icon'        => array(
		'after' => 'material-icons keyboard_arrow_right',
	),
	'attributes'  => array(
		'class' => array( 'button_bordered' ),
		'href'  => $post_permalink,
	),
);

$meta_args = array(
	'attributes'    => array(
		'class' => array( 'mb-2' ),
	),
	'set'           => $post_meta_set,
	'hasTransition' => false,
);

if ( $has_post_thumbnail ) {
	$post_header_attributes['class'][] = 'mt-4';
}

?>

<div <?php Utilities::print_attributes( $post_attributes ); ?>>
	<?php if ( $has_post_thumbnail ) : ?>
		<!-- Featured image -->
		<a <?php Utilities::print_attributes( $link_media_attributes ); ?>>
			<div class="overflow-hidden w-100 h-100 js-ajax-transition-element">
				<div class="overflow-hidden hover-zoom__zoom w-100 h-100">
					<?php get_template_part( 'template-parts/lazy/lazy', 'media', $thumbnail_args ); ?>
				</div>
			</div>
		</a>
		<!-- - Featured image -->
	<?php endif; ?>

	<!-- Heading & meta -->
	<div <?php Utilities::print_attributes( $post_header_attributes ); ?>>
		<!-- Meta -->
		<?php get_template_part( 'template-parts/blog/post/partials/meta', '', $meta_args ); ?>
		<!-- - Meta -->
		<!-- Heading -->
		<a <?php Utilities::print_attributes( $link_heading_attributes ); ?>>
			<h2 <?php Utilities::print_attributes( $heading_attributes ); ?>><?php the_title(); ?></h2>
		</a>
		<!-- - Heading -->
	</div>
	<!-- - Heading & meta -->

	<?php if ( $blog_posts_excerpt_words_number > 0 ) : ?>
		<!-- Excerpt -->
		<div class="post__excerpt mt-2 content">
			<p class="paragraph"><?php get_template_part( 'template-parts/blog/post/content/content', get_post_format() ); ?></p>
		</div>
		<!-- - Excerpt -->
	<?php endif; ?>

	<!-- Read more button -->
	<div class="post__wrapper-button mt-4 mb-1">
		<?php get_template_part( 'template-parts/button/button', 'normal', $button_args ); ?>
	</div>
	<!-- - Read more button -->
</div>
