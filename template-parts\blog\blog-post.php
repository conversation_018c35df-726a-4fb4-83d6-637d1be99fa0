<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$is_active_sidebar = is_active_sidebar( 'blog-sidebar' );

$post_outer_attributes = array(
	'class' => array(
		'post-wrapper',
		'container-fluid-gutters',
		'pb-medium',
		'max-lg-px-0',
	),
);

$container_attributes = array(
	'class' => array(
		'post-wrapper__container',
		'container',
	),
);

$row_attributes = array(
	'class' => array(
		'row',
		'section-blog__row',
	),
);

$post_inner_attributes = array(
	'class' => array(
		'post-wrapper__inner',
		'pt-small',
	),
);

if ( $is_active_sidebar ) {
	$row_attributes['class'][] = 'justify-content-between';
} else {
	$row_attributes['class'][] = 'justify-content-center';
}

$col_posts_attributes = array(
	'class' => array(
		'section-blog__posts',
		'col-12',
		'col-lg-8',
		'order-1',
		'content-width-3',
		'order-lg-1',
	),
);

$col_sidebar_attributes = array(
	'class' => array(
		'section-blog__sidebar',
		'col-12',
		'col-lg-3',
		'order-2',
		'mt-medium',
		'mt-lg-0',
		'order-lg-2',
	),
);

?>

<div <?php Utilities::print_attributes( $post_outer_attributes ); ?>>
	<div <?php Utilities::print_attributes( $post_inner_attributes ); ?>>
		<div <?php Utilities::print_attributes( $container_attributes ); ?>>
			<div <?php Utilities::print_attributes( $row_attributes ); ?>>
				<div <?php Utilities::print_attributes( $col_posts_attributes ); ?>>
					<?php the_post(); ?>
					<!-- Single post -->
					<?php get_template_part( 'template-parts/blog/post/post', 'single' ); ?>
					<!-- - Single post -->
				</div>
				<?php if ( $is_active_sidebar ) : ?>
					<!-- Sidebar -->
					<div <?php Utilities::print_attributes( $col_sidebar_attributes ); ?>><?php get_sidebar(); ?></div>
					<!-- - Sidebar -->
				<?php endif; ?>
			</div>
		</div>
	</div>
</div>
