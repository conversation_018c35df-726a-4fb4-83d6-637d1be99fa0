{"version": 3, "sources": ["../../../ts/index.ts", "../../../ts/core/app.ts"], "sourcesContent": ["import { ArtsNoticeManager } from './core/app'\n\n// Initialize the notice manager\nconst noticeManager = new ArtsNoticeManager()\n\n// Export for testing and external use\nexport default noticeManager\n", "import { NoticeManagerOptions } from './interfaces'\n\n/**\n * NoticeManager - Handles the lifecycle and interactions of WordPress admin notices\n */\nexport class ArtsNoticeManager {\n  private options: NoticeManagerOptions\n  private boundClickHandler: ((event: MouseEvent) => void) | null = null\n  private processingUrls: Set<string> = new Set()\n  private isInitialized: boolean = false\n  // Allow fetch to be injected for testing\n  private fetchImplementation: typeof fetch\n\n  /**\n   * Create a new NoticeManager instance\n   */\n  constructor(\n    options?: Partial<NoticeManagerOptions>,\n    dependencies?: { fetch?: typeof fetch; autoInit?: boolean }\n  ) {\n    this.options = {\n      dismissButtonSelector: '.notice-dismiss',\n      noticeSelector: '.notice.is-dismissible',\n      dismissUrlAttribute: 'data-dismiss-url',\n      ...options\n    }\n\n    // Ensure fetch maintains its proper context\n    this.fetchImplementation = dependencies?.fetch\n      ? dependencies.fetch.bind(window)\n      : fetch.bind(window)\n\n    // Allow delayed initialization for testing\n    if (dependencies?.autoInit !== false) {\n      this.init()\n    }\n  }\n\n  /**\n   * Initialize event listeners\n   */\n  public init(): void {\n    if (this.isInitialized) {\n      this.cleanup() // Ensure we clean up any existing handlers first\n    }\n\n    this.boundClickHandler = this.handleDismissClick.bind(this)\n    document.addEventListener('click', this.boundClickHandler)\n    this.isInitialized = true\n  }\n\n  /**\n   * Clean up event listeners and resources\n   */\n  public cleanup(): void {\n    if (this.boundClickHandler) {\n      document.removeEventListener('click', this.boundClickHandler)\n      this.boundClickHandler = null\n    }\n    this.processingUrls.clear()\n    this.isInitialized = false\n  }\n\n  /**\n   * Handle dismiss button clicks\n   */\n  private handleDismissClick(event: MouseEvent): void {\n    const target = event.target as HTMLElement\n    if (!target) {\n      return\n    }\n\n    // Use matches() for better selector compatibility\n    if (!this.elementMatches(target, this.options.dismissButtonSelector)) {\n      return\n    }\n\n    const notice = target.closest<HTMLElement>(this.options.noticeSelector)\n    if (!notice) {\n      return\n    }\n\n    const dismissUrl = notice.getAttribute(this.options.dismissUrlAttribute)\n    if (!dismissUrl) {\n      return\n    }\n\n    this.processDismissalWithDeduplication(dismissUrl, notice)\n  }\n\n  /**\n   * Process dismissal with URL deduplication\n   * Extracted for better testability\n   */\n  public processDismissalWithDeduplication(dismissUrl: string, noticeElement: HTMLElement): void {\n    // Prevent processing the same URL multiple times\n    if (this.processingUrls.has(dismissUrl)) {\n      return\n    }\n\n    // Track this URL as being processed\n    this.processingUrls.add(dismissUrl)\n\n    // Process the dismissal\n    this.dismissNotice(dismissUrl, noticeElement).catch(() => {}) // Catch errors to prevent unhandled promise rejections\n  }\n\n  /**\n   * Check if an element matches a selector\n   */\n  private elementMatches(element: HTMLElement, selector: string): boolean {\n    try {\n      return element.matches(selector)\n    } catch (e) {\n      // Fallback for older browsers\n      return (\n        element.classList.contains(selector.replace('.', '')) ||\n        element.id === selector.replace('#', '') ||\n        Array.from(document.querySelectorAll(selector)).includes(element)\n      )\n    }\n  }\n\n  /**\n   * Dismiss a notice via AJAX\n   */\n  private async dismissNotice(dismissUrl: string, noticeElement: HTMLElement): Promise<void> {\n    try {\n      const response = await this.fetchImplementation(dismissUrl)\n\n      if (!response.ok) {\n        throw new Error(`Failed to dismiss notice: ${response.status}`)\n      }\n\n      await response.text()\n\n      // Use a more direct way to remove the element\n      if (noticeElement.parentNode) {\n        noticeElement.parentNode.removeChild(noticeElement)\n      }\n    } catch (error) {\n      console.error('Notice dismissal failed:', error)\n    } finally {\n      // Remove this URL from the processing set\n      this.processingUrls.delete(dismissUrl)\n    }\n  }\n\n  /**\n   * For testing only - check if a URL is currently being processed\n   */\n  public isProcessingUrl(url: string): boolean {\n    return this.processingUrls.has(url)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;;;ACKO,MAAM,oBAAN,MAAwB;AAAA;AAAA;AAAA;AAAA,IAW7B,YACE,SACA,cACA;AAbF,0BAAQ;AACR,0BAAQ,qBAA0D;AAClE,0BAAQ,kBAA8B,oBAAI,IAAI;AAC9C,0BAAQ,iBAAyB;AAEjC;AAAA,0BAAQ;AASN,WAAK,UAAU;AAAA,QACb,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,GAAG;AAAA,MACL;AAGA,WAAK,uBAAsB,6CAAc,SACrC,aAAa,MAAM,KAAK,MAAM,IAC9B,MAAM,KAAK,MAAM;AAGrB,WAAI,6CAAc,cAAa,OAAO;AACpC,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKO,OAAa;AAClB,UAAI,KAAK,eAAe;AACtB,aAAK,QAAQ;AAAA,MACf;AAEA,WAAK,oBAAoB,KAAK,mBAAmB,KAAK,IAAI;AAC1D,eAAS,iBAAiB,SAAS,KAAK,iBAAiB;AACzD,WAAK,gBAAgB;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA,IAKO,UAAgB;AACrB,UAAI,KAAK,mBAAmB;AAC1B,iBAAS,oBAAoB,SAAS,KAAK,iBAAiB;AAC5D,aAAK,oBAAoB;AAAA,MAC3B;AACA,WAAK,eAAe,MAAM;AAC1B,WAAK,gBAAgB;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA,IAKQ,mBAAmB,OAAyB;AAClD,YAAM,SAAS,MAAM;AACrB,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,eAAe,QAAQ,KAAK,QAAQ,qBAAqB,GAAG;AACpE;AAAA,MACF;AAEA,YAAM,SAAS,OAAO,QAAqB,KAAK,QAAQ,cAAc;AACtE,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AAEA,YAAM,aAAa,OAAO,aAAa,KAAK,QAAQ,mBAAmB;AACvE,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AAEA,WAAK,kCAAkC,YAAY,MAAM;AAAA,IAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,IAMO,kCAAkC,YAAoB,eAAkC;AAE7F,UAAI,KAAK,eAAe,IAAI,UAAU,GAAG;AACvC;AAAA,MACF;AAGA,WAAK,eAAe,IAAI,UAAU;AAGlC,WAAK,cAAc,YAAY,aAAa,EAAE,MAAM,MAAM;AAAA,MAAC,CAAC;AAAA,IAC9D;AAAA;AAAA;AAAA;AAAA,IAKQ,eAAe,SAAsB,UAA2B;AACtE,UAAI;AACF,eAAO,QAAQ,QAAQ,QAAQ;AAAA,MACjC,SAAS,GAAG;AAEV,eACE,QAAQ,UAAU,SAAS,SAAS,QAAQ,KAAK,EAAE,CAAC,KACpD,QAAQ,OAAO,SAAS,QAAQ,KAAK,EAAE,KACvC,MAAM,KAAK,SAAS,iBAAiB,QAAQ,CAAC,EAAE,SAAS,OAAO;AAAA,MAEpE;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAc,cAAc,YAAoB,eAA2C;AACzF,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,oBAAoB,UAAU;AAE1D,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,IAAI,MAAM,6BAA6B,SAAS,MAAM,EAAE;AAAA,QAChE;AAEA,cAAM,SAAS,KAAK;AAGpB,YAAI,cAAc,YAAY;AAC5B,wBAAc,WAAW,YAAY,aAAa;AAAA,QACpD;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,4BAA4B,KAAK;AAAA,MACjD,UAAE;AAEA,aAAK,eAAe,OAAO,UAAU;AAAA,MACvC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKO,gBAAgB,KAAsB;AAC3C,aAAO,KAAK,eAAe,IAAI,GAAG;AAAA,IACpC;AAAA,EACF;;;ADvJA,MAAM,gBAAgB,IAAI,kBAAkB;AAG5C,MAAO,gBAAQ;", "names": []}