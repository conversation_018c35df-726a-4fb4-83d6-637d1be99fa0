<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_parse_args_recursive' ) ) {
	/**
	 * Like wp_parse_args but supports recursivity
	 * By default converts the returned type based on the $args and $defaults
	 *
	 * @param array|object $args Array or object that contains the user-defined values.
	 * @param array|object $defaults Array, Object that serves as the defaults or string.
	 * @param boolean      $preserve_type Optional. Convert output array into object if $args or $defaults if it is. Default true.
	 * @param boolean      $preserve_integer_keys Optional. If given, integer keys will be preserved and merged instead of appended.
	 *
	 * @return array|object $output Merged user defined values with defaults.
	 * @deprecated 2.0.0 Use `Arts\Utilities\Utilities::parse_args_recursive()` method instead.
	 */
	function arts_parse_args_recursive( $args, $defaults, $preserve_type = true, $preserve_integer_keys = false ) {
		return Utilities::parse_args_recursive( $args, $defaults, $preserve_type, $preserve_integer_keys );
	}
}
