<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$defaults = array(
	'id'                   => null,
	'type'                 => 'image',
	'size'                 => 'full',
	'video'                => array(
		'class'      => array(
			'of-cover',
			'of-cover-absolute',
		),
		'url'        => null,
		'src'        => null,
		'poster'     => false,
		'attributes' => array(
			'autoplay'    => '',
			'loop'        => '',
			'muted'       => '',
			'playsinline' => '',
		),
	),
	'image'                => array(),
	'lazy_wrapper'         => array(),
	'overlay'              => array(),
	'has_lazy_wrapper'     => true,
	'add_background_class' => true,
);

$args = Utilities::parse_args_recursive( $args, $defaults );

if ( is_array( $args['id'] ) && array_key_exists( 'id', $args['id'] ) ) {
	$args['id'] = $args['id']['id'];
}

if ( ! $args['id'] ) {
	return;
}

$image_attributes            = arts_get_lazy_image_attributes( $args );
$video_attributes            = array();
$seo_noscript_images_enabled = Utilities::get_kit_settings( 'seo_noscript_images_enabled', true );
$noscript_image_attributes   = array();

if ( $seo_noscript_images_enabled && array_key_exists( 'image', $image_attributes ) ) {
	$map_attributes            = array(
		'data-src'         => 'src',
		'data-texture-src' => 'src',
		'data-srcset'      => 'srcset',
		'data-sizes'       => 'sizes',
	);
	$noscript_image_attributes = $image_attributes['image'];

	foreach ( $map_attributes as $key => $value ) {
		if ( array_key_exists( $key, $image_attributes['image'] ) ) {
			$noscript_image_attributes[ $value ] = $image_attributes['image'][ $key ];
			unset( $noscript_image_attributes[ $key ] );
		}

		if ( ! array_key_exists( 'srcset', $noscript_image_attributes ) && array_key_exists( 'sizes', $noscript_image_attributes ) ) {
			unset( $noscript_image_attributes['sizes'] );
		}
	}
}

if ( is_array( $args['video'] ) && ( $args['video']['src'] || $args['video']['url'] ) ) {
	$video_attributes['src'] = $args['video']['src'] = $args['video']['url'];

	if ( $args['video']['poster'] ) {
		if ( $args['video']['poster'] && array_key_exists( 'data-src', $image_attributes['image'] ) ) {
			$video_attributes['poster'] = $image_attributes['image']['data-src'];
		}

		if ( $args['video']['poster'] && array_key_exists( 'data-texture-src', $image_attributes['image'] ) ) {
			$video_attributes['poster'] = $image_attributes['image']['data-texture-src'];
		}
	}

	if ( array_key_exists( 'class', $image_attributes['image'] ) ) {
		$video_attributes['class'] = $image_attributes['image']['class'];
	}

	if ( array_key_exists( 'class', $args['video'] ) && $args['video']['class'] ) {
		$video_attributes['class'] = array_merge( $video_attributes['class'], $args['video']['class'] );
	}

	if ( array_key_exists( 'attributes', $args['video'] ) && is_array( $args['video']['attributes'] ) ) {
		$video_attributes = array_merge( $video_attributes, $args['video']['attributes'] );
	}

	if ( $args['type'] === 'image' ) {
		$video_attributes['class'][] = 'w-100';
		$video_attributes['class'][] = 'va-middle';
	}
}

?>

<?php if ( $args['has_lazy_wrapper'] && ! empty( $image_attributes['lazy_wrapper'] ) ) : ?>
	<div <?php Utilities::print_attributes( $image_attributes['lazy_wrapper'] ); ?>>
<?php endif; ?>
	<?php if ( ! empty( $video_attributes ) ) : ?>
		<video <?php Utilities::print_attributes( $video_attributes ); ?>></video>
	<?php endif; ?>
	<img <?php Utilities::print_attributes( $image_attributes['image'] ); ?>>
	<?php if ( $seo_noscript_images_enabled && ! empty( $noscript_image_attributes ) ) : ?>
		<noscript><img <?php Utilities::print_attributes( $noscript_image_attributes ); ?>></noscript>
	<?php endif; ?>
	<?php if ( is_array( $args['overlay'] ) && ! empty( $args['overlay'] ) ) : ?>
		<div <?php Utilities::print_attributes( $args['overlay'] ); ?>></div>
	<?php endif; ?>
<?php if ( $args['has_lazy_wrapper'] && ! empty( $image_attributes['lazy_wrapper'] ) ) : ?>
	</div>
<?php endif; ?>
