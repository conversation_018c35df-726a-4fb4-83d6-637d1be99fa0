<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$is_active_sidebar = is_active_sidebar( 'blog-sidebar' );
$has_pagination    = get_the_posts_pagination();

$container_posts_attributes = array(
	'class' => array(
		'section-blog__container',
		'py-medium',
		'container',
	),
);

$row_attributes = array(
	'class' => array(
		'row',
		'section-blog__row',
	),
);

if ( $is_active_sidebar ) {
	$row_attributes['class'][] = 'justify-content-between';
} else {
	$row_attributes['class'][] = 'justify-content-center';
}

$section_attributes = array(
	'class' => array(
		'section',
		'section-blog',
		'js-ajax-blog',
	),
);

$section_attributes = Utilities::get_component_attributes(
	$section_attributes,
	array(
		'name'         => 'Content',
		'hasAnimation' => false,
	)
);

$col_posts_attributes = array(
	'class' => array(
		'section-blog__posts',
		'col-lg-8',
		'col-12',
		'order-1',
		'order-lg-1',
	),
);

$col_sidebar_attributes = array(
	'class' => array(
		'section-blog__sidebar',
		'col-lg-3',
		'col-12',
		'order-2',
		'mt-medium',
		'mt-lg-0',
		'order-lg-2',
	),
);

$pagination_attributes = array(
	'class'                       => array(
		'section-blog__pagination',
		'mt-xsmall',
		'pt-xsmall',
		'bt-opacity',
	),
	'data-arts-os-animation-name' => 'animatedJump',
);

?>

<section <?php Utilities::print_attributes( $section_attributes ); ?>>
	<div <?php Utilities::print_attributes( $container_posts_attributes ); ?>>
		<div <?php Utilities::print_attributes( $row_attributes ); ?>>
			<!-- Posts list -->
			<div <?php Utilities::print_attributes( $col_posts_attributes ); ?>>
				<?php if ( have_posts() ) : ?>
					<!-- Posts loop -->
					<?php get_template_part( 'template-parts/blog/loop/loop', 'cards' ); ?>
					<!-- - Posts loop -->
				<?php else : ?>
					<!-- No published content -->
					<?php get_template_part( 'template-parts/blog/post/content/content', 'none' ); ?>
				<?php endif; ?>
				<?php if ( $has_pagination ) : ?>
					<!-- Posts pagination -->
					<div <?php Utilities::print_attributes( $pagination_attributes ); ?>>
						<?php arts_get_posts_pagination(); ?>
					</div>
					<!-- - Posts pagination -->
				<?php endif; ?>
			</div>
			<!-- - Posts list -->
			<!-- Sidebar -->
			<?php if ( $is_active_sidebar ) : ?>
				<div <?php Utilities::print_attributes( $col_sidebar_attributes ); ?>>
					<div data-arts-os-animation-name="animatedJump"><?php get_sidebar(); ?></div>
				</div>
			<?php endif; ?>
			<!-- - Sidebar -->
		</div>
	</div>
</section>
