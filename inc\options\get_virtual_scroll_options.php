<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_virtual_scroll_options' ) ) {
	/**
	 * Get virtual scroll options.
	 *
	 * @return array $options
	 */
	function arts_get_virtual_scroll_options() {
		$virtual_scrolling_easing_mouse     = Utilities::get_kit_settings( 'virtual_scrolling_easing_mouse', 0.1 );
		$virtual_scrolling_easing_touch     = Utilities::get_kit_settings( 'virtual_scrolling_easing_touch', 0.06 );
		$virtual_scrolling_speed_mouse      = Utilities::get_kit_settings( 'virtual_scrolling_speed_mouse', 1 );
		$virtual_scrolling_speed_touch      = Utilities::get_kit_settings( 'virtual_scrolling_speed_touch', 2.5 );
		$virtual_scrolling_max_delta_mouse  = Utilities::get_kit_settings( 'virtual_scrolling_max_delta_mouse', 240 );
		$virtual_scrolling_max_delta_touch  = Utilities::get_kit_settings( 'virtual_scrolling_max_delta_touch', 180 );
		$virtual_scrolling_snap_delay_mouse = Utilities::get_kit_settings( 'virtual_scrolling_snap_delay_mouse', 0.05 );
		$virtual_scrolling_snap_delay_touch = Utilities::get_kit_settings( 'virtual_scrolling_snap_delay_touch', 0.6 );

		$options = array(
			'easing'    => array(
				'mouse' => floatval( $virtual_scrolling_easing_mouse ),
				'touch' => floatval( $virtual_scrolling_easing_touch ),
			),
			'speed'     => array(
				'mouse' => floatval( $virtual_scrolling_speed_mouse ),
				'touch' => floatval( $virtual_scrolling_speed_touch ),
			),
			'maxDelta'  => array(
				'mouse' => floatval( $virtual_scrolling_max_delta_mouse ),
				'touch' => floatval( $virtual_scrolling_max_delta_touch ),
			),
			'snapDelay' => array(
				'mouse' => floatval( $virtual_scrolling_snap_delay_mouse ),
				'touch' => floatval( $virtual_scrolling_snap_delay_touch ),
			),
		);

		return $options;
	}
}

