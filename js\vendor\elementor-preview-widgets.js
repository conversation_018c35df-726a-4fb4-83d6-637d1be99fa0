'use strict';

// Elementor editor
window.addEventListener('elementor/frontend/init', onElementorInit.bind(onElementorInit), {
	once: true
});

function onElementorInit() {
	let isHeaderLoading = false;

	class SectionHandlerClass extends elementorModules.frontend.handlers.Base {
		constructor(args) {
			super(args);

			this.handlers = {
				update: () => {
					const app = getApp();

					app.loaded.finally(() => {
						app.refresher.run();
					});
				}
			};
			this.componentStorage = 'disposable';
		}

		isActive() {
			return elementorFrontend.isEditMode();
		}

		onInit(...args) {
			if (!this.isActive()) {
				return;
			}

			super.onInit(...args);

			this.setHeader();
			this.initHeader();

			this.setUnitedGalleries();
			this.initUnitedGalleries();
		}

		setHeader() {
			const themeHeaderEnabled = this.getElementSettings('theme_header_enabled');

			if (themeHeaderEnabled === 'yes') {
				const $parentWrapper = this.$element.parent();

				const options = {
					sticky: false,
					observeHeight: true,
					matchMediaAutoCloseOverlay: '(min-width: 992px)',
					timeScaleOpen: 1,
					timeScaleClose: 1.2,
				};

				if ( !!this.getElementSettings('theme_header_sticky_enabled') ) {
					Object.assign(options, {
						sticky: {
							toggleAttributes: {
								'data-arts-header-logo': 'data-arts-header-sticky-logo',
								'class': 'data-arts-header-sticky-class',
							},
							toggleReveal: !!this.getElementSettings('theme_header_sticky_toggle_reveal_enabled'),
							toggleStickyClass: 'header__bar_sticky',
							toggleRevealingClass: false,
							toggleScrollingDownClass: 'header__bar_scrolling-down',
						}
					});
				}

				this.$element.attr({
					'data-arts-header-logo': this.getElementSettings('theme_header_logo_version'),
					'data-arts-header-sticky-logo': this.getElementSettings('theme_header_logo_version_sticky'),
				});

				this.$element.addClass([
					'header__bar',
					'js-header__bar',
					'header__bar_fixed'
				]);

				if (this.getElementSettings('theme_header_sticky_enabled')) {
					this.$element.removeClass('header__bar_absolute');
					this.$element.addClass('header__bar_fixed');
				} else {
					this.$element.removeClass('header__bar_fixed');
					this.$element.addClass('header__bar_absolute');
				}

				if ($parentWrapper.length && $parentWrapper.attr('id') === 'page-header') {
					this.$headerElement = $parentWrapper;
				} else {
					this.$headerElement = jQuery(this.$element.wrap(`<header class="header js-header" id="page-header" data-arts-component-name="Header" data-arts-component-options='${JSON.stringify(options)}'></header>`));
				}
			} else {
				this.$element.unwrap('#page-header');
				this.$headerElement = undefined;

				this.$element.removeClass([
					'header__bar',
					'js-header__bar',
					'header__bar_fixed',
					'header__bar_absolute'
				]);
				this.$element.removeAttr('data-arts-header-logo');
				this.$element.removeAttr('data-arts-header-sticky-logo');
				this.$element.removeAttr('style');
				this.componentStorage = 'disposable';
			}
		}

		initHeader() {
			const themeHeaderEnabled = this.getElementSettings('theme_header_enabled');

			if (themeHeaderEnabled === 'yes') {

				if (!isHeaderLoading) {
					const app = getApp();
					isHeaderLoading = true;

					app.loadHeader().finally(() => {
						const headerRef = app.componentsManager.getComponentByName('Header');

						this.componentStorage = 'persistent';

						if (headerRef && typeof headerRef.destroy === 'function') {
							if (document.body.contains(headerRef.element)) {
								// Destroy component when widget is removed or refreshed
								Object.assign(this, {
									onDestroy: this.destroy.bind(this, headerRef)
								});

								elementor.hooks.addAction('editor/widgets/loop-grid/on-init', this.destroy.bind(this, headerRef));
							} else {
								// By this moment the element doesn't exist in DOM anymore
								// for some reason (e.g. super-quick adding/removal action)
								// so call destroy() immediately
								this.componentStorage = 'disposable';
								this.destroy(headerRef);
							}
						}
						app.componentsManager.updateRef('headerRef', 'Header', app.componentsManager.instances.disposable);

						this.handlers.update();
						isHeaderLoading = false;
					});
				}
			} else {
				this.onDestroy();
			}
		}

		setUnitedGalleries() {
			const unitedInnerGalleriesEnabled = this.getElementSettings('asli_unite_inner_galleries_enabled');

			if (unitedInnerGalleriesEnabled === 'yes') {
				this.$element.attr('data-arts-component-name', 'PSWP');
			} else {
				this.$element.removeAttr('data-arts-component-name');
			}
		}

		initUnitedGalleries() {
			const unitedInnerGalleriesEnabled = this.getElementSettings('asli_unite_inner_galleries_enabled');

			if (unitedInnerGalleriesEnabled === 'yes') {
				this.loadComponent().then((instances) => {
					const instance = instances && instances[0];

					if (instance && typeof instance.destroy === 'function') {
						if (document.body.contains(instance.element)) {
							// Destroy component when widget is removed or refreshed
							Object.assign(this, {
								onDestroy: this.destroy.bind(this, instance)
							});

							elementor.hooks.addAction('editor/widgets/loop-grid/on-init', this.destroy.bind(this, instance));
						} else {
							// By this moment the element doesn't exist in DOM anymore
							// for some reason (e.g. super-quick adding/removal action)
							// so call destroy() immediately
							this.destroy(instance);
						}
					}

					this.handlers.update();
				}).catch((e) => {
					console.error('Failed to load component');
					console.error(e);
					this.handlers.update();
				});
			} else {
				this.onDestroy();
			}
		}

		loadComponent({ $el, loadInnerComponents, storage, parent, name } = {
			$el: this.$element,
			loadInnerComponents: false,
			storage: getApp().componentsManager.instances.disposable,
			parent: undefined,
			name: undefined
		}) {
			const app = getApp();
			const el = $el.get(0);

			return app.componentsManager.loadComponent({
				el,
				loadInnerComponents,
				parent,
				storage,
				name,
			});
		}

		// Will re-assign this method later after the component is loaded
		onDestroy() {

		}

		destroy(instance) {
			if (instance && instance.element) {
				const app = getApp();

				app.componentsManager.disposeComponent(instance.element, this.componentStorage);
				app.componentsManager.updateRef('headerRef', 'Header', app.componentsManager.instances.persistent);
			}
		}
	}

	class AsliWidgetHandler extends elementorModules.frontend.handlers.Base {
		constructor(args) {
			super(args);

			this.handlers = {
				update: () => {
					const app = getApp();

					app.loaded.finally(() => {
						app.refresher.run();
					});
				}
			};
		}

		onInit() {
			this.addLightboxComponent();

			if (!this.isWidgetInsideHeader()) {
				this.loadComponent().then((instances) => {
					const instance = instances && instances[0];

					if (instance && typeof instance.destroy === 'function') {
						if (document.body.contains(instance.element)) {
							// Destroy component when widget is removed or refreshed
							Object.assign(this, {
								onDestroy: this.destroy.bind(this, instance)
							});
							elementor.hooks.addAction('editor/widgets/loop-grid/on-init', this.destroy.bind(this, instance));
						} else {
							// By this moment the element doesn't exist in DOM anymore
							// for some reason (e.g. super-quick adding/removal action)
							// so call destroy() immediately
							this.destroy(instance);
						}
					}
					this.handlers.update();
				}).catch((e) => {
					console.error('Failed to load component');
					console.error(e);
				});
			} else {
				if (!isHeaderLoading) {
					const app = getApp();
					isHeaderLoading = true;

					app.loadHeader().finally(() => {
						app.componentsManager.updateRef('headerRef', 'Header', app.componentsManager.instances.persistent);

						isHeaderLoading = false;
					});
				}
			}
		}

		addLightboxComponent() {
			if (this.hasWidgetLightbox()) {
				this.$element.attr('data-arts-component-name', 'PSWP');
			} else if (this.$element.attr('data-arts-component-name') === 'PSWP') {
				this.$element.removeAttr('data-arts-component-name');
			}
		}

		hasWidgetLightbox() {
			const
				scope = this.$element.get(0),
				setting = this.getElementSettings('links_mode');

			return setting === 'lightbox' && !scope.querySelector('[data-arts-component-name="PSWP"]');
		}

		isWidgetInsideHeader() {
			return !!this.$element.get(0).closest('#page-header');
		}

		loadComponent() {
			const app = getApp();
			const el = this.$element.get(0);
			const scope = this.hasWidgetLightbox() ? el.parentElement : el;

			return Promise.all(app.componentsManager.init({
				scope,
				loadOnlyFirst: true
			}));
		}

		// Will re-assign this method later after the component is loaded
		onDestroy() {
			if (this.isWidgetInsideHeader()) {
				const app = getApp();
				const headerRef = app.componentsManager.getComponentByName('Header');

				if (headerRef) {
					app.componentsManager.disposeComponent(headerRef.element, 'persistent');
				}

				if (!isHeaderLoading) {
					isHeaderLoading = true;

					app.loadHeader().finally(() => {
						app.componentsManager.updateRef('headerRef', 'Header', app.componentsManager.instances.persistent);
						isHeaderLoading = false;
					});
				}
			}
		}

		destroy(instance) {
			if (instance && instance.element) {
				const app = getApp();

				app.componentsManager.disposeComponent(instance.element);
				this.handlers.update();
			}
		}
	}

	const infiniteListSkins = [
		'vertical-images-hover',
		'vertical-layout-left-right',
		'vertical-static-counter',
		'vertical-headings-left-images-right',
		'vertical-headings-right-images-left',
		'vertical-interactive-links-double-lanes',
		'vertical-interactive-links-triple-lanes',
		'vertical-interactive-links-linked-headings-images',
		'horizontal-headings-top-images-bottom',
		'horizontal-headings-over-images',
		'horizontal-single-lane-centered',
		'horizontal-double-lanes-hover',
	];

	const backgroundsSliderSkins = [
		'regular',
	];

	const splitScreenSliderSkins = [
		'centered',
		'split'
	];

	const interactiveHeadingsSkins = [
		'horizontal-headings',
		'static-headings',
		'vertical-headings'
	];

	const interactiveHeadingsScrollableSkins = [
		'regular'
	];

	const horizontalScrollingSkins = [
		'regular'
	];

	const postsNavigationSkins = [
		'auto-scroll-next',
		'prev-next'
	];

	const screensWallSkins = [
		'regular'
	];

	const fixedWallImagesSkins = [
		'regular'
	];

	const servicesSkins = [
		'full-width-cards',
		'layout-left-right',
		'pricing-tables',
	];

	const blogPostsSkins = [
		'list',
		'grid',
		'cards'
	];

	const mastheadSkins = [
		'marquee',
		'marquee-fullscreen',
		'marquee-fullscreen-mask',
		'fullscreen',
		'halfscreen',
		'fullscreen-custom-content-1',
		'fullscreen-custom-content-2',
		'fullscreen-custom-content-3',
		'fullscreen-custom-content-4',
	];

	const masonryGridSkins = [
		'no-filter',
		'filter-top',
		'filter-sidebar',
	];

	const buttonSkins = [
		'circle',
		'header',
		'regular',
	];

	const leftRightSkins = [
		'regular',
		'marquee'
	];

	// Content Widgets
	elementorFrontend.elementsHandler.attachHandler('asli-widget-crossing-lanes', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-marquee-header', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-pricing-table', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-team-member', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-testimonials-slider', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-text-content', AsliWidgetHandler);

	// Fullscreen Widgets
	infiniteListSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-infinite-list', AsliWidgetHandler, skin));
	backgroundsSliderSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-backgrounds-slider', AsliWidgetHandler, skin));
	splitScreenSliderSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-split-screen-slider', AsliWidgetHandler, skin));
	interactiveHeadingsSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-interactive-headings', AsliWidgetHandler, skin));
	horizontalScrollingSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-horizontal-scrolling', AsliWidgetHandler, skin));
	screensWallSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-screens-wall', AsliWidgetHandler, skin));
	fixedWallImagesSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-fixed-wall-images', AsliWidgetHandler, skin));

	// Fullwidth Widgets
	servicesSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-services', AsliWidgetHandler, skin));
	interactiveHeadingsScrollableSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-interactive-headings-scrollable', AsliWidgetHandler, skin));
	leftRightSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-left-right', AsliWidgetHandler, skin));
	masonryGridSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-masonry-grid', AsliWidgetHandler, skin));
	blogPostsSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-blog-posts', AsliWidgetHandler, skin));

	// Layout Widgets
	postsNavigationSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-posts-navigation', AsliWidgetHandler, skin));
	mastheadSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-masthead', AsliWidgetHandler, skin));
	elementorFrontend.elementsHandler.attachHandler('asli-widget-classic-menu', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-burger-menu', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-site-logo', AsliWidgetHandler);

	// Media Widgets
	elementorFrontend.elementsHandler.attachHandler('asli-widget-google-maps', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-lightbox-video', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-marquee-images', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-parallax-image', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-scrolling-images', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-slider-images', AsliWidgetHandler);

	// UI
	buttonSkins.forEach((skin) => elementorFrontend.elementsHandler.attachHandler('asli-widget-button', AsliWidgetHandler, skin));
	elementorFrontend.elementsHandler.attachHandler('asli-widget-icon-box', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-rotating-button', AsliWidgetHandler);
	elementorFrontend.elementsHandler.attachHandler('asli-widget-social-icons', AsliWidgetHandler);

	// Elementor "Section" and "Container"
	elementorFrontend.elementsHandler.attachHandler('section', SectionHandlerClass, null);
	elementorFrontend.elementsHandler.attachHandler('container', SectionHandlerClass, null);

	// elementor.once('document:loaded', onDocumentLoaded.bind(onDocumentLoaded));
}

function onDocumentLoaded() {
	const tl = gsap.timeline({
		// onComplete: () => resolve(true)
	});
	const app = getApp();
	const width = gsap.getProperty(app.elements.content, 'width');

	tl
		.to(app.elements.content, {
			width: width - 1,
			duration: 0.02
		})
		.to(app.elements.content, {
			width,
			clearProps: 'width',
			duration: 0.02
		});
	// 	});
}

function getApp() {
	return window.app ? window.app : elementor.$preview.get(0).contentWindow.app;
}
