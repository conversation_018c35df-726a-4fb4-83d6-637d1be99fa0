<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

if ( ! function_exists( 'arts_get_preloader_options' ) ) {
	/**
	 * Get the theme preloader options.
	 *
	 * @return array $options
	 */
	function arts_get_preloader_options() {
		$loading_steps_count                        = Utilities::get_kit_settings( 'preloader_animation_loading_steps', 4 );
		$preloader_animation_loading_steps          = arts_get_preloader_loading_steps( $loading_steps_count );
		$preloader_animation_timescale              = Utilities::get_kit_settings( 'preloader_animation_timescale', 1 );
		$preloader_animation_loading_rotation       = Utilities::get_kit_settings( 'preloader_animation_loading_rotation', 90 );
		$preloader_animation_loading_final_rotation = Utilities::get_kit_settings( 'preloader_animation_loading_final_rotation', 180 );
		$preloader_animation_final_delay            = Utilities::get_kit_settings( 'preloader_animation_final_delay', 0.4 );
		$preloader_animation_final_offset           = Utilities::get_kit_settings( 'preloader_animation_final_offset', 20 );

		$options = array(
			'timeScale'       => floatval( $preloader_animation_timescale ),
			'loadingRotation' => floatval( $preloader_animation_loading_rotation ),
			'loadingSteps'    => $preloader_animation_loading_steps, // safe PHP generated array of numbers
			'finalDelay'      => floatval( $preloader_animation_final_delay ),
			'finalOffset'     => floatval( $preloader_animation_final_offset ),
			'finalRotation'   => floatval( $preloader_animation_loading_final_rotation ),
			'toggleLoadClass' => 'preloader_loaded',
		);

		return $options;
	}
}
