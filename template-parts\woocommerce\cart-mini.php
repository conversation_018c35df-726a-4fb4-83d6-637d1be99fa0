<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! function_exists( 'woocommerce_mini_cart' ) ) {
	return;
}

use \Arts\Utilities\Utilities;

$product_count = WC()->cart->get_cart_contents_count();

$cart_wrapper_attributes = array(
	'class' => array(
		'woocommerce',
		'widget_shopping_cart',
	),
);

$cart_wrapper_attributes = Utilities::get_component_attributes(
	$cart_wrapper_attributes,
	array(
		'name'         => 'MiniCart',
		'hasAnimation' => false,
	)
);

$cart_icon_attributes = array(
	'class' => array(
		'mini-cart__wrapper-icon',
		'header__col',
		'header__col_cart',
		'header__col_fluid-paddings',
	),
	'href'  => wc_get_cart_url(),
);

$cart_badge_attributes = array(
	'class'              => array(
		'mini-cart__badge',
	),
	'data-product-count' => $product_count,
);

?>
<div <?php Utilities::print_attributes( $cart_wrapper_attributes ); ?>>
	<a <?php Utilities::print_attributes( $cart_icon_attributes ); ?>>
		<div class="mini-cart__wrapper-icon" data-arts-cursor-follower-delegated="true">
			<i class="material-icons mini-cart__icon shopping_basket"></i>
			<div <?php Utilities::print_attributes( $cart_badge_attributes ); ?>>
				<div class="mini-cart__counter"><?php echo esc_html( $product_count ); ?></div>
			</div>
		</div>
	</a>
	<div class="mini-cart__content">
		<div class="widget_shopping_cart_content"><?php woocommerce_mini_cart(); ?></div>
	</div>
	<!-- Border left -->
	<div class="header__border-vertical bg-bl"></div>
</div>
