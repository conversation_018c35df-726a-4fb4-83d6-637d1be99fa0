<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use \Arts\Utilities\Utilities;

$defaults              = array();
$args                  = wp_parse_args( $args, $defaults );
$has_preload_image_set = false;

$thumbnail_args = array(
	'type'         => 'image',
	'lazy_wrapper' => array(
		'class' => array(
			'preloader__image',
			'custom-aspect-ratio',
			'js-preloader__image',
		),
	),
	'image'        => array(
		'class' => array( 'lazy' ),
	),
);

if ( isset( $args['slidingImagesSize'] ) ) {
	$thumbnail_args['size'] = $args['slidingImagesSize'];
}

?>

<?php if ( $args['slidingImagesEnabled'] && ! empty( $args['slidingImages'] ) ) : ?>
	<!-- Fast-sliding images -->
	<div class="preloader__wrapper-images-outer js-preloader__wrapper-images-outer">
		<div class="preloader__wrapper-images-inner js-preloader__wrapper-images-inner">
			<?php foreach ( $args['slidingImages'] as $item ) : ?>
				<?php if ( ! empty( $item['image'] ) ) : ?>
					<?php
						$thumbnail_args['id'] = arts_get_image_id( $item['image'] );

						if ( ! $has_preload_image_set ) {
							add_filter(
								'arts/optimizer/preloads/images_map',
								function( $preload_images_map ) use ( $thumbnail_args ) {
									$preload_images_map['Preloader_LCP'] = $thumbnail_args['id'];
									return $preload_images_map;
								}
							);

							$has_preload_image_set = true;
						}

						if ( isset( $args['slidingImagesSize'] ) && $args['slidingImagesSize'] === 'custom' && isset( $args['slidingImagesCustomDimension'] ) ) {
							$group_control_prefix = 'preloader_sliding_images';
							$settings             = array(
								"{$group_control_prefix}_custom_size" => $args['slidingImagesSize'],
								"{$group_control_prefix}_custom_dimension" => $args['slidingImagesCustomDimension'],
							);

							$thumbnail_args['size'] = Utilities::get_settings_thumbnail_size( $settings, $thumbnail_args['id'], $group_control_prefix, 'custom_size' );
						}
					?>
					<div class="preloader__wrapper-image">
						<div class="preloader__image-mask js-preloader__wrapper-image">
							<?php get_template_part( 'template-parts/lazy/lazy', 'media', $thumbnail_args ); ?>
						</div>
					</div>
				<?php endif; ?>
			<?php endforeach; ?>
		</div>
	</div>
	<!-- - Fast-sliding images -->
<?php endif; ?>
